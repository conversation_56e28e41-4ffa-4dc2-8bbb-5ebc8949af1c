<script setup lang="ts">
import { message } from 'ant-design-vue';
import { ref, onMounted, watch, nextTick } from 'vue';
import { schemeApi, fileApi } from '@haierbusiness-front/apis';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

// Props 接收父组件传递的数据验证函数
const props = defineProps({
  // 验证所有数据是否完整的函数
  validateAllData: {
    type: Function,
    required: false,
  },
  // 获取导出数据的函数
  getExportData: {
    type: Function,
    required: false,
  },
  // 当前方案类型
  schemeType: {
    type: String,
    default: 'billUpload',
  },
  // 是否为只读模式
  readonly: {
    type: Boolean,
    default: false,
  },
  // 方案详情数据（查看模式使用）
  schemeDetail: {
    type: Object,
    default: () => ({}),
  },
});

const exportLoading = ref(false);
const importLoading = ref(false);
const hasImportedSettlement = ref(false); // 是否已导入结算单
const importedFileName = ref(''); // 导入的文件名
const hasExported = ref(false); // 是否已导出过结算单
const importedFilePath = ref(''); // 导入的文件路径

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';

// 数据完整性验证
const validateDataIntegrity = async (): Promise<boolean> => {
  try {
    // 如果没有验证函数或在只读模式下，直接返回true
    if (!props.validateAllData || props.readonly) {
      return true;
    }

    // 调用父组件的验证函数，复用完成提报的校验逻辑
    const isValid = await props.validateAllData();

    if (!isValid) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
};

// 导出功能
const handleExport = async () => {
  // 在只读模式下禁用导出功能
  if (props.readonly) {
    return;
  }

  exportLoading.value = true;

  try {
    // 第一步：数据完整性验证
    const isDataValid = await validateDataIntegrity();
    if (!isDataValid) {
      return;
    }

    // 第二步：获取导出数据
    if (!props.getExportData) {
      message.error('导出功能不可用！');
      return;
    }

    const exportData = await props.getExportData();
    if (!exportData) {
      message.error('获取导出数据失败！');
      return;
    }

    // 第三步：调用导出接口（downloadPost方法会自动处理文件下载）
    await schemeApi.exportExpenseConfirmation(exportData);

    message.success('费用确认导出成功！');
    hasExported.value = true; // 标记已导出
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败，请重试！');
  } finally {
    exportLoading.value = false;
  }
};

// 导入功能
const handleImport = async () => {
  // 在只读模式下禁用导入功能
  if (props.readonly) {
    return;
  }

  // 检查是否已导出
  if (!hasExported.value) {
    message.warning('请先导出结算单后再进行导入操作！');
    return;
  }

  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  // input.accept = '.xlsx,.xls'; // 移除文件类型限制

  input.onchange = async (e: any) => {
    const file = e.target.files[0];
    if (!file) return;

    importLoading.value = true;

    try {
      // 获取文件信息
      const fileName = file.name;
      // 移除文件格式验证，支持所有文件类型
      // const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
      // if (!['.xlsx', '.xls'].includes(fileExt)) {
      //   message.error('请选择Excel文件！');
      //   return;
      // }

      // 实际的文件上传逻辑
      const formData = new FormData();
      formData.append('file', file);

      try {
        // 调用文件上传API
        const response = await fileApi.upload(formData);
        const filePath = response.path ? baseUrl + response.path : ''; // 拼接完整路径

        // 导入成功
        hasImportedSettlement.value = true;
        importedFileName.value = fileName;
        importedFilePath.value = filePath;

        message.success('结算单导入成功！');
      } catch (uploadError) {
        console.error('文件上传失败:', uploadError);
        message.error('文件上传失败，请重试！');
        hasImportedSettlement.value = false;
        importedFileName.value = '';
        importedFilePath.value = '';
      }
    } catch (error) {
      message.error('导入失败，请重试！');
      hasImportedSettlement.value = false;
      importedFileName.value = '';
      importedFilePath.value = '';
    } finally {
      importLoading.value = false;
    }
  };

  input.click();
};

// 预览导入的文件
const previewVisible = ref(false);
const previewFile = ref<any>(null);

const handlePreviewImported = () => {
  if (importedFilePath.value && importedFileName.value) {
    previewFile.value = {
      url: importedFilePath.value,
      name: importedFileName.value,
    };
    previewVisible.value = true;
  }
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewFile.value = null;
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};

// 删除导入的文件
const handleRemoveImported = () => {
  // 在只读模式下禁用删除功能
  if (props.readonly) {
    return;
  }

  hasImportedSettlement.value = false;
  importedFileName.value = '';
  importedFilePath.value = '';
  message.success('文件删除成功');
};

// 从缓存初始化结算单状态
const initFromCache = (cacheData: any) => {
  if (cacheData && cacheData.balances && cacheData.balances.length > 0) {
    const balance = cacheData.balances[0];

    if (balance.balanceAttachment && balance.balanceAttachment.length > 0) {
      // 恢复导入状态
      hasImportedSettlement.value = true;

      // 处理文件路径 - 确保是完整的URL
      const attachment = balance.balanceAttachment[0];
      if (attachment && typeof attachment === 'string') {
        // 如果路径已经包含完整URL，直接使用
        if (attachment.startsWith('http')) {
          importedFilePath.value = attachment;
        } else {
          // 否则拼接基础URL
          importedFilePath.value = attachment.startsWith('/') ? baseUrl + attachment : baseUrl + '/' + attachment;
        }
      }

      // 从路径中提取文件名
      const fileName = balance.balanceAttachment[0].split('/').pop().replace(/^\d+-/, '') || '已导入的结算单';
      importedFileName.value = fileName;

      // 标记已导出（因为能导入说明已经导出过）
      hasExported.value = true;
    }
  }
};

// 从 schemeDetail 初始化结算单状态（查看模式使用）
const initFromSchemeDetail = () => {
  if (props.readonly && props.schemeDetail && props.schemeDetail.balances && props.schemeDetail.balances.length > 0) {
    const balance = props.schemeDetail.balances[0];

    if (balance.balanceAttachment && balance.balanceAttachment.length > 0) {
      // 恢复导入状态
      hasImportedSettlement.value = true;
      importedFilePath.value = balance.balanceAttachment[0];

      

      // 处理文件路径 - 确保是完整的URL
      const attachment = balance.balanceAttachment[0];
      if (attachment && typeof attachment === 'string') {
        // 如果路径已经包含完整URL，直接使用
        if (attachment.startsWith('http')) {
          importedFilePath.value = attachment;
        } else {
          // 否则拼接基础URL
          importedFilePath.value = attachment.startsWith('/') ? baseUrl + attachment : baseUrl + '/' + attachment;
        }
      }
      // 从路径中提取文件名
      const fileName = balance.balanceAttachment[0].split('/').pop().replace(/^\d+-/, '') || '已导入的结算单';
      importedFileName.value = fileName;


      // 标记已导出（因为能导入说明已经导出过）
      hasExported.value = true;

      console.log('结算单文件名:', importedFileName.value);
      console.log('结算单文件路径:', importedFilePath.value);
    }
  }
};

// 暴露给父组件的方法，检查是否已导入结算单
defineExpose({
  hasImportedSettlement: hasImportedSettlement,
  importedFileName: importedFileName,
  importedFilePath: importedFilePath,
  hasExported: hasExported,
  initFromCache: initFromCache, // 新增暴露初始化方法
  initFromSchemeDetail: initFromSchemeDetail, // 暴露查看模式初始化方法
});

// 监听props变化，确保数据更新时能正确回显
// 监听 schemeDetail 数据变化
watch(
  () => props.schemeDetail,
  (newData) => {
    if (props.readonly && newData && Object.keys(newData).length > 0) {
      nextTick(() => {
        initFromSchemeDetail();
      });
    }
  },
  { deep: true, immediate: true },
);

onMounted(() => {
  // 如果是查看模式，自动从 schemeDetail 初始化数据
  if (props.readonly) {
    // 使用 nextTick 确保组件完全挂载后再初始化
    nextTick(() => {
      initFromSchemeDetail();
    });
  }
});
</script>

<template>
  <div class="export-expense-confirmation">
    <div class="contract_title">
      <div class="interact_shu mr20"></div>
      <span>结算单上传</span>
      <a-tooltip class="ml10">
        <template #title> 导出结算单后，线下盖章，将盖完章的文件重新导入（支持所有文件类型） </template>
        <QuestionCircleOutlined style="font-size: 16px; color: #a7a5a6" />
      </a-tooltip>
    </div>
    <div class="contract_wrapper">
      <div class="export-item">
        <div class="button-group">
          <!-- 查看模式下隐藏导入导出按钮，只显示上传的文件 -->
          <template v-if="readonly">
            <div v-if="hasImportedSettlement" class="import-status readonly-mode">
              <span class="readonly-label">已上传的结算单：</span>
              <a-tag class="file-tag readonly-tag" @click="handlePreviewImported">
                {{ importedFileName }}
              </a-tag>
            </div>
            <div v-else class="no-file-status">
              <span class="no-file-text">暂无上传的结算单</span>
            </div>
          </template>
          <!-- 非查看模式显示导入导出按钮 -->
          <template v-else>
            <a-button :loading="exportLoading" @click="handleExport">导出结算单</a-button>
            <a-button :loading="importLoading" :disabled="!hasExported" @click="handleImport"> 导入结算单 </a-button>
            <div v-if="hasImportedSettlement" class="import-status">
              <span class="file-tag-wrapper">
                <a-tag class="file-tag" @click="handlePreviewImported">
                  {{ importedFileName }}
                </a-tag>
                <a-popconfirm v-if="!readonly" :title="`确认删除文件 ${importedFileName}？`" description="删除后无法恢复" ok-text="删除"
                  cancel-text="取消" @confirm="()=>handleRemoveImported()">
                  <a-button type="text" size="small" danger class="file-delete-btn">×</a-button>
                </a-popconfirm>
              </span>

            </div>
            <span v-else-if="!hasExported" class="import-hint"> 请先导出结算单 </span>
          </template>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewFile?.name }}</h4>
        </div>
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <!-- 图片预览 -->
            <img v-if="
              previewFile.name &&
              (previewFile.name.toLowerCase().includes('.jpg') ||
                previewFile.name.toLowerCase().includes('.jpeg') ||
                previewFile.name.toLowerCase().includes('.png') ||
                previewFile.name.toLowerCase().includes('.gif'))
            " :src="previewFile.url" alt="预览图片" style="max-width: 100%; max-height: 500px; object-fit: contain" />
            <!-- PDF预览 -->
            <iframe v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url" style="width: 100%; height: 500px; border: none"></iframe>
            <!-- 其他文件类型显示下载链接 -->
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile"> 下载文件 </a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFile?.name }}</p>
              <p>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
* {
  font-family: PingFangSC, PingFang SC;
}

.export-expense-confirmation {
  padding-top: 24px;

  .interact_shu {
    width: 4px;
    height: 20px;
    background: #1868db;
    border-radius: 2px;
  }

  .contract_title {
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
    // font-weight: 500;
    display: flex;
    align-items: center;
    // gap: 8px;

    span {
      font-size: 18px;
      // font-weight: 500;
      color: #1d2129;
    }
  }

  .contract_wrapper {
    border-radius: 6px;
    overflow: hidden;
    width: 70%;

    .export-item {
      display: flex;
      align-items: center;
      padding: 18px 30px;
      padding-top: 0px;
      background-color: #fff;
      transition: background-color 0.3s;

      // &:hover {
      //   background-color: #fafafa;
      // }

      .export-label {
        width: 140px;
        font-size: 14px;
        color: #333;
        margin-right: 40px;
        flex-shrink: 0;
        font-weight: 500;
      }

      .button-group {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 24px;

        :deep(.ant-btn) {
          height: 36px;
          font-size: 14px;
          padding: 0 20px;
          border-radius: 4px;

          &[type='primary'] {
            background-color: #1890ff;
            border-color: #1890ff;

            &:hover {
              background-color: #40a9ff;
              border-color: #40a9ff;
            }
          }

          &:not([type='primary']) {
            border-color: #d9d9d9;
            color: #666;

            &:hover {
              border-color: #1890ff;
              color: #1890ff;
            }
          }
        }

        .import-status {
          margin-left: 16px;
          display: flex;
          align-items: center;

          .file-tag {
            cursor: pointer;
            font-size: 12px;
            background-color: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
            padding: 4px 20px;
            margin: 2px 0;

            &:hover {
              opacity: 0.8;
              background-color: #bae7ff;
            }
          }

          &.readonly-mode {
            margin-left: 0;

            .readonly-label {
              font-size: 14px;
              color: #666;
              margin-right: 12px;
            }

            .readonly-tag {
              background-color: #f6ffed;
              border-color: #52c41a;
              color: #52c41a;

              &:hover {
                background-color: #d9f7be;
              }
            }
          }
        }

        .no-file-status {
          .no-file-text {
            font-size: 14px;
            color: #999;
            font-style: italic;
          }
        }

        .import-hint {
          margin-left: 16px;
          font-size: 14px;
          color: #999;
          font-style: italic;
        }
      }
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .file-download {
        padding: 40px 0;

        p {
          margin-bottom: 16px;
          color: #666;
        }
      }

      .no-file {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }
}

.mr20 {
  margin-right: 20px;
}

.ml10 {
  margin-left: 10px;
}
.file-tag-wrapper {
  position: relative;
  display: inline-flex;
  align-items: center;

  .file-tag {
    cursor: pointer;
    font-size: 12px;
    background-color: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
    padding-right: 20px; // 为删除按钮预留空间
    margin-right: 0;

    &:hover {
      opacity: 0.8;
      background-color: #bae7ff;
    }
  }

  .file-delete-btn {
    position: absolute;
    right: 5px;
    top: 45%;
    transform: translateY(-50%);
    width: 14px !important;
    height: 14px !important;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    line-height: 1;
    border: none;
    padding: 0 !important;
    background: transparent;
    color: #ff4d4f !important;

    &:hover {
      background-color: rgba(255, 77, 79, 0.1);
      border-radius: 2px;
    }
  }
}
</style>
