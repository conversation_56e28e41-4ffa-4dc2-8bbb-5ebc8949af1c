<script setup lang="ts">
// 会议现场照片组件
import { message } from 'ant-design-vue';
import { onMounted, ref, watch, defineProps, defineEmits, computed } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import arrow from '@/assets/image/orderList/delete.png'; //删除
import deleteGray from '@/assets/image/orderList/deleteGray.png'; //删除灰色
import add from '@/assets/image/orderList/add.png'; //添加
import { UploadFile, ConferencePhotoItem, BILL_UPLOAD_FILE_CONFIG } from '@haierbusiness-front/common-libs';
import { UploadOutlined } from '@ant-design/icons-vue';
import { ConferencePhotoTypeEnum } from '@haierbusiness-front/common-libs';

// 注意：ConferencePhotoItem 类型定义已移至 @haierbusiness-front/common-libs/micemerchant

//鼠标移入效果
const hoverColor = ref(-1)

// 文件上传相关常量（从 common-libs 导入，使用图片类型）
const { IMAGE_FILE_TYPES: SUPPORTED_IMAGE_TYPES, BASIC_FILE_SIZE_LIMIT: FILE_SIZE_LIMIT, IMAGE_UPLOAD_ACCEPT: UPLOAD_ACCEPT } = BILL_UPLOAD_FILE_CONFIG;

const props = defineProps({
  conferencePhotoList: {
    type: Array as () => ConferencePhotoItem[],
    default: () => [],
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  schemeDetail: {
    type: Object as () => any,
    default: () => ({}),
  },
});

const emit = defineEmits(['conferencePhotosEmit']);

// 响应式数据
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 用于存储每个条目的上传状态
const itemUploadingState = ref<Record<string, boolean>>({});

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 初始化标志，防止重复初始化
const isInitialized = ref(false);

// 🔥 计算属性：获取要显示的会议现场照片数据
const displayConferencePhotoList = computed(() => {
  if (props.readonly && props.schemeDetail?.attachmentPhotos) {
    const transformedData = props.schemeDetail.attachmentPhotos.map((item: any, index: number) => {
      // 🔥 修复：将 paths 转换为 photos 格式
      const photos = (item.paths || []).map((path: string, pathIndex: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;

        // 如果路径已经包含完整 URL，直接使用
        if (path.startsWith('http')) {
          processedPath = path;
        } else {
          // 如果是相对路径，拼接 baseUrl
          processedPath = path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
        }

        // 生成文件名
        const fileName = path.split('/').pop() || `会议照片${pathIndex + 1}`;

        return {
          uid: `${item.id || index}_${pathIndex}`,
          name: fileName,
          status: 'done',
          url: processedPath,
          filePath: processedPath,
        };
      });

      return {
        ...item,
        tempId: item.tempId || `view_photo_${item.id || Date.now()}_${index}`, // 添加 tempId
        serialNumber: item.serialNumber || index + 1, // 添加序号
        subType: item.subType || null, // 确保类型字段存在
        paths: item.paths || [], // 确保路径数组存在
        photos: photos, // 🔥 修复：使用转换后的 photos
      };
    });

    return transformedData;
  } else {
    return props.conferencePhotoList || [];
  }
});

// 从缓存数据初始化会议现场照片
const initConferencePhotosFromCache = (cacheData: ConferencePhotoItem[]) => {
  if (!cacheData || cacheData.length === 0) {
    return;
  }

  // 处理缓存数据，将 paths 转换为 photos
  const processedData = cacheData.map((item: any, index: number) => {
    const processedItem = { ...item };

    // 确保必要字段存在
    if (!processedItem.tempId) {
      processedItem.tempId = `cache_${Date.now()}_${index}`;
    }
    if (!processedItem.subType) {
      processedItem.subType = processedItem.subType || null;
    }

    // 处理附件数据 - 将字符串路径转换为 UploadFile 对象
    if (item.paths && Array.isArray(item.paths) && item.paths.length > 0) {
      processedItem.photos = item.paths.map((path: string, fileIndex: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;
        let fullUrl = path;

        // 如果路径已经包含完整 URL，提取相对路径
        if (path.includes(baseUrl)) {
          processedPath = path.replace(baseUrl, '');
          fullUrl = path;
        } else {
          // 如果是相对路径，确保以 / 开头
          if (!processedPath.startsWith('/')) {
            processedPath = '/' + processedPath;
          }
          fullUrl = baseUrl + processedPath;
        }

        const fileObj = {
          uid: `${processedItem.tempId}_cache_${fileIndex}`,
          name: path.split('/').pop() || `照片${fileIndex + 1}`,
          status: 'done' as const,
          url: fullUrl,
          filePath: processedPath,
          fileName: path.split('/').pop() || `照片${fileIndex + 1}`,
        };

        return fileObj;
      });
    } else {
      processedItem.photos = [];
    }

    // 确保序号正确
    processedItem.serialNumber = index + 1;

    return processedItem;
  });

  isInitialized.value = true;

  // 发射处理后的数据到父组件
  emit('conferencePhotosEmit', processedData);
};

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';
  if (fileName) if (fileName.includes('-')) fileName = fileName.split('-')[1];

  const maxLength = 15;
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';

  return `${truncatedName}.${extension}`;
};

// 文件上传前验证
const beforeUpload = (file: File): boolean => {
  const isValidType = SUPPORTED_IMAGE_TYPES.includes(file.type);

  if (!isValidType) {
    message.error('只支持上传 JPG、PNG、GIF 格式的图片！');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`图片大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return false;
  }

  return true; // 验证通过，允许上传
};

// 新增附件分类
const handleAddPhotoCategory = () => {
  const newItem: ConferencePhotoItem = {
    tempId: `${Date.now()}_${Math.random()}`,
    serialNumber: props.conferencePhotoList.length + 1,
    subType: null,
    paths: [],
    photos: [],
    _uploading: false,
  };

  const updatedList = [...props.conferencePhotoList, newItem];

  emit('conferencePhotosEmit', updatedList);
};

// 更新照片字段
const updatePhoto = (itemId: string, field: string, value: any) => {
  const updatedList = props.conferencePhotoList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, [field]: value };
    }
    return item;
  });
  emit('conferencePhotosEmit', updatedList);
};

// 照片上传处理
const handlePhotoUpload = (options: any, itemId: string) => {
  // 设置该条目的上传状态
  itemUploadingState.value[itemId] = true;

  // 更新条目的上传状态
  const updatedListForLoading = props.conferencePhotoList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, _uploading: true };
    }
    return item;
  });
  emit('conferencePhotosEmit', updatedListForLoading);

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((response) => {
      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: options.file.name,
        status: 'done',
        url: response.path ? baseUrl + response.path : '',
        filePath: response.path ? baseUrl + response.path : '',
        fileName: options.file.name,
      };

      const updatedList = props.conferencePhotoList.map((item) => {
        if (item.tempId === itemId) {
          return {
            ...item,
            photos: [...(item.photos || []), fileObj],
            paths: [...(item.paths || []), response.path ? baseUrl + response.path : ''],
          };
        }
        return item;
      });

      emit('conferencePhotosEmit', updatedList);
      message.success(`照片 ${options.file.name} 上传成功`);
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error(`照片 ${options.file.name} 上传失败，请重试`);
    })
    .finally(() => {
      // 清除该条目的上传状态
      itemUploadingState.value[itemId] = false;

      // 更新条目状态
      const finalList = props.conferencePhotoList.map((item) => {
        if (item.tempId === itemId) {
          return { ...item, _uploading: false };
        }
        return item;
      });
      emit('conferencePhotosEmit', finalList);
    });
};

// 删除照片 - 真正删除数据而不是隐藏
const handleRemovePhoto = (file: UploadFile, itemId: string) => {
  const updatedList = props.conferencePhotoList.map((item) => {
    if (item.tempId === itemId) {
      const photoIndex = item.photos.findIndex((photo) => photo.uid === file.uid);
      if (photoIndex > -1) {
        // 创建新的数组，真正删除对应索引的数据
        const newPhotos = [...item.photos];
        const newPaths = [...item.paths];

        // 从数组中移除对应索引的元素
        newPhotos.splice(photoIndex, 1);
        newPaths.splice(photoIndex, 1);

        return {
          ...item,
          photos: newPhotos,
          paths: newPaths,
        };
      }
    }
    return item;
  });

  // 立即更新到父组件，确保数据真正被删除
  emit('conferencePhotosEmit', updatedList);
  message.success('照片删除成功');
};

// 删除照片分类 - 使用 popconfirm 确认
const handleDeletePhotoCategory = (itemId: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能删除分类');
    return;
  }

  const index = props.conferencePhotoList.findIndex((item) => item.tempId === itemId);
  if (index > -1) {
    // 创建新的数组，真正删除指定条目
    const updatedList = [...props.conferencePhotoList];
    updatedList.splice(index, 1);

    // 重新排序序号
    updatedList.forEach((item, idx) => {
      item.serialNumber = idx + 1;
    });

    // 立即更新到父组件，确保数据真正被删除
    emit('conferencePhotosEmit', updatedList);
    message.success('照片分类删除成功');
  }
};

// 文件预览
const handlePreviewPhoto = (file: UploadFile) => {
  previewImage.value = file.url || file.filePath || '';
  previewVisible.value = true;
  previewTitle.value = file.name || '文件预览';
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewImage.value = '';
  previewTitle.value = '';
};

// 监听 conferencePhotoList 变化，处理缓存数据回显
watch(
  () => props.conferencePhotoList,
  (newList, oldList) => {
    if (newList && newList.length > 0) {
      // 检查是否需要处理附件回显或序号修复
      const needsProcessing = newList.some((item) => {
        const hasPathsButNoPhotos =
          item.paths &&
          item.paths.length > 0 &&
          (!item.photos || !Array.isArray(item.photos) || item.photos.length === 0);
        const hasIncorrectSerialNumber = !item.serialNumber || item.serialNumber === 0;
        const missingTempId = !item.tempId;

        return hasPathsButNoPhotos || hasIncorrectSerialNumber || missingTempId;
      });

      if (needsProcessing) {
        initConferencePhotosFromCache(newList);
      } else if (!isInitialized.value) {
        // 即使不需要处理附件，也要确保序号正确（仅在未初始化时）
        const fixedList = newList.map((item, index) => ({
          ...item,
          serialNumber: index + 1,
        }));
        emit('conferencePhotosEmit', fixedList);
        isInitialized.value = true;
      }
    }
  },
  { immediate: true, deep: true },
);

// 🔥 监听 schemeDetail 变化（仅查看模式使用）
watch(
  () => props.schemeDetail,
  (newSchemeDetail) => {
    // 只在查看模式下处理 schemeDetail
    if (props.readonly && newSchemeDetail && newSchemeDetail.attachmentPhotos) {
      // 🔥 查看模式：直接使用数据展示，通过 emit 传递给父组件进行显示
      emit('conferencePhotosEmit', newSchemeDetail.attachmentPhotos);
    }
  },
  { immediate: true, deep: true },
);
// 🔧 修复：监听会议现场照片列表变化，但不自动添加空数据
// 让用户主动点击"添加图片"按钮来添加项目，避免总是有空值存在
watch(
  () => props.conferencePhotoList,
  (newPhotoList, oldPhotoList) => {
    // 只处理数据初始化，不自动添加空项
    if (newPhotoList && newPhotoList.length > 0 && !isInitialized.value) {
      isInitialized.value = true;
    }
  },
  { immediate: true, deep: true },
);

// 校验
const conferencePhotosSub = () => {
  return true;
};

// 🔧 新增：设置初始化状态的方法，供父组件调用
const setInitialized = (value: boolean) => {
  isInitialized.value = value;
};

// 暴露给父组件的方法
defineExpose({
  conferencePhotosSub,
  setInitialized, // 🔧 新增：暴露设置初始化状态的方法
});
</script>

<template>
  <!-- 会议现场照片 -->
  <div class="scheme_conference_photos">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>会议现场照片</span>
    </div>

    <!-- 表格布局 -->
    <div class="info-table-wrapper conference-photos-table">
      <div class="table-header">
        <div class="col-serial font-color">序号</div>
        <div class="col-type font-color">类型</div>
        <div class="col-files font-color">附件</div>
        <div class="col-action font-color" v-if="!readonly">操作</div>
      </div>

      <div class="table-body">
        <div
          v-for="(item, index) in displayConferencePhotoList"
          :key="item.tempId || item.id || index"
          class="table-row"
        >
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber || index + 1 }}
          </div>

          <!-- 类型 -->
          <div class="col-type">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{ ConferencePhotoTypeEnum.getTypeText(item.subType) || item.subType || '-' }}
            </span>
            <!-- 编辑模式：显示下拉选择框 -->
            <a-select
              v-else
              :value="item.subType"
              placeholder="请选择类型"
              size="small"
              class="borderless-input"
              :bordered="false"
              allow-clear
              @change="(value: string) => updatePhoto(item.tempId, 'subType', value)"
            >
              <a-select-option
                v-for="option in ConferencePhotoTypeEnum.getTypeOptions()"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </div>

          <!-- 附件 -->
          <div class="col-files">
            <div class="files-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item.photos && item.photos.length > 0">
                <span v-for="photo in item.photos" :key="photo.uid" class="file-tag-wrapper">
                  <a-tag class="file-tag" @click="() => handlePreviewPhoto(photo)">
                    {{ getFileDisplayName(photo.name) }}
                  </a-tag>
                  <a-popconfirm
                    v-if="!readonly"
                    :title="`确认删除照片 ${photo.name}？`"
                    description="删除后无法恢复"
                    ok-text="删除"
                    cancel-text="取消"
                    @confirm="() => handleRemovePhoto(photo, item.tempId)"
                  >
                    <a-button type="text" size="small" danger class="file-delete-btn">×</a-button>
                  </a-popconfirm>
                </span>
              </div>
              <div v-else-if="readonly" class="readonly-text">暂无附件</div>

              <!-- 上传按钮 - 查看模式下隐藏 -->
              <a-upload
                v-if="!readonly"
                :file-list="[]"
                :before-upload="beforeUpload"
                :custom-request="(options: any) => handlePhotoUpload(options, item.tempId)"
                :show-upload-list="false"
                :accept="UPLOAD_ACCEPT"
                multiple
              >
                <a-button
                  size="small"
                  type="link"
                  :loading="item._uploading || itemUploadingState[item.tempId] || false"
                >
                  <upload-outlined />
                  上传附件
                </a-button>
              </a-upload>
            </div>
          </div>

          <!-- 操作 - 查看模式下隐藏 -->
          <div class="col-action" v-if="!readonly">
            <a-popconfirm
              title="确认删除该照片分类吗？"
              description="删除后将清除该分类的所有照片"
              ok-text="删除"
              cancel-text="取消"
              @confirm="() => handleDeletePhotoCategory(item.tempId)"
            >
              <a-button type="link" danger size="small">
                <img :src="hoverColor == index ?arrow : deleteGray" alt="" class="imgBig" @mouseenter="hoverColor = index"
                @mouseleave="hoverColor = -1"/>
              </a-button>
            </a-popconfirm>
          </div>
        </div>

        <!-- 添加按钮行 - 查看模式下隐藏 -->
        <div v-if="!readonly" class="table-row add-row">
          <div class="add-button-full-width" @click="handleAddPhotoCategory">
            <div class="demand_add">
              <img :src="add" alt="" class="imgAddBig" style="margin-right: 5px" />
              <span>添加图片</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewTitle }}</h4>
        </div>
        <div class="preview-body">
          <img
            v-if="previewImage"
            :src="previewImage"
            alt="照片预览"
            style="width: 100%; max-height: 600px; object-fit: contain"
          />
          <div v-else class="no-image">
            <p>暂无可预览的照片内容</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
* {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

.font-color {
  color: #86909c;
}

.imgBig {
  width: 16px;
  height: 16px;
}

.imgAddBig {
  width: 16px;
  height: 16px;
}

.scheme_conference_photos {
  position: relative;
  margin-bottom: 24px;

  .interact_title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;

    .interact_shu {
      width: 4px;
      height: 20px;
      background: #1868db;
      border-radius: 2px;
    }

    span {
      font-family: PingFangSC, PingFang SC;
      font-size: 18px;
      font-weight: 500;
      color: #1d2129;
    }

    .tip-text {
      margin-left: 16px;
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .info-table-wrapper {
    width: 100%;
    border: none;
    border-radius: 0;
    margin-bottom: 0;

    .table-header {
      display: flex;
      background-color: #f2f3f5;
      font-weight: 500;
      font-size: 14px;
      color: #333;

      > div {
        padding: 12px 8px;
        text-align: center;
      }

      .col-serial {
        width: 80px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-type {
        width: 200px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-files {
        flex: 1;
        min-width: 200px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-action {
        width: 120px;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #e5e6eb;

        &:last-child {
          border-bottom: none;
        }

        &.add-row {
          border-bottom: none;
          margin-top: 5px; // 🔧 新增：距离上边5px

          .add-button-full-width {
            width: auto; // 🔧 修改：从100%改为auto，让按钮宽度自适应内容
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-height: 38px;
            cursor: pointer;
            border: none; // 🔧 修改：去掉边框
            border-radius: 6px; // 🔧 新增：添加圆角
            background-color: transparent; // 🔧 新增：透明背景
            transition: all 0.3s ease; // 🔧 新增：添加过渡效果

            &:hover {
              background-color: #f5f5f5; // 🔧 保留：悬停背景色
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              color: #1890ff;
              font-size: 14px;
              margin-left: 8px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+')
                  no-repeat center;
                background-size: contain;
                margin-right: 8px;
              }
            }
          }
        }

        > div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 40px;
          // border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }
        }

        .col-serial {
          width: 80px;
        }

        .col-type {
          width: 200px;
        }

        .col-files {
          flex: 1;
          min-width: 180px;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 4px;

          .files-content {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
            justify-content: flex-start;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: flex-start;
              max-width: 100%;

              .file-tag-wrapper {
                position: relative;
                display: inline-flex;
                align-items: center;

                .file-tag {
                  cursor: pointer;
                  font-size: 12px;
                  background-color: #e6f7ff;
                  border-color: #1890ff;
                  color: #1890ff;
                  padding-right: 20px; // 为删除按钮预留空间
                  margin-right: 0;

                  &:hover {
                    opacity: 0.8;
                    background-color: #bae7ff;
                  }
                }

                .file-delete-btn {
                  position: absolute;
                  right: 2px;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 14px;
                  height: 14px;
                  padding: 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 12px;
                  line-height: 1;
                  border: none;
                  background: transparent;

                  &:hover {
                    background-color: rgba(255, 77, 79, 0.1);
                    border-radius: 2px;
                  }
                }
              }
            }
          }
        }

        .col-action {
          width: 120px;
          display: flex;
          justify-content: center;
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }

    .ant-picker-input > input {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .no-image {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }
}

.mr8 {
  margin-right: 8px;
}

.mr20 {
  margin-right: 20px;
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}

// 查看模式纯文本样式
.readonly-text {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  padding: 4px 0;
  display: inline-block;
  width: 100%;
  text-align: center;

  &.small {
    font-size: 12px;
    color: #999;
  }
}
</style>
