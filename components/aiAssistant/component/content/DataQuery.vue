<template>
  <div v-if="tableData && tableData.list && tableData.list.length > 0" class="data-query-container">
    <!-- 卡片容器 -->
    <a-card class="data-card">
      <!-- 全屏按钮 -->
      <template #title>
        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
          <span>数据查询结果</span>

          <div class="card-header-actions">
            <a-button size="small" @click="exportToExcel">
              <DownloadOutlined />
              导出Excel
            </a-button>
            <a-button size="small" @click="copyTableData">
              <CopyOutlined />
              复制数据
            </a-button>
            <a-button
              :title="isFullscreenMode ? '退出全屏' : '全屏查看'"
              size="small"
              type="text"
              @click="toggleFullscreen"
            >
              <template #icon>
                <FullscreenOutlined v-if="!isFullscreenMode" />
                <FullscreenExitOutlined v-else />
              </template>
            </a-button>
          </div>
        </div>
      </template>

      <!-- 表格区域 -->
      <div class="table-scroll-container">
        <a-table
          :columns="columns"
          :data-source="tableData.list"
          :pagination="paginationConfig"
          :scroll="{ x: 'max-content' }"
          bordered
          size="middle"
        >
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key === 'action'">
              <a-button size="small" type="link" @click="showDetail(record)">
                <EyeOutlined />
              </a-button>
            </template>
            <template v-else>
              <a-tooltip :title="text" placement="topLeft">
                <div class="table-cell">{{ text }}</div>
              </a-tooltip>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 真实全屏展示区域 -->
    <div v-if="isFullscreenMode" ref="fullscreenContainerRef" class="true-fullscreen-container">
      <!-- 全屏标题 -->
      <div class="browser-fullscreen-header">
        <div style="display: flex; justify-content: space-between; align-items: center">
          <span class="title-container"> 数据查询结果 </span>

          <!-- 移动按钮到这里 -->
          <div class="fullscreen-top-actions">
            <a-button size="small" @click="exportToExcel">
              <DownloadOutlined />
              导出Excel
            </a-button>
            <a-button size="small" @click="copyTableData">
              <CopyOutlined />
              复制数据
            </a-button>
            <a-button size="small" type="text" @click="exitFullscreen">
              <template #icon>
                <FullscreenExitOutlined />
              </template>
              <span>退出全屏</span>
            </a-button>
          </div>
        </div>
      </div>

      <div class="browser-fullscreen-content">
        <a-table
          :columns="columns"
          :data-source="tableData.list"
          :pagination="paginationConfig"
          :scroll="{ x: 'max-content' }"
          bordered
          size="middle"
        >
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key === 'action'">
              <a-button size="small" type="link" @click="showDetail(record)">
                <EyeOutlined />
              </a-button>
            </template>
            <template v-else>
              <a-tooltip :title="text" placement="topLeft">
                <div class="table-cell">{{ text }}</div>
              </a-tooltip>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 数据详情模态框 -->
    <a-modal v-model:open="detailModalVisible" :footer="null" :width="modalWidth" :z-index="2000" title="数据详情">
      <a-descriptions v-if="currentDetailRecord" :column="2" bordered>
        <a-descriptions-item v-for="(value, key) in currentDetailRecord" :key="key" :label="formatColumnName(key)">
          {{ value }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import {
  CopyOutlined,
  DownloadOutlined,
  EyeOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import * as XLSX from 'xlsx'

const props = withDefaults(
  defineProps<{
    tableData?: Object | null
    isFullscreen?: boolean
    sqlContentId?: number | null
  }>(),
  {
    tableData: () => null,
    isFullscreen: false,
    sqlContentId: null,
  },
)

const emit = defineEmits(['pageChange'])

// 全屏状态控制
const isFullscreenMode = ref(false)
const fullscreenContainerRef = ref<HTMLElement | null>(null)

const toggleFullscreen = () => {
  if (isFullscreenMode.value) {
    exitFullscreen()
  } else {
    enterFullscreen()
  }
}

const enterFullscreen = async () => {
  const elem = fullscreenContainerRef.value
  if (elem && elem.requestFullscreen) {
    await elem.requestFullscreen()
  }
  isFullscreenMode.value = true
}

const exitFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen().catch((err) => console.error('退出全屏失败:', err))
  }
  isFullscreenMode.value = false
}

const handleFullscreenChange = () => {
  isFullscreenMode.value = !!document.fullscreenElement
}

onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})

// 详情模态框相关
const modalWidth = computed(() => (props.isFullscreen ? '90vw' : '80%'))
const detailModalVisible = ref(false)
const currentDetailRecord = ref<any>(null)

const formatColumnName = (key: string) => {
  return key
    .replace(/_/g, ' ')
    .replace(/([A-Z])/g, ' $1')
    .toUpperCase()
}

// 分页配置
const paginationConfig = computed(() => ({
  current: props.tableData?.pageNum || 1,
  pageSize: props.tableData?.pageSize || 10,
  total: props.tableData?.total || 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small',
  position: ['bottomCenter'],
  align: 'center',
  onChange: handlePageChange,
  onShowSizeChange: handlePageSizeChange,
}))

const handlePageChange = (page: number) => {
  emit('pageChange', {
    pageNum: page,
    pageSize: paginationConfig.value.pageSize,
    sqlContentId: props.sqlContentId,
  })
}

const handlePageSizeChange = (current: number, size: number) => {
  emit('pageChange', {
    pageNum: current,
    pageSize: size,
    sqlContentId: props.sqlContentId,
  })
}

// 表格列配置
const columns = computed(() => {
  if (!props.tableData || props.tableData.list.length === 0) {
    return []
  }
  const keys = Object.keys(props.tableData.list[0])

  const cols = [
    {
      title: '操作',
      key: 'action',
      dataIndex: 'action',
      fixed: true,
      align: 'center',
      width: 60,
      customCell: () => ({ style: { minWidth: '60px' } }),
    },
  ]

  keys.forEach((key) => {
    cols.push({
      title: formatColumnName(key),
      dataIndex: key,
      key: key,
      ellipsis: true,
      customCell: () => ({
        style: {
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        },
      }),
    })
  })

  return cols
})

// 显示详情
const showDetail = (record: any) => {
  currentDetailRecord.value = record
  detailModalVisible.value = true
}

// 复制数据
const copyTableData = () => {
  try {
    if (!props.tableData || props.tableData.list.length === 0) {
      return
    }

    const headers = Object.keys(props.tableData.list[0]).join('\t')
    const rows = props.tableData.list.map((row) => Object.values(row).join('\t')).join('\n')

    const data = `${headers}\n${rows}`
    navigator.clipboard.writeText(data)
    message.success('表格数据已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    message.error('复制失败')
  }
}

// 导出Excel
const exportToExcel = () => {
  try {
    if (!props.tableData || props.tableData.list.length === 0) {
      message.warning('没有数据可导出')
      return
    }

    const wb = XLSX.utils.book_new()
    const headers = Object.keys(props.tableData.list[0])
    const data = props.tableData.list.map((row) => headers.map((header) => row[header]))

    const ws = XLSX.utils.aoa_to_sheet([headers.map((header) => formatColumnName(header)), ...data])

    const colWidths = headers.map((header) => ({
      wch: Math.max(header.length, ...data.map((row) => String(row[headers.indexOf(header)]).length)),
    }))
    ws['!cols'] = colWidths

    XLSX.utils.book_append_sheet(wb, ws, '数据查询结果')
    XLSX.writeFile(wb, `数据查询结果_${new Date().toISOString().slice(0, 10)}.xlsx`)

    message.success('导出成功')
  } catch (err) {
    console.error('导出失败:', err)
    message.error('导出失败')
  }
}
</script>

<style lang="less" scoped>
.data-query-container {
  display: flex;
  justify-content: left;
  margin-bottom: 16px;
  width: 100%;

  .data-card {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    position: relative;

    .card-header-actions {
      display: flex;
      gap: 8px;
    }

    .table-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
      gap: 8px;
    }
  }
}

.true-fullscreen-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background-color: white;
  display: flex;
  flex-direction: column;

  .browser-fullscreen-content {
    flex: 1;
    overflow: hidden;
    padding: 16px;
  }

  .browser-fullscreen-header {
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1;

    .title-container {
      font-size: 18px;
      font-weight: bold;
    }

    .fullscreen-top-actions {
      display: flex;
      gap: 8px;
    }
  }
}
</style>
