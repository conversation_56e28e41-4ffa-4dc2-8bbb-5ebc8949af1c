<template>
  <div v-if="echartsOption && Object.keys(echartsOption).length > 0" class="chart-container">
    <div class="chart-wrapper">
      <v-chart ref="chartRef" :autoresize="true" :option="echartsOption" class="echarts-instance" />
      <a-button class="download-btn" shape="circle" type="primary" @click="downloadChart">
        <template #icon>
          <CameraOutlined />
        </template>
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { CameraOutlined } from '@ant-design/icons-vue'
import VChart from 'vue-echarts'

defineProps({
  echartsOption: {
    type: Object,
    default: () => ({}),
  },
})

// 关键修复：使用正确的组件引用类型
const chartRef = ref<InstanceType<typeof VChart> | null>(null)

// 修复后的下载方法
const downloadChart = async () => {
  if (!chartRef.value) {
    return
  }

  // 正确获取图表实例
  const chartInstance = chartRef.value.chart // 使用 .chart 属性
  if (!chartInstance) {
    return
  }

  const dataURL = chartInstance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff',
  })

  const link = document.createElement('a')
  link.download = 'chart.png'
  link.href = dataURL
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>

<style lang="less" scoped>
.chart-container {
  display: flex;
  justify-content: left;
  margin-bottom: 16px;
  width: 100%;

  .chart-wrapper {
    position: relative;
    width: 100%;
    max-width: 1020px;

    .echarts-instance {
      height: 390px;
      width: 100%;
    }

    .download-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 10;
    }
  }
}
</style>
