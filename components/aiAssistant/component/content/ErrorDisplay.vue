<template>
  <div v-if="errorContent" class="error-display">
    <div class="error-header">
      <div class="error-icon">
        <svg fill="none" height="16" viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
            stroke="#FF4D4F"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
      </div>
      <div class="error-title">执行出错</div>
    </div>
    <div class="error-content">
      <pre>{{ errorContent }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  errorContent: {
    type: String,
    default: '',
  },
})
</script>

<style lang="less" scoped>
.error-display {
  margin: 16px 0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  background-color: #fff2f0;
  overflow: hidden;
  animation: fadeIn 0.3s ease;
}

.error-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #ffccc7;
}

.error-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.error-title {
  font-weight: 500;
  color: #f5222d;
}

.error-content {
  padding: 12px;
  font-size: 13px;
  line-height: 1.6;
  color: #f5222d;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  white-space: pre-wrap;
  word-break: break-word;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
