<template>
  <div class="container">
    <!-- 消息列表（可滚动区域） -->
    <div ref="scrollRef" class="scrollable-area">
      <!-- 头像 -->
      <div class="welcome-container">
        <!-- 圆形头像 -->
        <div class="avatar-wrapper">
          <a-avatar :size="64" :src="ailogohead" />
        </div>

        <!-- 右侧欢迎语 -->
        <div class="welcome-message">
          你好我是商务AI智能体"百应"，我有如下技能，点击体验吧！<span style="color: red; font-size: 12px"
            >（目前仅支持会务及异地宴请，后续更多场景陆续上线中...）</span
          >
        </div>
      </div>

      <!-- 能力展示区 -->
      <div class="abilities-container">
        <div v-for="(ability, index) in abilities" :key="index" class="ability-card">
          <div class="ability-header">
            <div class="title-container">
              <span class="ability-title">{{ ability.subject }}</span>
              <a-popover placement="top">
                <template #content>
                  <div class="ability-description">
                    {{ ability.description }}
                  </div>
                </template>
                <question-circle-outlined class="help-icon" />
              </a-popover>
            </div>
            <a-button size="small" type="link" @click.stop="refreshExample(index)">
              换一换
              <reload-outlined :style="{ color: '#8c8c8c' }" />
            </a-button>
          </div>
          <div class="ability-example" @click="selectExample(currentExamples[index].content)">
            <div class="example-content">
              <span>💡</span>
              {{ currentExamples[index]?.content || '' }}
            </div>
          </div>
        </div>
      </div>
      <div v-for="(v, index) in dialogues" :key="index" class="dialogue-item">
        <!-- 用户消息部分 -->
        <div v-if="v.message" class="user-message">
          <div class="avatar-container">
            <a-avatar size="small">
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
          </div>
          <div class="message-bubble">
            {{ v.message }}
          </div>
        </div>

        <!-- AI回复部分 -->
        <div class="ai-reply-container">
          <div class="avatar-container" style="flex: 0 0 5%">
            <a-avatar size="small">
              <template #icon>
                <img :src="ailogohead" />
              </template>
            </a-avatar>
          </div>

          <div class="ai-reply-content">
            <div v-if="loading && index === dialogues.length - 1" class="loading-progress">
              <div class="progress-bar"></div>
            </div>
            <!-- 思考过程组件 -->
            <ThinkingProcess
              :execution-duration="v.executionDuration"
              :thinking-content="v.thinkingContent"
              :thinking-loading="v.thinkingLoading"
            />
            <Conversation :ailogohead="ailogohead" :dialogue="v" />

            <DataAnalysis :echarts-option="v.echartsOption" />
            <DataQuery
              :is-fullscreen="isFullscreen"
              :sql-content-id="v.sqlContentId"
              :table-data="v.tableData"
              @page-change="handlePageChange"
            />
            <FormPreFill :mice-request="v.miceRequest" @preview="handlePreview" />
            <ErrorDisplay :error-content="v.errorContent" />
            <TemplateRenderer
              v-if="v.userMessageTemplateResult"
              :user-message-template-result="v.userMessageTemplateResult"
              @send-template-message="handleTemplateMessage"
            />
            <RelatedOpinions
              v-if="v.relatedOpinions && v.relatedOpinions.length > 0"
              :opinions="v.relatedOpinions"
              @ask-again="fillPrompt"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 输入框（固定区域） -->
    <div class="fixed-input-area">
      <div class="input-container">
        <!-- 文本输入区域 -->
        <div class="textarea-container">
          <a-textarea
            :auto-size="{ minRows: 2, maxRows: 5 }"
            :bordered="false"
            :disabled="loading"
            :value="userMessage"
            class="custom-input"
            placeholder="请输入你想分析的数据。例如：按部门分析一下2025年机票超标情况，以排行图显示"
            @update:value="handleInput"
            @keydown.enter="handleEnterKey"
          />
        </div>

        <!-- 新增的操作按钮行 -->
        <div class="action-container">
          <!-- 精细化问答开关 -->
          <div
            :class="{ active: enableMessageCheck }"
            class="refinement-button"
            @click="enableMessageCheck = !enableMessageCheck"
          >
            <svg
              v-if="!enableMessageCheck"
              fill="none"
              height="1em"
              viewBox="0 0 24 24"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M12 14.019a2.02 2.02 0 1 0 0-4.04 2.02 2.02 0 0 0 0 4.04" fill="currentColor"></path>
              <path
                clip-rule="evenodd"
                d="M2.059 6.209c-.14-.932-.098-2.259.897-3.253.994-.995 2.321-1.037 3.253-.897.98.147 2.02.556 3.026 1.084.878.46 1.81 1.052 2.765 1.753a22 22 0 0 1 2.765-1.753c1.007-.527 2.046-.937 3.026-1.084.932-.14 2.259-.097 3.253.897s1.037 2.321.897 3.253c-.147.98-.557 2.02-1.084 3.026-.46.878-1.052 1.81-1.753 2.765a22 22 0 0 1 1.753 2.765c.527 1.007.937 2.046 1.084 3.026.14.932.098 2.259-.897 3.253-.994.994-2.321 1.037-3.253.897-.98-.147-2.02-.557-3.026-1.084A22 22 0 0 1 12 19.104a22 22 0 0 1-2.766 1.754c-1.006.527-2.045.936-3.025 1.083-.932.14-2.259.098-3.253-.897-.995-.994-1.037-2.321-.897-3.253.147-.98.556-2.02 1.084-3.026A22 22 0 0 1 4.896 12a22 22 0 0 1-1.753-2.766C2.616 8.228 2.206 7.19 2.059 6.21m2.325-1.825c.892-.892 3.238-.1 5.969 1.816-.724.613-1.45 1.28-2.161 1.992a36 36 0 0 0-1.992 2.16c-1.916-2.73-2.708-5.076-1.816-5.968M9.62 9.62A33 33 0 0 0 7.455 12a33 33 0 0 0 2.165 2.38A33 33 0 0 0 12 16.545a33 33 0 0 0 2.38-2.165A33 33 0 0 0 16.545 12a33 33 0 0 0-2.165-2.38A33 33 0 0 0 12 7.455 33 33 0 0 0 9.62 9.62m-5.236 9.996c-.892-.892-.1-3.238 1.816-5.969.613.724 1.28 1.449 1.992 2.16.712.713 1.437 1.38 2.161 1.993-2.73 1.916-5.077 2.708-5.97 1.816m15.232 0c-.892.892-3.238.1-5.969-1.816a36 36 0 0 0 2.16-1.992 36 36 0 0 0 1.993-2.161c1.916 2.73 2.708 5.077 1.816 5.969M15.808 8.192a36 36 0 0 1 1.992 2.16c1.915-2.73 2.708-5.076 1.816-5.968s-3.238-.1-5.969 1.816c.724.613 1.45 1.28 2.161 1.992"
                fill="currentColor"
                fill-rule="evenodd"
              ></path>
            </svg>
            <svg v-else fill="none" height="1em" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg">
              <path
                clip-rule="evenodd"
                d="M2.059 6.209c-.14-.932-.098-2.259.897-3.253.994-.995 2.321-1.037 3.253-.897.98.147 2.02.556 3.026 1.084.878.46 1.81 1.052 2.765 1.753a22 22 0 0 1 2.765-1.753c1.007-.527 2.046-.937 3.026-1.084.932-.14 2.259-.097 3.253.897s1.037 2.321.897 3.253c-.147.98-.557 2.02-1.084 3.026-.46.878-1.052 1.81-1.753 2.765l.021.03a6.485 6.485 0 0 0-2.934.392q.18-.212.354-.422a33 33 0 0 0-2.165-2.38A33 33 0 0 0 12 7.455 33 33 0 0 0 9.62 9.62 33 33 0 0 0 7.455 12a33 33 0 0 0 2.165 2.38A33 33 0 0 0 12 16.545q.21-.173.422-.354a6.5 6.5 0 0 0-.392 2.934l-.03-.021a22 22 0 0 1-2.766 1.754c-1.006.527-2.045.936-3.025 1.083-.932.14-2.259.098-3.253-.897-.995-.994-1.037-2.321-.897-3.253.147-.98.556-2.02 1.084-3.026A22 22 0 0 1 4.896 12a22 22 0 0 1-1.753-2.766C2.616 8.228 2.206 7.19 2.059 6.21m2.325-1.825c.892-.892 3.238-.1 5.969 1.816-.724.613-1.45 1.28-2.161 1.992a36 36 0 0 0-1.992 2.16c-1.916-2.73-2.708-5.076-1.816-5.968m0 15.232c-.892-.892-.1-3.238 1.816-5.969.613.724 1.28 1.449 1.992 2.16.712.713 1.437 1.38 2.161 1.993-2.73 1.916-5.077 2.708-5.97 1.816M15.808 8.192a36 36 0 0 1 1.992 2.16c1.915-2.73 2.708-5.076 1.816-5.968s-3.238-.1-5.969 1.816c.724.613 1.45 1.28 2.161 1.992"
                fill="currentColor"
                fill-rule="evenodd"
              ></path>
              <path d="M12 14.019a2.02 2.02 0 1 0 0-4.04 2.02 2.02 0 0 0 0 4.04" fill="currentColor"></path>
              <path
                clip-rule="evenodd"
                d="M18.5 24a5.5 5.5 0 1 1 0-11 5.5 5.5 0 0 1 0 11m2.6-6.832a.497.497 0 0 0-.703-.005L17.92 19.64l-1.126-1.126a.496.496 0 0 0-.701.006.496.496 0 0 0-.006.701l.329.329 1.15 1.15a.5.5 0 0 0 .708 0q1.023-1.025 2.048-2.049l.782-.781a.497.497 0 0 0-.005-.702"
                fill="currentColor"
                fill-rule="evenodd"
              ></path>
            </svg>
            精确问答{{ enableMessageCheck ? '：开' : '' }}
          </div>

          <!-- 发送按钮 -->
          <a-button
            :loading="loading"
            class="send-button"
            shape="round"
            type="primary"
            @click="handleSend(enableMessageCheck)"
          >
            <template #icon>
              <SendOutlined style="transform: rotate(180deg); font-size: 14px" />
            </template>
            发送
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, PropType, ref, watch } from 'vue'
import { QuestionCircleOutlined, ReloadOutlined, SendOutlined, UserOutlined } from '@ant-design/icons-vue'

import { AiIntroduceResult, IAgentDTO } from '@haierbusiness-front/common-libs/src/ai'
import { agentsApi } from '@haierbusiness-front/apis'
import ThinkingProcess from './content/ThinkingProcess.vue'
import Conversation from './content/Conversation.vue'
import DataAnalysis from './content/DataAnalysis.vue'
import DataQuery from './content/DataQuery.vue'
import FormPreFill from './content/MiceFormPreFill.vue'
import ErrorDisplay from './content/ErrorDisplay.vue'
import RelatedOpinions from './content/RelatedOpinions.vue'
import TemplateRenderer from './content/TemplateRenderer.vue'

const enableMessageCheck = ref<boolean>(false)

const props = defineProps({
  isFullscreen: {
    type: Boolean,
    default: false,
  },
  dialogues: {
    type: Array as PropType<IAgentDTO[]>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  ailogohead: {
    type: String,
    default: '',
  },
  userMessage: {
    type: String,
    default: '',
  },
  abilities: {
    type: Array as PropType<AiIntroduceResult[]>,
    default: () => [],
  },
})
const emit = defineEmits(['update:userMessage', 'send', 'preview'])

// 添加输入处理函数
const handleInput = (value: string) => {
  emit('update:userMessage', value)
}
const scrollRef = ref<HTMLElement>()

const currentExamples = ref(
  props.abilities.map((ability) => ability.prompts[Math.floor(Math.random() * ability.prompts.length)]),
)
// 换一换功能
const refreshExample = (index: number) => {
  const ability = props.abilities[index]
  const otherExamples = ability.prompts.filter((ex) => ex !== currentExamples.value[index])

  if (otherExamples.length > 0) {
    const randomIndex = Math.floor(Math.random() * otherExamples.length)
    currentExamples.value[index] = otherExamples[randomIndex]
  }
}

// 选择案例到输入框
const selectExample = (example: string) => {
  emit('update:userMessage', example)
}

// 新增方法：处理发送模板消息
const handleTemplateMessage = (message: string) => {
  emit('send', false, message)
}

// 新增填充提示语方法
const fillPrompt = (prompt: string) => {
  emit('update:userMessage', prompt)
  // 可选：自动聚焦到输入框
  nextTick(() => {
    const textarea = document.querySelector('.custom-input textarea')
    if (textarea) {
      ;(textarea as HTMLTextAreaElement).focus()
    }
  })
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (scrollRef.value) {
      scrollRef.value.scrollTop = scrollRef.value.scrollHeight
    }
  })
}

// 修改后的 handlePageChange 方法
const handlePageChange = (params: { pageNum: number; pageSize: number; sqlContentId: number }) => {
  // 根据 sqlContentId 找到对应的对话项
  const targetDialogue = props.dialogues.find((d) => d.sqlContentId === params.sqlContentId)

  if (targetDialogue) {
    // 重新执行分页查询
    agentsApi
      .executeSql({
        sqlContentId: String(params.sqlContentId), // 确保类型匹配
        pageNum: params.pageNum,
        pageSize: params.pageSize,
      })
      .then((response) => {
        // 更新表格数据
        targetDialogue.tableData = {
          ...response,
          sqlContentId: params.sqlContentId,
        }
      })
      .catch((error) => {
        console.error('分页查询失败:', error)
        // 可以添加错误处理逻辑，如显示错误提示
      })
  } else {
    console.warn(`未找到匹配的对话项，sqlContentId: ${params.sqlContentId}`)
  }
}

// 监听对话变化自动滚动
watch(
  () => props.dialogues?.length,
  () => scrollToBottom(),
  { deep: true },
)
// 添加回车键处理函数
const handleEnterKey = (event: KeyboardEvent) => {
  if (!event.shiftKey) {
    event.preventDefault()
    handleSend(enableMessageCheck.value)
  }
}
const handleSend = (enableMessageCheck: boolean) => {
  emit('send', enableMessageCheck)
  scrollToBottom()
}

const handlePreview = (miceRequestData: any) => {
  emit('preview', miceRequestData)
}

// 保存滚动位置
const saveScrollPosition = () => {
  return scrollRef.value?.scrollTop || 0
}

// 恢复滚动位置
const restoreScrollPosition = (position: number) => {
  nextTick(() => {
    if (scrollRef.value) {
      scrollRef.value.scrollTop = position
    }
  })
}

// 暴露方法给父组件
defineExpose({
  saveScrollPosition,
  restoreScrollPosition,
  scrollToBottom,
})
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

/* 更新欢迎容器样式 */
.welcome-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 16px 10px;
  gap: 16px;
  background: linear-gradient(to right, #f0f7ff, #ffffff);
  border-radius: 8px;
  margin: 0 16px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 能力展示区样式 */
.abilities-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 0 16px 16px;
}

.ability-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 12px;
  background: white;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.ability-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.ability-title {
  font-weight: 500;
  color: #1890ff;
}

.ability-example {
  /* 保留原有样式 */
  position: relative; /* 确保定位正确 */

  .send-hint {
    position: absolute;
    bottom: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0.8;
    transition: all 0.3s;

    span {
      line-height: 1;
    }
  }

  &:hover .send-hint {
    opacity: 1;
    background: rgba(24, 144, 255, 0.2);
    transform: translateY(-2px);
  }
}

.avatar-wrapper {
  flex: 0 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);

  .ant-avatar {
    border: 2px solid #1890ff;
  }
}

.welcome-message {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid white;
  }
}

// 功能介绍
.intro-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
}

.intro-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background-color: #f5f9ff;
  }

  h3 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 16px;
    color: #1890ff;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #666;
  }
}

.scrollable-area {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 只在此区域滚动 */
  padding-bottom: 10px; /* 底部留出空间 */
  padding: 6px;
}

.fixed-input-area {
  flex: none; /* 不参与flex伸缩 */
  padding: 10px;
}

.input-container {
  display: flex;
  flex-direction: column;
  border-radius: 12px; /* 增加圆角 */
  background: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* 添加阴影 */
  border: 1px solid #e0e0e0; /* 单层边框 */
  transition: all 0.3s ease; /* 添加过渡效果 */
  padding: 6px;
  min-height: 64px;
}

.input-container:hover {
  border-color: #1890ff; /* 悬停时边框颜色变化 */
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.2); /* 悬停时阴影变化 */
}

.textarea-container {
  flex: 1;
  display: flex;
  position: relative;
  padding-right: 0; /* 移除右侧留白 */
}

/* 自定义输入框样式 */
.custom-input {
  padding: 8px 70px 8px 10px;
  min-height: 78px;
  line-height: 1.95;
}

/* 发送按钮样式优化 */
.send-button {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.send-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.input-container:hover::before {
  opacity: 0.6;
}

/* 确保文本区域自适应 */
.ant-input {
  flex: 1;
  min-height: 80px;
}

.button-container {
  position: absolute;
  right: 10px;
  bottom: 8px; /* 微调底部位置 */
  padding: 0;
}

.ai-reply-container {
  display: flex;
  justify-content: flex-start; /* 确保容器内容左对齐 */
  align-items: flex-start;
  margin-bottom: 10px;
}

.ai-reply-content {
  flex: 1; /* 改为自适应宽度 */
  max-width: 95%; /* 保留最大宽度限制 */
}

.user-message {
  display: flex;
  justify-content: flex-end; /* 保持右对齐 */
  margin-top: 60px;
  margin-bottom: 10px;
  align-items: flex-start;

  .message-bubble {
    background-color: #dddddd;
    border-radius: 8px;
    padding: 8px 12px;
    white-space: pre-wrap;
    word-break: break-word;
    display: inline-block;
    max-width: 80%;
    text-align: left;
    margin-right: 10px;
  }

  .avatar-container {
    order: 2; /* 将头像放在右侧 */
  }
}

.message-bubble {
  background-color: #dddddd;
  border-radius: 8px;
  padding: 8px 12px;
  white-space: pre-wrap;
  word-break: break-word;
  position: relative;
  box-sizing: border-box;
  display: block;
  max-width: 95%;
  text-align: left;
  margin-left: 10px;
}

.avatar-container {
  padding: 4px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 新增操作按钮容器 */

.action-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.refinement-button {
  border-radius: 8px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 0px 12px;
  height: 32px;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
  border: 1px solid #d9d9d9;

  &.active {
    border-color: rgb(24, 104, 219);
    background-color: rgb(229, 238, 255);
    color: rgb(24, 104, 219);
  }
}

.button-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.loading-progress {
  width: 100%;
  height: 4px;
  background-color: rgba(24, 144, 255, 0.1);
  position: relative;
  overflow: hidden;
  margin-left: 2px;
  border-radius: 2px;
}

.progress-bar {
  position: absolute;
  height: 100%;
  width: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(24, 144, 255, 0.8) 25%,
    rgba(24, 144, 255, 0.6) 50%,
    rgba(24, 144, 255, 0.8) 75%,
    transparent 100%
  );
  animation: shimmer 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  border-radius: 2px;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.title-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.help-icon {
  color: #8c8c8c;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;

  &:hover {
    color: #1890ff;
  }
}

.ability-description {
  max-width: 280px;
  padding: 8px;
  font-size: 13px;
  line-height: 1.5;
  color: #595959;
}

.ability-example {
  /* 保留原有样式 */
  border-radius: 8px; /* 确保圆角 */
  overflow: hidden; /* 防止内部元素溢出圆角 */

  .example-content {
    cursor: pointer;
    position: relative;
    padding: 10px 12px; /* 增加内边距 */
    background-color: #f5f9ff; /* 添加浅蓝色背景 */
    border-radius: 6px; /* 内部圆角 */
    transition: all 0.3s; /* 添加过渡效果 */
  }

  &:hover .example-content {
    background-color: #e6f4ff; /* 悬停时加深背景 */
    transform: translateY(-2px); /* 轻微上浮效果 */
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15); /* 添加阴影 */
  }
}
</style>
