<script lang="ts" setup>
import { CloseOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons-vue'
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { agents<PERSON>pi, aiApi, introduceApi } from '@haierbusiness-front/apis'
import { registerMap, use } from 'echarts/core'
import { v4 } from 'uuid'
import { CanvasRenderer } from 'echarts/renderers'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>plot<PERSON>hart,
  Candlestick<PERSON>hart,
  CustomChart,
  FunnelChart,
  Gauge<PERSON>hart,
  Graph<PERSON>hart,
  <PERSON><PERSON>hart,
  LinesChart,
  Map<PERSON>hart,
  <PERSON><PERSON>l<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>key<PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Treemap<PERSON>hart,
} from 'echarts/charts'
import {
  CalendarComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  VisualMapComponent,
} from 'echarts/components'
import { marked } from 'marked'
import { AiIntroduceResult, IAgentDTO } from '@haierbusiness-front/common-libs/src/ai'
import ailogo from './assets/image/AI.png'
import ailogohead from './assets/image/AIhead.png'

import ChatMainContent from './component/ChatMainContent.vue'
import ConversationList from './component/ConversationList.vue'
import chinaMap from './mapsource/chinaMap.json'

// 配置AI助手位置
interface Props {
  top?: number
}

const props = withDefaults(defineProps<Props>(), {
  top: 260,
})

use([
  CanvasRenderer,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  BarChart,
  BoxplotChart,
  CustomChart,
  CandlestickChart,
  LineChart,
  PieChart,
  TreemapChart,
  TreeChart,
  ParallelChart,
  MapChart,
  LinesChart,
  GraphChart,
  GaugeChart,
  FunnelChart,
  SankeyChart,
  VisualMapComponent,
  CalendarComponent,
  RadarChart,
])
registerMap('china', chinaMap)
const titlePrefix = ''
const titleHighlight = 'AI'
const titleSuffix = '智能体-百应'

// 新增标志位，用于区分拖拽和点击
const dragDistance = ref(0) // 记录拖拽距离

const handleMouseUp = () => {
  // 只有当拖拽距离小于5像素时才视为点击
  if (dragDistance.value < 5) {
    openChat()
  }
  dragDistance.value = 0
  endDrag() // 确保结束拖拽
}
const openChat = () => {
  openChatFlag.value = true
}
const openChatClose = () => {
  openChatFlag.value = false
}
const openChatFlag = ref<boolean>(false)
const userMessage = ref<string>()
const loading = ref(false)

const dialogues = ref<Array<IAgentDTO>>([])

// 新增对话列表状态
const currentChatId = ref<string>(v4()) // 默认当前对话ID
const conversations = ref([])
const isNewConversation = ref<boolean>(true)
// 创建新对话
const createNewConversation = () => {
  currentChatId.value = v4()
  isNewConversation.value = true
  // 清空当前对话内容
  dialogues.value = []
  userMessage.value = ''
}

// 修改contextList函数
const contextList = () => {
  return agentsApi
    .contextList()
    .then((response) => {
      for (const iAiChartContextDO of response) {
        conversations.value.push({
          chatId: iAiChartContextDO.chatId,
          subject: iAiChartContextDO.subject,
        })
      }
    })
    .finally(() => {
      nextTick(() => {
        if (chatMainContentRef.value) {
          chatMainContentRef.value.scrollToBottom()
        }
      })
    })
}

// 修改contextListDetails函数，接受chatId参数
const contextListDetails = (chatId: string) => {
  agentsApi
    .contextListDetails({ chatId })
    .then((response) => {
      dialogues.value = [] // 清空当前对话
      for (const iAiChatContextDO of response) {
        const content: IAgentDTO = JSON.parse(iAiChatContextDO.context)
        const newDialogue = reactive<IAgentDTO>({})
        newDialogue.message = iAiChatContextDO.message
        if (content.errorContent) {
          newDialogue.errorContent = content.errorContent
        }
        if (iAiChatContextDO.executionDuration) {
          newDialogue.executionDuration = iAiChatContextDO.executionDuration
        }
        if (content.dialogueContent) {
          newDialogue.dialogueContent = marked(content.dialogueContent) as string
        }
        if (content.echartsOption) {
          newDialogue.echartsOption = content.echartsOption
        }
        if (content.thinkingContent && content.thinkingContent != 'null') {
          newDialogue.thinkingContent = marked(content.thinkingContent) as string
        }
        if (content.relatedOpinions) {
          newDialogue.relatedOpinions = content.relatedOpinions
        }
        if (content.sql) {
          newDialogue.sql = content.sql
        }
        if (content.sqlContentId) {
          newDialogue.sqlContentId = content.sqlContentId
        }
        if (content.tableData) {
          newDialogue.tableData = content.tableData
        }
        if (content.chartData) {
          newDialogue.chartData = content.chartData
        }
        if (content.miceRequest) {
          newDialogue.miceRequest = content.miceRequest
        }
        if (content.userMessageTemplateResult) {
          newDialogue.userMessageTemplateResult = content.userMessageTemplateResult
        }
        dialogues.value.push(newDialogue)
      }
    })
    .finally(() => {
      nextTick(() => {
        if (chatMainContentRef.value) {
          chatMainContentRef.value.scrollToBottom()
        }
      })
    })
}

// 切换对话
const switchConversation = (chatId: string) => {
  currentChatId.value = chatId
  isNewConversation.value = false
  contextListDetails(chatId)
}

onMounted(() => {
  contextList().then(() => {
    if (conversations.value && conversations.value.length > 0) {
      isNewConversation.value = false
      currentChatId.value = conversations.value[conversations.value.length - 1].chatId
      // 加载默认对话
      contextListDetails(currentChatId.value)
    }
  })
})

onMounted(() => {
  // 6秒后显示气泡
  bubbleTimer.value = setTimeout(() => {
    if (!openChatFlag.value) {
      showBubble.value = true
    }
  }, 3000)
})

// 初始提示词
const abilities = ref<AiIntroduceResult[]>()

const initIntroduce = () => {
  introduceApi.list({}).then((response) => {
    // 更新表格数据
    abilities.value = response
  })
}

onMounted(() => {
  initIntroduce()
})

// 新增气泡状态
const showBubble = ref(false)
const bubbleTimer = ref(null)
const bubbleMessages = ref([
  '点击我，解锁今日份的智慧彩蛋 🥚',
  '听说聪明的人都爱和我聊天 😉',
  '我猜您现在需要...（戳我揭晓答案）🔍',
  '别让我等太久哦，知识会过期哒！⏳',
  '叮！您的专属智囊团已上线💡',
  '和我聊天的人，今天都变聪明了✨',
])

// 当前显示的气泡消息
const currentBubbleMessage = ref(bubbleMessages.value[Math.floor(Math.random() * bubbleMessages.value.length)])

// 在显示气泡时选择随机消息
watch(showBubble, (newVal) => {
  if (newVal) {
    // 随机选择消息
    const randomIndex = Math.floor(Math.random() * bubbleMessages.value.length)
    currentBubbleMessage.value = bubbleMessages.value[randomIndex]
  }
})

// 添加气泡元素的引用
const bubbleRef = ref<HTMLElement | null>(null)

// 点击文档任意位置的处理函数
const handleDocumentClick = (event: MouseEvent) => {
  // 如果点击的不是气泡本身或气泡的子元素，则关闭气泡
  if (bubbleRef.value && !bubbleRef.value.contains(event.target as Node)) {
    showBubble.value = false
  }
}
// 监听气泡显示状态的变化
watch(showBubble, (newVal) => {
  if (newVal) {
    // 随机选择消息
    const randomIndex = Math.floor(Math.random() * bubbleMessages.value.length)
    currentBubbleMessage.value = bubbleMessages.value[randomIndex]

    // 添加全局点击事件监听
    nextTick(() => {
      document.addEventListener('click', handleDocumentClick)
    })
  } else {
    // 移除全局点击事件监听
    document.removeEventListener('click', handleDocumentClick)
  }
})
onUnmounted(() => {
  // 清理定时器
  if (bubbleTimer.value) {
    clearTimeout(bubbleTimer.value)
  }
  document.removeEventListener('click', handleDocumentClick)
})

const handleBubbleSwitch = () => {
  showBubble.value = false
  openChat() // 打开主聊天窗口
  // 如果有对话记录，切换到最新对话
  if (conversations.value.length > 0) {
    const lastConversation = conversations.value[conversations.value.length - 1]
    switchConversation(lastConversation.chatId)
  }
}

const sendUserMessage = (enableMessageCheck: boolean = false, message?: string) => {
  if (loading.value) {
    return
  }
  const currentMessage = message || userMessage.value

  if (isNewConversation.value) {
    conversations.value.push({
      chatId: currentChatId.value,
      subject: `新对话`,
    })
  }

  loading.value = true
  const newDialogue = reactive<IAgentDTO>({
    message: currentMessage,
    thinkingLoading: true,
  })
  dialogues.value.push(newDialogue)

  let accumulatedText = ''
  let thinkingContentText = ''

  try {
    aiApi.executeAgent(
      {
        message: currentMessage,
        chatId: currentChatId.value,
        enableMessageCheck: enableMessageCheck,
      },
      (content) => {
        userMessage.value = ''
        nextTick(() => {
          if (content.echartsOption) {
            newDialogue.echartsOption = content.echartsOption
          }
          if (content.errorContent) {
            newDialogue.errorContent = content.errorContent
          }
          if (content.thinkingContent && content.thinkingContent != 'null') {
            thinkingContentText += content.thinkingContent
            newDialogue.thinkingContent = marked(thinkingContentText) as string
          }
          if (content.dialogueContent) {
            accumulatedText += content.dialogueContent
            newDialogue.dialogueContent = marked(accumulatedText) as string
          }
          if (content.executionDuration) {
            newDialogue.executionDuration = content.executionDuration
          }
          if (content.relatedOpinions) {
            newDialogue.relatedOpinions = content.relatedOpinions
          }
          if (content.sql) {
            newDialogue.sql = content.sql
          }
          if (content.sqlContentId) {
            newDialogue.sqlContentId = content.sqlContentId
          }
          if (content.tableData) {
            newDialogue.tableData = content.tableData
          }
          if (content.chartData) {
            newDialogue.chartData = content.chartData
          }
          if (content.miceRequest) {
            newDialogue.miceRequest = content.miceRequest
          }
          if (content.userMessageTemplateResult) {
            newDialogue.userMessageTemplateResult = content.userMessageTemplateResult
          }
          // 滚动到最下
          if (chatMainContentRef.value) {
            chatMainContentRef.value.scrollToBottom()
          }
        })
      },
      () => {
        newDialogue.thinkingLoading = false
        loading.value = false
      },
      (err) => {
        newDialogue.thinkingLoading = false
        console.error('流处理错误:', err)
        loading.value = false
      },
    )
  } catch (error) {
    console.error('请求错误:', error)
    newDialogue.thinkingLoading = false
    loading.value = false
  } finally {
    if (isNewConversation.value) {
      agentsApi.contextListDetails(currentChatId.value).then((response) => {
        conversations.value[conversations.value.length - 1].subject = response[response.length - 1].subject
        console.log(response[response.length - 1].subject)
      })
      isNewConversation.value = false
    }
  }
}
// 监听对话框打开状态
watch(
  () => openChatFlag.value,
  (newVal) => {
    if (newVal) {
      // 对话框打开时滚动到底部
      nextTick(() => {
        if (chatMainContentRef.value) {
          chatMainContentRef.value.scrollToBottom()
        }
      })
    }
  },
)
// 监听对话变化自动滚动
watch(
  () => dialogues.value?.length,
  () => {
    nextTick(() => {
      if (chatMainContentRef.value) {
        chatMainContentRef.value.scrollToBottom()
      }
    })
  },
  { deep: true },
)

// 新增事件发射器
const emit = defineEmits(['close-and-pass-data'])

// 处理关闭并传递数据的函数
const handleCloseAndPass = (miceRequestData: any) => {
  openChatFlag.value = false // 关闭浮层
  // 确保组件卸载时恢复滚动
  document.body.style.overflow = ''
  emit('close-and-pass-data', miceRequestData) // 向父组件传递数据
}

// 新增拖拽位置状态
const position = reactive({
  x: window.innerWidth - 85, // 初始位置：右侧5px
  y: props.top,
})

// 拖拽状态
const isDragging = ref(false)
let startX = 0
let startY = 0

// 开始拖拽
const startDrag = (event: MouseEvent) => {
  isDragging.value = true
  startX = event.clientX
  startY = event.clientY

  // 添加全局事件监听
  window.addEventListener('mousemove', dragMove)
  window.addEventListener('mouseup', endDrag)
  event.preventDefault() // 防止文本选中
}

// 拖拽移动
const dragMove = (event: MouseEvent) => {
  if (!isDragging.value) {
    return
  }

  // 计算偏移量
  const deltaX = event.clientX - startX
  const deltaY = event.clientY - startY

  // 更新拖拽距离（用于区分点击和拖拽）
  dragDistance.value += Math.abs(deltaX) + Math.abs(deltaY)

  // 更新位置
  position.x += deltaX
  position.y += deltaY

  // 边界检查
  position.x = Math.max(0, Math.min(position.x, window.innerWidth - 80))
  position.y = Math.max(0, Math.min(position.y, window.innerHeight - 90))

  // 更新起始点
  startX = event.clientX
  startY = event.clientY
}

// 结束拖拽
const endDrag = () => {
  isDragging.value = false

  // 移除事件监听
  window.removeEventListener('mousemove', dragMove)
  window.removeEventListener('mouseup', endDrag)
}

// 在组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('mousemove', dragMove)
  window.removeEventListener('mouseup', endDrag)
})

const isFullscreen = ref(false)
const chatMainContentRef = ref()
// 全屏切换方法
// 在原有代码基础上修改
const toggleFullscreen = () => {
  // 保存当前滚动位置
  let scrollPosition = 0
  if (chatMainContentRef.value) {
    scrollPosition = chatMainContentRef.value.saveScrollPosition()
  }

  if (isFullscreen.value) {
    document.body.style.overflow = ''
  } else {
    document.body.style.overflow = 'hidden'
  }

  isFullscreen.value = !isFullscreen.value

  // 恢复滚动位置
  nextTick(() => {
    if (chatMainContentRef.value) {
      chatMainContentRef.value.restoreScrollPosition(scrollPosition)
    }
  })
}
// 确保组件卸载时恢复滚动
onUnmounted(() => {
  document.body.style.overflow = ''
})

// 计算响应高度和宽度
const modalWidth = computed(() => {
  const windowWidth = window.innerWidth
  if (windowWidth < 768) {
    return '95%'
  } // 手机端
  if (windowWidth < 1200) {
    return '90%'
  } // 平板
  return '1200px' // 大屏幕
})
const modalHeight = computed(() => {
  const windowHeight = window.innerHeight
  if (windowHeight < 800) {
    return '600px'
  }
  if (windowHeight < 1000) {
    return '700px'
  }
  return '800px'
})
</script>

<template>
  <!-- 新增包裹容器 -->
  <div
    :style="{
      top: position.y + 'px',
      left: position.x + 'px',
      zIndex: 100,
    }"
    class="ai-assistant-container"
    @mousedown="startDrag"
  >
    <img
      :src="ailogo"
      :style="{
        height: '90px',
        width: '80px',
        cursor: 'move',
        userSelect: 'none',
      }"
      @mouseup="handleMouseUp"
    />

    <!-- 气泡对话框 - 现在相对于容器定位 -->
    <div v-if="showBubble" ref="bubbleRef" class="bubble-dialog" @click="handleBubbleSwitch">
      <!-- 关闭按钮（右上角） -->
      <a-button class="bubble-close-btn" size="small" type="text" @click.stop="showBubble = false">
        <CloseOutlined />
      </a-button>

      <div class="bubble-content">
        <div class="bubble-message">{{ currentBubbleMessage }}</div>
        <div class="bubble-hint">点击和我对话吧</div>
      </div>
      <div class="bubble-arrow"></div>
    </div>
  </div>
  <!-- 模态框（非全屏时显示） -->
  <a-modal
    v-if="!isFullscreen"
    v-model:open="openChatFlag"
    :body-style="{ height: modalHeight, display: 'flex', padding: 0 }"
    :closable="false"
    :width="modalWidth"
    centered
    footer=""
  >
    <template #title>
      <div style="display: flex; justify-content: space-between; align-items: center; height: 32px">
        <span class="title-container">
          {{ titlePrefix }}
          <span class="ai-art">{{ titleHighlight }}</span>
          {{ titleSuffix }}
        </span>
        <span class="env-hint">当前为体验环境，数据均为测试模拟数据！</span>
        <div style="display: flex; gap: 8px">
          <a-button size="small" type="text" @click="toggleFullscreen">
            <template #icon>
              <FullscreenExitOutlined v-if="isFullscreen" />
              <FullscreenOutlined v-else />
            </template>
            <span v-if="!isFullscreen">进入全屏</span>
            <span v-else>退出全屏</span>
          </a-button>
          <a-button size="small" style="color: rgba(0, 0, 0, 0.45)" type="text" @click="openChatClose">
            <template #icon>
              <CloseOutlined />
            </template>
          </a-button>
        </div>
      </div>
    </template>

    <!-- 在非全屏模态框中 -->
    <ConversationList
      :conversations="conversations"
      :current-chat-id="currentChatId"
      :is-new-conversation="isNewConversation"
      @create="createNewConversation"
      @switch="switchConversation"
    />

    <div class="chat-content">
      <ChatMainContent
        ref="chatMainContentRef"
        :abilities="abilities"
        :ailogohead="ailogohead"
        :dialogues="dialogues"
        :is-fullscreen="isFullscreen"
        :loading="loading"
        :user-message="userMessage"
        @preview="handleCloseAndPass"
        @send="sendUserMessage"
        @update:user-message="(val) => (userMessage = val)"
      />
    </div>
  </a-modal>

  <!-- 全屏容器（全屏时显示） -->
  <div v-if="isFullscreen && openChatFlag" class="browser-fullscreen-container">
    <div class="browser-fullscreen-header">
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span class="title-container">
          {{ titlePrefix }}
          <span class="ai-art">{{ titleHighlight }}</span>
          {{ titleSuffix }}
        </span>
        <span class="env-hint">当前为体验环境，数据均为测试模拟数据！</span>
        <div style="display: flex; gap: 8px">
          <a-button size="small" type="text" @click="toggleFullscreen">
            <template #icon>
              <FullscreenExitOutlined />
            </template>
            退出全屏
          </a-button>
          <a-button size="small" style="color: rgba(0, 0, 0, 0.45)" type="text" @click="openChatClose">
            <template #icon>
              <CloseOutlined />
            </template>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 修改内容区域 -->
    <div class="browser-fullscreen-content">
      <ConversationList
        :conversations="conversations"
        :current-chat-id="currentChatId"
        :is-new-conversation="isNewConversation"
        @create="createNewConversation"
        @switch="switchConversation"
      />
      <div class="chat-content">
        <ChatMainContent
          ref="chatMainContentRef"
          :abilities="abilities"
          :ailogohead="ailogohead"
          :dialogues="dialogues"
          :is-fullscreen="isFullscreen"
          :loading="loading"
          :user-message="userMessage"
          @preview="handleCloseAndPass"
          @send="sendUserMessage"
          @update:user-message="(val) => (userMessage = val)"
        />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
/* 全屏容器样式 */
.browser-fullscreen-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  z-index: 1001;
  display: flex;
  flex-direction: column;

  /* 全屏模式下的布局调整 */

  .conversation-list {
    height: 100%;

    &.collapsed {
      width: 60px;
    }
  }

  .browser-fullscreen-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    padding-left: 10px;
  }
}

.browser-fullscreen-header {
  overflow: hidden;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* 艺术字样式 */
.title-container {
  font-size: 18px;
  font-weight: bold;

  &::after {
    content: '（海尔国旅）';
    font-size: 12px;
    color: #8e8e8e;
    margin-left: 4px;
  }
}

.ai-art {
  /* 渐变色彩效果 */
  background: linear-gradient(45deg, #40a9ff, #1890ff, #722ed1);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;

  /* 发光效果 */
  text-shadow: 0 0 8px rgba(24, 144, 255, 0.5);

  /* 动画效果 */
  animation: glow 2s ease-in-out infinite alternate;

  /* 字体增强 */
  font-weight: 800;
  font-size: 1.2em;
  letter-spacing: 1px;
}

/* 发光动画 */
@keyframes glow {
  from {
    text-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
  }
  to {
    text-shadow:
      0 0 15px rgba(24, 144, 255, 0.8),
      0 0 20px rgba(114, 46, 209, 0.6);
  }
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-assistant-container {
  position: fixed;
  width: 80px;
  height: 90px;
}

.bubble-dialog {
  position: absolute;
  top: 0;
  right: 100px;
  z-index: 101;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
  padding: 12px;
  min-width: 180px;
  cursor: pointer;
  animation: slideInFromRight 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  max-width: 360px;
  width: max-content;
  border: 1px solid #f0f0f0;
  background-image: linear-gradient(to bottom, #ffffff, #fafafa);

  .bubble-message {
    word-wrap: break-word;
    white-space: normal;
    line-height: 1.4;
  }

  .bubble-close-btn {
    position: absolute;
    top: 0px;
    right: -2px;
    color: rgba(0, 0, 0, 0.45);
    z-index: 2;
  }

  .bubble-content {
    padding-top: 2px;
    padding-right: 10px;
  }

  .bubble-hint {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 4px;
  }

  .bubble-arrow {
    position: absolute;
    top: 20px;
    left: 100%;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-left: 10px solid white;
    border-right: none;
    filter: drop-shadow(2px 0 2px rgba(0, 0, 0, 0.1));
  }
}

/* 新增的弹跳动画 */
@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
  70% {
    opacity: 1;
    transform: translateX(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 悬停效果 */
.bubble-dialog:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-3px);
  transition: all 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 测试环境提示
.env-hint {
  font-weight: 500;
  display: inline-block;
  color: red;
  animation:
    enter 0.6s ease-in-out forwards,
    exit 0.6s ease-in-out 5.6s forwards;
}

@keyframes enter {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes exit {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}
</style>
