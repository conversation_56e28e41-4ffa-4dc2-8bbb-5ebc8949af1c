<script lang="ts" setup>
import { <PERSON><PERSON> as h<PERSON><PERSON>on, Popover as hPopover } from 'ant-design-vue'
import type { MenuInfo, MenuItemType } from 'ant-design-vue/lib/menu/src/interface'

interface Props {
  menuOptions: MenuItemType[]
  onMenuClick: (e: MenuInfo) => void
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <h-popover
    v-if="props.menuOptions.length > 0"
    placement="bottom"
    trigger="hover"
    arrow-point-at-center
  >
    <template #content>
      <h-button
        v-for="option in props.menuOptions"
        :key="option.key"
        style="display: block; width: 100%; text-align: left"
        type="link"
        @click="props.onMenuClick({ key: option.key } as MenuInfo)"
      >
        {{ option.label }}
      </h-button>
    </template>
    <h-button type="link">操作</h-button>
  </h-popover>
</template>

<style lang="less" scoped></style>
