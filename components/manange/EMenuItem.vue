<script lang="ts" setup>
import { MenuItem as hMenuItem, SubMenu as hSubMenu } from 'ant-design-vue'
import { watch } from 'vue'
import { ResourceTypeConstant } from '@haierbusiness-front/common-libs'
import { useRouter } from 'vue-router'
import AntIcon from '@haierbusiness-front/components/IconSelector/AntIcon.vue'

const router = useRouter()
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['select-key', 'open-key'])
const selectedKey = (param: any) => {
  emits('select-key', param)
  if (props?.item?.type === ResourceTypeConstant.PAGE_GROUP.type) {
    emits('open-key', [props?.item])
  }
}

const openKey = (param: any) => {
  emits('open-key', [...param, props?.item])
}

if (props?.item?.type === ResourceTypeConstant.PAGE_MENU.type) {
  let hashUrl = window.location.hash
  if (hashUrl.includes('?')) {
    hashUrl = hashUrl.substring(0, hashUrl.indexOf('?'))
  }
  if (!hashUrl.endsWith('/')) {
    hashUrl = hashUrl + '/'
  }
  let path: string = props.item.url
  if (!path.startsWith('#')) {
    path = '#' + path
  }
  if (!path.endsWith('/')) {
    path = path + '/'
  }
  const replacePath = path.indexOf('?') > 0 ? path.substring(0, path.indexOf('?')) : path
  if (hashUrl.startsWith(replacePath)) {
    emits('select-key', props?.item)
  }
}
watch(
  () => router.currentRoute.value.path,
  () => {
    // 获取所有路由
    if (props?.item?.type === ResourceTypeConstant.PAGE_MENU.type) {
      let hashUrl = window.location.hash
      if (hashUrl.includes('?')) {
        hashUrl = hashUrl.substring(0, hashUrl.indexOf('?'))
      }
      if (!hashUrl.endsWith('/')) {
        hashUrl = hashUrl + '/'
      }
      let path: string = props.item.url
      if (!path.startsWith('#')) {
        path = '#' + path
      }
      if (!path.endsWith('/')) {
        path = path + '/'
      }
      const replacePath = path.indexOf('?') > 0 ? path.substring(0, path.indexOf('?')) : path
      if (hashUrl.startsWith(replacePath)) {
        emits('select-key', props?.item)
      }
    }
  },
  { immediate: true },
)
</script>
<template>
  <h-sub-menu v-if="item?.type === ResourceTypeConstant.PAGE_GROUP.type" :key="item?.id">
    <template #icon>
      <AntIcon v-if="item?.menuIcon" :name="item?.menuIcon" :size="16" />
      <AntIcon v-else :name="'HolderOutlined'" :size="16" />
    </template>
    <template #title>{{ item.name }}</template>
    <e-menu-item
      v-for="i of item?.children"
      :item="i"
      @select-key="selectedKey"
      @open-key="openKey"
      :key="i"
    ></e-menu-item>
  </h-sub-menu>
  <h-menu-item v-if="item?.type === ResourceTypeConstant.PAGE_MENU.type" :key="item?.id">
    <div class="menu-item-content">
      <AntIcon v-if="item?.menuIcon" :name="item?.menuIcon" :size="16" style="margin-right: 5px" />
      <AntIcon v-else :name="'HolderOutlined'" :size="16" style="margin-right: 5px" />
      <router-link :target="item?.url.indexOf('board') > -1 ? '_blank' : ''" :to="item?.url"
        >{{ item.name }}
      </router-link>
    </div>
  </h-menu-item>
</template>

<style lang="less" scoped>
.menu-item-content {
  display: flex;
  width: 100%;
  align-items: center;
}
</style>
