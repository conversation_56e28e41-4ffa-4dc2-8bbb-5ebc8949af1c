<script lang="ts" setup>
import EMenuItem from '@haierbusiness-front/components/manange/EMenuItem.vue'
import { Menu as hMenu } from 'ant-design-vue'
import { ref } from 'vue'
import { themeColor } from '@haierbusiness-front/utils'

defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

const clean = () => {
  selectedKeys.value = []
  openKeys.value = []
}
defineExpose({ clean })
const selectedKey = (param: any) => {
  selectedKeys.value = [param.id]
}
const openKey = (param: any[]) => {
  const opens = [] as Array<string>
  for (let open of param) {
    opens.push(open.id)
  }
  openKeys.value = opens
}
</script>

<template>
  <h-menu v-model:open-keys="openKeys" v-model:selected-keys="selectedKeys" :theme="themeColor.isDark" mode="inline">
    <e-menu-item v-for="i of list" :item="i" @select-key="selectedKey" @open-key="openKey" :key="i"></e-menu-item>
  </h-menu>
</template>
