<script lang="ts" setup>
import type { Ref } from 'vue'
import { onMounted, ref, watch } from 'vue'
import { userApi } from '@haierbusiness-front/apis'
import { debounce } from 'lodash'
import { IUserInfo, IUserListRequest } from '@haierbusiness-front/common-libs'
import { guid } from '@haierbusiness-front/utils'
import { userStore } from '@haierbusiness-front/utils/src/store/user'

const store = userStore()

interface Props {
  value: string | Array<string>
  params: IUserListRequest
  placeholder?: string
  cacheKey?: string
  multiple?: boolean
  size?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  placeholder: '请选择',
  multiple: false,
  disabled: false,
  cacheKey: undefined,
  size: undefined,
})

const placeholder = ref(props.placeholder)

const defaultParams: IUserListRequest = {}

const params: Ref<IUserListRequest> = ref((props.params as IUserListRequest) || defaultParams)

// 查询用户
const getUserList = async (isScroll: boolean = false) => {
  loading.value = true
  // 判断是否需要缓存   如果需要取缓存的用户放到最前边
  let cacheUsers: Array<IUserInfo> = []
  if (props.cacheKey && !params.value.keyWord) {
    cacheUsers = store.getSelectUsersByCacheKey(props.cacheKey)
  }
  const data = await userApi.list(params.value)
  console.log(data.records, 'data.records')

  if (isScroll) {
    // 下拉刷新
    userSelect.value = data.records ? [...userSelect.value, ...data.records] : userSelect.value
  } else {
    const users = data.records ?? []
    userSelect.value = [...cacheUsers, ...users]
  }
  total.value = data.total ?? 0
  loading.value = false
}

const userSelect = ref<Array<IUserInfo>>([])
const id = guid()

onMounted(async () => {
  const element = document.getElementById(id)
  const x = element!.getBoundingClientRect().left
  const screenWidth = window.screen.width
  if (x + 600 > screenWidth) {
    placement.value = 'bottomRight'
  } else {
    placement.value = 'bottomLeft'
  }
})

const placement = ref<'bottomLeft' | 'bottomRight' | 'topLeft' | 'topRight'>('bottomLeft')

const emit = defineEmits(['change', 'blur', 'deselect'])
// 多选选中的选项
const selectArray = ref<Array<string>>([])
defineExpose({
  setFirstData(res: any) {
    console.log(res, 'res')
    selectArray.value = res
  },
})

// 完全重写onChange函数，更简单直接地处理选择逻辑
const onChange = (value: any | undefined) => {
  console.log('UserSelect onChange触发，value:', value, '当前选中:', selectArray.value)
  // 单选模式
  if (!props.multiple) {
    if (!value) {
      emit('change', value)
      return
    }
    const user = userSelect.value.find((o) => o.username === value)
    if (props.cacheKey && user) {
      store.setSelectUsers(props.cacheKey, user)
    }
    emit('change', user)
    params.value.keyWord = ''
    return
  }

  // 多选模式

  // 处理清空选择的情况
  if (!value || !value.length) {
    selectArray.value = []
    emit('change', [])
    return
  }

  // 对比当前选择和之前选择，判断是添加还是删除
  const valueArray = Array.isArray(value) ? value : [value]

  // 检测删除操作
  if (valueArray.length < selectArray.value.length) {
    console.log('检测到删除操作，原值:', selectArray.value, '新值:', valueArray)

    // 直接使用新值更新选择数组
    selectArray.value = [...valueArray]

    // 转换为用户对象数组
    const users: any[] = []
    valueArray.forEach((username) => {
      const user = userSelect.value.find((o) => o.username === username)
      if (user) {
        users.push(user)
      } else {
        users.push(username)
      }
    })

    // 发送更新后的选择结果
    emit('change', users)
    console.log(users, 'delete')
  }
  // 处理添加操作
  else if (valueArray.length > selectArray.value.length) {
    // 获取新添加的项
    const newValue = valueArray[valueArray.length - 1]
    console.log(newValue, 'newValue')

    // 如果是已选中项，则移除
    if (selectArray.value.includes(newValue)) {
      selectArray.value = selectArray.value.filter((item) => item !== newValue)
    }
    // 否则添加到选中项
    else {
      selectArray.value.push(newValue)
    }

    // 转换为用户对象数组
    const users: any[] = []
    selectArray.value.forEach((username) => {
      const user = userSelect.value.find((o) => o.username === username)
      if (user) {
        users.push(user)
      } else {
        users.push(username)
      }
    })

    // 更新缓存
    if (props.cacheKey && users.length > 0) {
      store.setSelectUsers(props.cacheKey, users as unknown as IUserInfo)
    }

    // 发送更新后的选择结果
    emit('change', users)
    console.log(users, 'add')
  }

  // 清空搜索关键词
  params.value.keyWord = ''
}

const handleSearch = debounce(async (val: string) => {
  params.value.keyWord = val
  params.value.pageNum = 1
  params.value.pageSize = 20
  await getUserList()
}, 500)

const loading = ref<boolean>(false)
const total = ref<number>(0)

const popupScroll = (e: any) => {
  const { clientHeight, scrollHeight, scrollTop } = e.target
  // 判断一下滚动条
  if (scrollHeight - scrollTop <= clientHeight + 100 && !loading.value) {
    // 计算一下当前所查分页是否还有数据，如果小于总数则查，大于则不查，防抖节流，减少服务器损耗
    if (params.value.pageNum! * 10 < total.value) {
      params.value.pageNum! += 1
      getUserList(true)
    }
  }
}

const select = ref()

let isUpdating = false // 应初始化为false

watch(
  () => props.value,
  (newData) => {
    if (newData && !isUpdating) {
      console.log(props.value, 'console.log(props.value);')
      isUpdating = true
      onChange(selectArray.value)
      console.log('监听：', selectArray.value)
    }
  },
  {
    immediate: true,
    flush: 'post',
  },
)
</script>

<template>
  <a-config-provider
    :theme="{
      token: {
        borderRadius: 2,
      },
    }"
  >
    <a-select
      :id="id"
      :default-active-first-option="false"
      :disabled="props.disabled"
      :dropdown-style="{ minWidth: '600px', maxWidth: '600px' }"
      :filter-option="false"
      :mode="props.multiple ? 'multiple' : ''"
      :placeholder="placeholder"
      :placement="placement"
      :size="props.size ? props.size : 'default'"
      :value="props.value ?? ''"
      allow-clear
      class="row"
      option-label-prop="children"
      show-search
      @blur="inputBlur"
      @change="onChange"
      @focus="getUserList()"
      @search="handleSearch"
      @popup-scroll="popupScroll"
    >
      <a-select-option class="userHeaderForSelect" disabled value="0">
        <div ref="select" class="userHeader">
          <div class="no">账号</div>
          <div class="name">姓名</div>
          <div class="email">部门</div>
        </div>
      </a-select-option>
      <a-select-option
        v-for="(item, index) in userSelect"
        :key="index"
        :label="item.nickName"
        :style="{ 'background-color': selectArray.includes(item.username) ? '#e6f4ff' : '' }"
        :title="item.nickName"
        :value="item.username"
      >
        <div class="userHeader">
          <div class="no">
            {{ item.username }}
          </div>
          <div class="name">
            {{ item.nickName }}
          </div>
          <div class="email">
            {{ item.departmentName }}
          </div>
        </div>
      </a-select-option>
    </a-select>
  </a-config-provider>
</template>

<style lang="less" scoped>
.row {
  width: 100%;
}

.userHeader {
  display: flex;
  width: 100%;
  flex-direction: row;
  color: #262626;

  .no {
    display: flex;
    flex: 1;
  }

  .phone {
    display: flex;
    flex: 1;
  }

  .name {
    display: flex;
    flex: 1;
  }

  .email {
    display: flex;
    flex: 1;
  }
}
</style>
