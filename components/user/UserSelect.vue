<script lang="ts" setup>
import type { Ref } from 'vue'
import { onMounted, ref } from 'vue'
import { userApi } from '@haierbusiness-front/apis'
import { debounce } from 'lodash'
import { IUserInfo, IUserListRequest } from '@haierbusiness-front/common-libs'
import { guid } from '@haierbusiness-front/utils'
import { userStore } from '@haierbusiness-front/utils/src/store/user'

const store = userStore()

interface Props {
  value: string | Array<string>
  params: IUserListRequest
  placeholder?: string
  cacheKey?: string
  size?: string
  maxTagCount?: number
  multiple?: boolean
  disabled?: boolean
  hideCode?: string
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  placeholder: '请选择',
  multiple: false,
  maxTagCount: 4,
  disabled: false,
  hideCode: '',
  cacheKey: undefined,
  size: undefined,
})

const placeholder = ref(props.placeholder)

const defaultParams: IUserListRequest = {}

const params: Ref<IUserListRequest> = ref((props.params as IUserListRequest) || defaultParams)

// 查询用户
const getUserList = async (isScroll: boolean = false) => {
  loading.value = true
  // 判断是否需要缓存   如果需要取缓存的用户放到最前边
  let cacheUsers: Array<IUserInfo> = []
  if (props.cacheKey && !params.value.keyWord) {
    cacheUsers = store.getSelectUsersByCacheKey(props.cacheKey)
  }
  const data = await userApi.list(params.value)
  if (isScroll) {
    // 下拉刷新
    userSelect.value = data.records ? [...userSelect.value, ...data.records] : userSelect.value
  } else {
    const users = data.records ?? []
    userSelect.value = [...cacheUsers, ...users]
  }
  total.value = data.total ?? 0
  loading.value = false
}

const userSelect = ref<Array<IUserInfo>>([])
const id = guid()

onMounted(async () => {
  const element = document.getElementById(id)
  const x = element!.getBoundingClientRect().left
  const screenWidth = window.screen.width
  if (x + 600 > screenWidth) {
    placement.value = 'bottomRight'
  } else {
    placement.value = 'bottomLeft'
  }
})

const placement = ref<'bottomLeft' | 'bottomRight' | 'topLeft' | 'topRight'>('bottomLeft')

const emit = defineEmits(['change', 'blur'])
// 多选选中的选项
const selectArray = ref<Array<string>>([])
defineExpose({
  setFirstData(res: any) {
    selectArray.value = res
  },
})
const onChange = (value: any | undefined) => {
  // 是否多选
  if (!props.multiple) {
    if (!value) {
      emit('change', value)
      return
    }
    const user = userSelect.value.find((o) => o.username === value)
    if (props.cacheKey && user) {
      store.setSelectUsers(props.cacheKey, user)
    }
    emit('change', user)
    params.value.keyWord = ''
  } else {
    console.log(value, selectArray.value)
    if (!value || !value.length) {
      emit('change', [])
      selectArray.value = []
      return
    }
    if (value.length < selectArray.value.length) {
      // 点tag叉号
      selectArray.value = []
      value.forEach((item: string) => {
        selectArray.value.push(item.split('/')[1])
      })
    } else {
      // 判断是否是已经选中
      if (selectArray.value.includes(value[value.length - 1])) {
        selectArray.value = selectArray.value.filter((item) => item !== value[value.length - 1])
      } else {
        selectArray.value.push(value[value.length - 1])
      }
    }
    const user: Array<IUserInfo> = []
    selectArray.value.forEach((item: string) => {
      if (userSelect.value.find((o) => o.username === item)) {
        user.push(userSelect.value.find((o) => o.username === item)!)
      } else {
        user.push(item)
      }
    })
    if (props.cacheKey && user) {
      store.setSelectUsers(props.cacheKey, user)
    }
    emit('change', user)
    params.value.keyWord = ''
  }
}

const handleSearch = debounce(async (val: string) => {
  params.value.keyWord = val
  params.value.pageNum = 1
  params.value.pageSize = 20
  await getUserList()
}, 500)

const loading = ref<boolean>(false)
const total = ref<number>(0)

const popupScroll = (e) => {
  const { clientHeight, scrollHeight, scrollTop } = e.target
  // 判断一下滚动条
  if (scrollHeight - scrollTop <= clientHeight + 100 && !loading.value) {
    // 计算一下当前所查分页是否还有数据，如果小于总数则查，大于则不查，防抖节流，减少服务器损耗
    if (params.value.pageNum! * 10 < total.value) {
      params.value.pageNum! += 1
      getUserList(true)
    }
  }
}

const select = ref()

const inputBlur = () => {
  emit('blur')
}
</script>

<template>
  <a-config-provider
    :theme="{
      token: {
        borderRadius: 2,
      },
    }"
  >
    <a-select
      :id="id"
      :default-active-first-option="false"
      :disabled="props.disabled"
      :dropdown-style="{ minWidth: '600px', maxWidth: '600px' }"
      :filter-option="false"
      :max-tag-count="props.maxTagCount"
      :mode="props.multiple ? 'multiple' : ''"
      :placeholder="placeholder"
      :placement="placement"
      :size="props.size ? props.size : 'default'"
      :value="props.value ?? ''"
      allow-clear
      class="row"
      option-label-prop="children"
      show-search
      @blur="inputBlur"
      @change="onChange"
      @focus="getUserList()"
      @search="handleSearch"
      @popup-scroll="popupScroll"
    >
      <a-select-option class="userHeaderForSelect" disabled value="0">
        <div ref="select" class="userHeader">
          <div class="no">账号</div>
          <div class="name">姓名</div>
          <div class="email">部门</div>
        </div>
      </a-select-option>
      <template v-for="(item, index) in userSelect" :key="index">
        <a-select-option
          v-if="item.username != props.hideCode"
          :label="item.nickName"
          :style="{ 'background-color': selectArray.includes(item.username) ? '#e6f4ff' : '' }"
          :title="item.nickName"
          :value="item.username"
        >
          <div class="userHeader">
            <div class="no">
              {{ item.username }}
            </div>
            <div class="name">
              {{ item.nickName }}
            </div>
            <div class="email">
              {{ item.departmentName }}
            </div>
          </div>
        </a-select-option>
      </template>
    </a-select>
  </a-config-provider>
</template>

<style lang="less" scoped>
.row {
  width: 100%;
}

.userHeader {
  display: flex;
  width: 100%;
  flex-direction: row;
  color: #262626;

  .no {
    display: flex;
    flex: 1;
  }

  .phone {
    display: flex;
    flex: 1;
  }

  .name {
    display: flex;
    flex: 1;
  }

  .email {
    display: flex;
    flex: 1;
  }
}
</style>
