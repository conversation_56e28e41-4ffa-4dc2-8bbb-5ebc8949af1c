<script lang="ts" setup>
import { Progress as hProgress } from 'ant-design-vue'
import { ref, watch } from 'vue'

const props = defineProps({
  time: {
    type: Number,
    default: 0,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
const loadInt = ref()
const percent = ref(0)

const loadingFlag = ref(false)
watch(
  () => props.loading,
  (n: any) => {
    if (n === true) {
      loadingFlag.value = true
      eLoadingStart()
    } else {
      eLoadingEnd()
      setTimeout(() => {
        loadingFlag.value = false
      }, 500)
    }
  },
)

const eLoadingStart = () => {
  let count = 0
  let step = 1
  loadInt.value = setInterval(() => {
    if (percent.value < 40) {
      count += 50
      step = 5
    } else if (percent.value < 60) {
      count += 35
      step = 4
    } else if (percent.value < 80) {
      count += 20
      step = 2
    } else if (percent.value < 90) {
      count += 10
      step = 1
    } else if (percent.value < 95) {
      count += 3
      step = 1
    } else {
      count += 1
      step = 1
    }
    if (count >= 100) {
      count = 0
      while (step >= 1) {
        if (percent.value !== 99) {
          percent.value++
        } else {
          clearInterval(loadInt.value)
        }
        step--
      }
      step
    }
  }, 100)
}

const eLoadingEnd = () => {
  percent.value = 100
  clearInterval(loadInt.value)
  setTimeout(() => {
    percent.value = 0
  }, 500)
}
</script>

<template>
  <div v-if="loadingFlag" class="masking" style="padding-top: 15%; padding-left: 40%">
    <div style="height: 160px; width: 160px; background-color: #ffffff; border-radius: 80px">
      <h-progress
        :percent="percent"
        :stroke-color="{
          '0%': '#0073E5',
          '100%': '#87d068',
        }"
        style="padding-left: 20px; padding-top: 20px"
        type="circle"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
