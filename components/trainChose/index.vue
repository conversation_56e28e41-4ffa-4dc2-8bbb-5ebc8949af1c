<template>
  <a-popover
    v-model:open="visible"
    :title="trainOptions[0]?.cityName"
    class="city-popover"
    placement="bottomLeft"
    trigger="click"
  >
    <template #content>
      <div class="city-main-box">
        <!-- 默认城市三字码 -->
        <a-tabs v-model:active-key="activeKey">
          <a-tab-pane v-for="tab in trainOptions[0]?.lowerList" :key="tab.cityId" :tab="tab.cityName">
            <a-list :data-source="tab?.zdList" style="height: 320px; overflow-y: scroll">
              <template #renderItem="{ item }">
                <a-list-item style="cursor: pointer" @click="trainChose(item)">
                  <div>{{ item.stationName }}</div>
                  <div>{{ item.stationCode }}</div>
                </a-list-item>
              </template>
            </a-list>
          </a-tab-pane>
        </a-tabs>
      </div>
    </template>
    <a-input :allow-clear="true" :bordered="props.bordered" :value="props.value" placeholder="选择火车站" readonly />
  </a-popover>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { cityApi } from '@haierbusiness-front/apis'

const emit = defineEmits(['chosed'])

interface Props {
  value: string
  cityId: string
  bordered?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  cityId: '',
  bordered: true,
})

const visible = ref<boolean>(false)
const activeKey = ref<string>('')

const trainOptions = ref([])

const trainChose = (ZD: any) => {
  visible.value = false
  emit('chosed', ZD)
}

const trainStationGroupingByCityId = (cityId) => {
  const param = {
    cityId: cityId,
  }
  // 查询火车三字码列表
  cityApi.trainStationGroupingByCityId(param).then((res) => {
    trainOptions.value = res || []
    activeKey.value = trainOptions.value[0]?.lowerList[0]?.cityId
    // 默认自动选择第一个人站点
    trainChose(trainOptions.value[0]?.mainStation)
  })
}

watch(
  () => props.cityId,
  (N) => {
    if (N) {
      trainStationGroupingByCityId(N)
    }
  },
  { immediate: true },
)
</script>

<style lang="less" scoped>
.city-main-box {
  width: 600px;
}

.city-list {
  .city-box {
    display: flex;

    .box-left {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 10%;
      color: orange;
      font-size: 20px;
    }

    .box-right {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
    }
  }
}
</style>
