<script lang="ts" setup>
import {
  Avatar as hAvatar,
  <PERSON><PERSON> as hB<PERSON>on,
  <PERSON> as hCol,
  Dropdown as hDropdown,
  Layout as hLayout,
  Layout<PERSON>eader as hLayoutHeader,
  <PERSON>u as hMenu,
  MenuItem as hMenuItem,
  Result as hResult,
  Row as hRow,
} from 'ant-design-vue'
import { computed } from 'vue'
import { HeaderConstant } from '@haierbusiness-front/common-libs'

import { loginApi } from '@haierbusiness-front/apis'
import { removeStorageItem } from '@haierbusiness-front/utils'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton'
import logo from '@/assets/image/logo.png'

const router = useRouter()

const store = applicationStore()
const { loginUser, loginUrl } = storeToRefs(store)

const avatarName = computed(() => {
  if (loginUser?.value?.nickName) {
    const nickName = loginUser.value.nickName
    if (nickName.length === 2) {
      return nickName
    } else if (nickName.length > 2 && nickName.length <= 5) {
      return nickName.substring(nickName.length - 2, nickName.length)
    } else {
      return nickName.substring(0, 1).toUpperCase()
    }
  } else {
    return '无名'
  }
})
const back = () => {
  router.go(-2)
}
const getBackUrl = () => {
  let backHash = router.options.history.state.back
  let backUrl = window.location.origin + window.location.pathname + window.location.search + '#' + backHash
  return backUrl
}
const logout = () => {
  loginApi.haierIamTokenLogout({ token: loginUser?.value?.extended?.iamToken }).finally(() => {
    removeStorageItem(HeaderConstant.TOKEN_KEY.key, false)
    window.location.href = loginUrl.value + '&redirect_url=' + encodeURIComponent(getBackUrl())
    // window.location.reload()
  })
}
</script>
<template>
  <h-layout style="height: 100vh; min-height: 100px">
    <h-layout-header class="header">
      <h-row style="line-height: 48px">
        <h-col :span="3">
          <div class="logo-div">
            <img :src="logo" class="logo-img" />
          </div>
        </h-col>
        <h-col :offset="18" :span="3" style="line-height: 10px">
          <div style="width: 100%; height: 100%; text-align: right; padding-top: 4px">
            <h-dropdown :trigger="['click']">
              <a @click.prevent>
                <h-avatar
                  :title="loginUser?.username"
                  size="large"
                  style="color: #f56a00; background-color: #fde3cf; user-select: none"
                  >{{ avatarName }}
                </h-avatar>
              </a>
              <template #overlay>
                <h-menu>
                  <h-menu-item key="0">
                    <h-button type="link" @click="logout">登出</h-button>
                  </h-menu-item>
                </h-menu>
              </template>
            </h-dropdown>
          </div>
        </h-col>
      </h-row>
    </h-layout-header>
    <h-layout style="height: 90%; margin-top: 2%">
      <h-result
        :title="getBackUrl()"
        status="404"
        sub-title="找不当前页面或没有当前页面访问权限！请返回上一页或尝试退出重新登录！"
      >
        <template #extra>
          <h-button type="primary" @click="back">返回上一页</h-button>
          <h-button danger type="primary" @click="logout">重新登录</h-button>
        </template>
      </h-result>
    </h-layout>
  </h-layout>
</template>
<style lang="less" scoped>
.header {
  height: 48px;
  background-color: #ffffff;
}

.logo-div {
  text-align: center;
  width: 92%;
  height: 48px;

  .logo-img {
    height: 26px;
    object-fit: cover;
  }
}

.trigger {
  font-size: 18px;
  line-height: 44px;
  padding: 3px 24px 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.navigation-button {
  padding-right: 12px;
  cursor: pointer;

  :hover {
    color: #0073e5;
  }
}
</style>
