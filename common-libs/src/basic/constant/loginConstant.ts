/**
 * 登录类型
 * 1: 本地用户名密码登录
 * 2: 海尔统一code登录
 *      示例 : http://127.0.0.1:5173/?login_type=2&application_code=haierbusiness-wyy&redirect_url=http%3A%2F%2F127.0.0.1%3A5174%2F%3FapplicationCode%3Dhaierbusiness-wyy%26username%3D01347713%26enterpriseCode%3Dhaier%26orderCode%3DDZQB123457%26providerCode%3Dwyy%26amount%3D0.01%26notifyUrl%3Dhttp%253A%252F%252Fs03274-wyy-v1-s03274.paas-sit.haier.net%252Ftravel-yanxuan-mall%252Fapp-api%252Fyxmall%252Fpay%252Fnotify%26callbackUrl%3Dhttp%253A%252F%252F127.0.0.13%253A5173%252F%2523%252Forder%252Ffinish%253ForderId%253D202303036976000004%26description%3D%25E7%25BD%2591%25E6%2598%2593%25E4%25BA%2591%25E5%2595%2586%25E5%2593%2581%26hbTimestamp%3D1679211048%26hbNonce%3De55200a3-4a82-4f5e-96ab-a09bfce736c8%26sign%3DIjQl8Jn5kC45UJxGJvyrGaGGJ6xtgOPjTmN2PMmLfaOggDnV7Nc1cvjVQ4gR7dzpTUJmeMLw4ZxMKo6oi%252Fe5EoegpmHuCF10ExWhMft8R81g1j%252BszecEJNOf%252BJ9aqFo%252F7OVe8vhGFHycmfPEpzrDzIiCkVG%252F0NGz%252BGmT7RtVeJk%253D#/index
 * 3: 微信登录
 * 4: 手机号登录
 * 5: 海尔统一token登录
 * 6: 生态系统session登录
 * 7: 超市系统token登录
 */
export const LoginTypeConstant = {
  LOCAL: { key: 1, name: '本地用户名密码登录' },
  IAM_CODE: { key: 2, name: '海尔统一code登录' },
  WECHAT: { key: 3, name: '微信登录' },
  PHONE: { key: 4, name: '手机号登录' },
  /**
   *
   *      * 海尔统一token登录
   *      * 这种登录不再建议使用，集团iam_token在任何系统退出后，校验都会返回false。导致两个系统间用户不同步
   *
   *     @Deprecated
   */
  IAM_TOKEN: { key: 5, name: '海尔统一token登录' },
  TRAVEL_SESSION: { key: 6, name: '生态系统session登录' },
  SUPERMARKET_TOKEN: { key: 7, name: '超市系统token登录' },
  WYY_TOKEN: { key: 8, name: '网易云系统token登录' },

  USERNAME_TOKEN: { key: 9, name: '用户名工号登录' },
}
