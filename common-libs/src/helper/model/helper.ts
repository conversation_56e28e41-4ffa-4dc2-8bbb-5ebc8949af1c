import { IPageRequest } from '../../basic'

export interface IHelperSearchParam extends IPageRequest {
  pageNum: number
  pageSize: number
  total: number
  onlyMatchFlag?: number
  fromCityCode?: string
  fromCityName?: string
  destCityCode?: string
  destCityName?: string
  expectTimeFrom?: string // 使用字符串表示日期时间
  expectTimeTo?: string // 使用字符串表示日期时间
  objectDesc?: string
  objectType?: number
  creatorPhone?: string
  pickupAddress?: string
  destAddress?: string
  pickupLongitude?: any // 使用 any 表示任意类型
  pickupLatitude?: any // 使用 any 表示任意类型
  destLongitude?: any // 使用 any 表示任意类型
  destLatitude?: any // 使用 any 表示任意类型
  destGeohash?: string
  pickupGeohash?: string
  isDelete?: number
  piggybackStatus?: number | string
  creatorOpenid?: string
  createTime?: string // 使用字符串表示日期时间
  updateUser?: string
  updateTime?: string // 使用字符串表示日期时间
  createUser?: string
  acceptUser?: string
  orderByItemList?: Array<any>
}

// 新增接口
export interface PiggybackFiles {
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 发布人用户编码
   */
  createUser?: string
  /**
   * 文件名
   */
  fileName?: string
  /**
   * 文件url
   */
  fileUrl?: string
  id?: number
  /**
   * 是否删除0未删除1已删除
   */
  isDelete?: number
  /**
   * 捎带需表id
   */
  piggybackId?: number
  /**
   * 更新时间
   */
  updateTime?: Date
  /**
   * 更新人
   */
  updateUser?: string

  [property: string]: any
}

export interface IHelperReq {
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 发布人用户编码
   */
  createUser?: string
  /**
   * 创建人openid，用于发起会话
   */
  creatorOpenid?: string
  /**
   * 创建人联系电话
   */
  creatorPhone?: string
  /**
   * 目标详细地址
   */
  destAddress?: string
  /**
   * 目的地城市编码
   */
  destCityCode?: string
  /**
   * 目的地城市名称
   */
  destCityName?: string
  /**
   * 目的地经纬度编码
   */
  destGeohash?: string
  /**
   * 目的地纬度
   */
  destLatitude?: string
  /**
   * 目的地经度
   */
  destLongitude?: string
  /**
   * 期望时间开始
   */
  expectTimeFrom?: string
  /**
   * 期望时间结束
   */
  expectTimeTo?: string
  /**
   * 目标城市编码
   */
  fromCityCode?: string
  /**
   * 出发城市名称
   */
  fromCityName?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 是否删除0未删除1已删除
   */
  isDelete?: string
  /**
   * 物品详细描述
   */
  objectDesc?: string
  /**
   * 捎带物类型
   */
  objectType?: string
  /**
   * 取货地址
   */
  pickupAddress?: string
  /**
   * 物品所在地经纬度编码
   */
  pickupGeohash?: string
  /**
   * 物品所在地纬度
   */
  pickupLatitude?: string
  /**
   * 物品所在地经度
   */
  pickupLongitude?: string
  /**
   * 接受状态(0:匹配中，1：匹配成功，2：已取消)
   */
  piggybackStatus?: number
  /**
   * 更新时间
   */
  updateTime?: Date
  /**
   * 更新人
   */
  updateUser?: string

  timeRange?: Array<string>
  files?: PiggybackFiles[]
}

export interface IHelperListRes {
  fromCityName?: string
  fromCityCode?: string
  destCityCode?: string
  beginTime?: string
  endTime?: string

  type?: string
}

export interface UserTicketResponseDTO {
  userFlightTicketDTOList: UserFlightTicketDTO[]
  userTrainTicketDTOList: UserTrainTicketDTO[]
}

/**
 * UserFlightTicketDTO
 */
export interface UserFlightTicketDTO {
  cxrYggh?: string
  jpCfcity?: string
  jpCfcityAirPortMc?: string
  jpCfcityMc?: string
  jpCfsj?: string
  jpDdcity?: string
  jpDdcityAirPortMc?: string
  jpDdcityMc?: string
  jpDdsj?: string

  [property: string]: any
}

/**
 * UserTrainTicketDTO
 */
export interface UserTrainTicketDTO {
  cfcs?: string
  cfcsmc?: string
  cfrq?: string
  cfsj?: string
  ddcs?: string
  ddcsmc?: string
  ddrq?: string
  ddsj?: string

  [property: string]: any
}
