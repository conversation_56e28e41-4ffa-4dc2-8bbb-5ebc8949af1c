import { IBaseDO } from '../../../basic/model/baseModel'

export class IDailyDeptSaveRequestDTO {
  /**
   * 同集团组织架构code
   */
  code?: string

  /**
   * 部门名称
   */
  name?: string

  /**
   * 部门小微主
   */
  managerCode?: string

  managerName?: string

  managerMail?: string

  managerPhone?: string
}

export class IDailyDeptUpdateRequestDTO {
  id?: number

  /**
   * 同集团组织架构code
   */
  code?: string

  /**
   * 部门名称
   */
  name?: string

  /**
   * 部门小微主
   */
  managerCode?: string

  managerName?: string

  managerMail?: string

  managerPhone?: string
}

export class IDailyDeptListRequestDTO {
  /**
   * 同集团组织架构code
   */
  code?: string

  /**
   * 部门名称
   */
  name?: string

  /**
   * 部门小微主
   */
  managerCode?: string

  managerName?: string

  managerMail?: string

  managerPhone?: string
}

export class IDailyDeptDeleteRequestDTO {
  id?: number
}

export class IDailyDeptResponse extends IBaseDO {
  id?: number

  /**
   * 同集团组织架构code
   */
  code?: string

  /**
   * 部门名称
   */
  name?: string

  /**
   * 部门小微主
   */
  managerCode?: string

  managerName?: string

  managerMail?: string

  managerPhone?: string
}
