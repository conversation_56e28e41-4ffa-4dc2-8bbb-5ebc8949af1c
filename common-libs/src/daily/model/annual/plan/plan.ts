import { IBaseDO } from '../../../../basic/model/baseModel'
import { IMonthPlanDetailResponseDTO } from '../../month/month'
import { IEvaluateListResponseDTO } from '../../evaluate/evaluate'

export class IAnnualPlanListRequestDTO {
  /**
   * 所属年份，每年每个部门只有一条数据
   */
  year?: number

  /**
   * 部门ID
   */
  deptCode?: string

  /**
   * 部门名称
   */
  deptName?: string

  /**
   */
  state?: string

  /**
   * 年度目标
   */
  target?: string

  /**
   * 年度定位
   */
  orientation?: string
}

export class IAnnualPlanDO extends IBaseDO {
  id?: number

  /**
   * A + dept_code + year + 5位自增编码
   * A50089211202300001
   * code + ver = 0 ， 获取唯一数据
   */
  code?: string

  /**
   * 所属年份，每年每个部门只有一条数据
   */
  year?: number

  /**
   * 目标总数
   */
  itemCount?: number

  /**
   * 已终止数量
   */
  stopItemCount?: number

  /**
   * 已完成数量
   */
  completeItemCount?: number

  /**
   * 示例
   * 基础:10/10,重点:2/5,创新:1/2,挑战:1/1
   */
  typeItemCount?: string

  /**
   * 部门ID
   */
  deptCode?: string

  /**
   * 部门名称
   */
  deptName?: string

  /**
   * annualPlanStateConstant.ts
   */
  state?: number

  stateName?: string

  /**
   * 年度目标
   */
  target?: string

  /**
   * 年度定位
   */
  orientation?: string

  /**
   * 年度需求-审批关联表id
   */
  annualPapId?: number

  /**
   * 年度需求-审批关联表code
   */
  annualPapCode?: string

  /**
   * 年度需求-审批单号
   */
  annualProcessCode?: string

  /**
   * 相同code下版本号，时间戳
   */
  ver?: number

  /**
   * 版本状态 ， 数据始终都应有一条生效版本
   * verStateConstant.ts
   */
  verState?: number
}

export class IMonthPlanDO extends IBaseDO {
  id?: number

  /**
   * M+dept_code + year + month + 5位自增编码
   * M5008921120230100001
   * code + ver = 0 ， 获取唯一数据
   */
  code?: string

  /**
   * 年度计划code
   */
  apCode?: string

  /**
   * 年度计划主表版本
   */
  apVer?: number

  /**
   * 所属年份，每年只有一条数据
   */
  year?: number

  /**
   * 所属月份，各小微每年一条
   */
  month?: number

  /**
   * 目标总数
   */
  itemCount?: number

  /**
   * 已终止数量
   */
  stopItemCount?: number

  /**
   * 已完成数量
   */
  completeItemCount?: number

  /**
   * 示例
   * 基础:10/10,重点:2/5,创新:1/2,挑战:1/1
   */
  typeItemCount?: string

  /**
   * 部门ID
   */
  deptCode?: string

  /**
   * 部门名称
   */
  deptName?: string

  /**
   * 00001 小微主评价   1
   * 00010 战略评价    2
   * 00100 风控评价    4
   * 01000 财务评价    8
   * 10000 人力评价    16
   * == 31 全部完成评价
   */
  evaluateFlag?: number

  /**
   * {@link MonthPlanStateConstant}
   */
  state?: number

  stateName?: string

  /**
   * 需提报总天数，法定工作日天数
   */
  totalDays?: number

  /**
   * 正常提报天数
   */
  normalDays?: number

  /**
   * 异常提报天数，提交人数不满足
   */
  abnormalDays?: number

  /**
   * 人天，需提报总天数，每人需录入天数相加，如果发生离职等人员变动，当前数值不变化。反应在下月中
   */
  personTotalDays?: number

  /**
   * 人天，正常提报天数，包含状态为请假/离职等状态的人
   */
  personNormalDays?: number

  /**
   * 人天，异常提报天数，未提交的
   */
  personAbnormalDays?: number

  /**
   * 相同code下版本号，时间戳
   */
  ver?: number

  /**
   * 版本状态 ， 数据始终都应有一条生效版本
   * {@link VerStateConstant}
   */
  verState?: number
}

export class IAnnualPlanItemDO extends IBaseDO {
  id?: number

  /**
   * AI + dept_code + year + 5位自增编码
   * AI50089211202300001
   * code + ver = 0 ， 获取唯一数据
   */
  code?: string

  /**
   * 年度计划主表code
   */
  apCode?: string

  /**
   * 年度计划主表版本
   */
  apVer?: number

  /**
   * 年度项目名称
   */
  name?: string

  /**
   * 所属年份
   */
  year?: number

  /**
   * 年度项目类型ID
   */
  typeId?: number

  /**
   * 年度项目类型名称
   */
  typeName?: string

  /**
   * 部门ID
   */
  deptCode?: string

  /**
   * 部门名称
   */
  deptName?: string

  /**
   * {@link AnnualPlanItemStateConstant}
   */
  state?: number

  stateName?: string

  /**
   * 1：临时项目。
   * 临时项目是特殊的项目，每年默认为所有部门生成一条，展示处与报表中显示
   */
  temp?: boolean

  /**
   * {@link PlanTypeConstant}
   */
  planType?: number

  /**
   * 年度目标 ，定性必填，定量选填
   */
  planDesc?: string

  /**
   * 年度目标值，定性禁填，定量必填
   */
  planValue?: number

  /**
   * 年度目标单位，定性禁填，定量必填
   */
  planUnit?: string

  /**
   * 完成计划日期
   */
  completePlanTime?: string

  /**
   * 完成效果 ，定性必填，定量选填
   */
  completePlanDesc?: string

  /**
   * 完成量
   */
  completePlanValue?: number

  /**
   * 相同code下版本号，时间戳
   */
  ver?: number

  /**
   * 版本状态 ， 数据始终都应有一条生效版本
   * {@link VerStateConstant}
   */
  verState?: number
}

export class IMonthPlanItemDO extends IBaseDO {
  id?: number

  /**
   * MI+dept_code + year + month + 5位自增编码
   * MI5008921120230100001
   * code + ver = 0 ， 获取唯一数据
   */
  code?: string

  /**
   * 年度计划主表code
   */
  apCode?: string

  /**
   * 年度计划主表版本
   */
  apVer?: number

  /**
   * 年度项目表id
   */
  apiCode?: string

  /**
   * 年度项目主表版本
   */
  apiVer?: number

  /**
   * 年度项目名称
   */
  apiName?: string

  /**
   * 月度计划id
   */
  mpCode?: string

  /**
   * 月度计划主表版本
   */
  mpVer?: number

  /**
   * 所属年份
   */
  year?: number

  /**
   * 所属月份
   */
  month?: number

  /**
   * 责任人
   */
  principalUsercode?: string

  /**
   * 责任人姓名
   */
  principalUsername?: string

  /**
   * 责任人部门
   */
  principalDeptCode?: string

  principalDeptName?: string

  /**
   * 年度项目类型ID
   */
  apTypeId?: number

  /**
   * 年度项目类型
   */
  apTypeName?: string

  /**
   * 部门ID
   */
  deptCode?: string

  /**
   * 部门名称
   */
  deptName?: string

  /**
   * {@link MonthPlanItemStateConstant}
   */
  type?: number

  typeName?: string

  /**
   * 1：临时项目。
   * 临时项目是特殊的项目，每月默认为所有部门生成一条,api_id为年度临时项目id，展示处与报表中显示
   */
  temp?: boolean

  /**
   * {@link PlanTypeConstant}
   */
  planType?: number

  /**
   * 必填（不可修改）
   */
  planDesc?: string

  /**
   * 月度目标值，定性禁填，定量必填
   */
  planValue?: number

  /**
   * 月度目标单位，定性禁填，定量必填
   */
  planUnit?: string

  /**
   * {@link MonthPlanItemStateConstant}
   */
  state?: number

  stateName?: string

  /**
   * 完成效果 ，定性必填，定量选填
   */
  completePlanDesc?: string

  /**
   * 完成计划日期
   */
  completePlanTime?: string

  /**
   * 完成量，定性禁填，定量必填
   */
  completePlanValue?: number

  /**
   * 完成率，最大可超过1
   */
  completeRate?: number

  /**
   * 相同code下版本号，时间戳
   */
  ver?: number

  /**
   * 版本状态 ， 数据始终都应有一条生效版本
   * {@link VerStateConstant}
   */
  verState?: number
}

export class IAnnualPlanListResponseDTO extends IAnnualPlanDO {
  /**
   * 历史版本
   */
  historyAnnualPlans?: IAnnualPlanDO[]

  /**
   * 预备版本
   */
  waitAnnualPlan?: IAnnualPlanDO
}

export class IAnnualPlanSaveOrUpdateRequestDTO {
  /**
   * 修改时id
   */
  id?: number

  /**
   * 所属年份，每年相同部门只有一条数据
   */
  year?: number

  /**
   * 年度目标
   */
  target?: string

  /**
   * 年度定位
   */
  orientation?: string

  /**
   * 部门ID
   */
  deptCode?: string

  /**
   * 部门名称
   */
  deptName?: string

  /**
   * 年度计划
   */
  items?: IAnnualPlanUpdateItemRequestDTO[]
}

export class IAnnualPlanUpdateItemRequestDTO {
  /**
   * 年度项目Code
   */
  id?: number

  /**
   * 删除标记
   */
  isDeleted?: boolean

  /**
   * 年度项目名称
   */
  name?: string

  /**
   * 年度项目类型ID
   */
  typeId?: number

  /**
   * 年度项目类型
   */
  typeName?: string

  /**
   * {@link PlanTypeConstant}
   */
  planType?: number

  /**
   * 年度目标描述 ，定性必填，定量选填
   */
  planDesc?: string

  /**
   * 年度目标值，定性禁填，定量必填
   */
  planValue?: number

  /**
   * 年度目标单位，定性禁填，定量必填
   */
  planUnit?: string

  /**
   * 月度分解
   */
  monthPlans?: IAnnualPlanUpdateMonthPlanRequestDTO[]
}

export class IAnnualPlanUpdateMonthPlanRequestDTO {
  /**
   * 月度项目code
   */
  id?: string

  /**
   * 删除标记
   */
  isDeleted?: boolean

  /**
   * 所属月份
   */
  month?: number

  /**
   * {@link MonthPlanItemStateConstant}
   * 月度计划项目状态
   */
  state?: number

  /**
   * 年度项目类型ID
   */
  apTypeId?: number

  /**
   * 年度项目类型
   */
  apTypeName?: string

  /**
   * 责任人
   */
  principalUsercode?: string

  /**
   * 责任人姓名
   */
  principalUsername?: string

  /**
   * {@link MonthPlanItemTypeConstant}
   * 项目类型
   */
  type?: number
  typeName?: string

  /**
   * {@link PlanTypeConstant}
   */
  planType?: number

  /**
   * 月度目标必填
   */
  planDesc?: string

  /**
   * 月度目标值，定性禁填，定量必填
   */
  planValue?: number

  /**
   * 月度目标单位，定性禁填，定量必填
   */
  planUnit?: string

  /**
   * 完成效果 ，定性必填，定量选填
   */
  completePlanDesc?: string

  /**
   * 完成计划日期
   */
  completePlanTime?: string

  /**
   * 完成量，定性禁填，定量必填
   */
  completePlanValue?: number

  /**
   * 完成率，最大可超过1
   */
  completeRate?: number

  /**
   * 评价ID
   */
  evaluateId?: number

  /**
   * 评价金额
   */
  evaluateAmount?: number

  /**
   * 评价备注
   */
  evaluateRemark?: string
}

export class IAnnualPlanUpdateRequestDTO {
  /**
   * 年度计划ID
   */
  id?: number

  /**
   * 年度目标
   */
  target?: string

  /**
   * 年度定位
   */
  orientation?: string

  /**
   * 年度计划
   */
  items?: IAnnualPlanUpdateItemRequestDTO[]
}

export class IAnnualPlanAddRequestDTO {
  /**
   * 年度计划ID
   */
  id?: number

  /**
   * 年度计划
   */
  items?: IAnnualPlanAddItemRequestDTO[]
}

export class IAnnualPlanAddItemRequestDTO {
  /**
   * 年度计划ID
   */
  id?: number

  /**
   * 月度分解
   */
  items?: IAnnualPlanUpdateMonthPlanRequestDTO[]
}

export class IAnnualPlanUpdateCloseRequestDTO {
  /**
   * 年度计划ID
   */
  id?: number

  /**
   * 月度计划
   */
  items?: IMonthPlanUpdateCloseItemRequestDTO[]
}

export class IMonthPlanUpdateCloseItemRequestDTO {
  /**
   * 年度计划ID
   */
  id?: number

  /**
   * 月度分解
   */
  items?: IAnnualPlanUpdateCloseMonthPlanRequestDTO[]
}

export class IAnnualPlanUpdateCloseMonthPlanRequestDTO {
  /**
   * 月度项目id
   */
  id?: number

  /**
   * {@link MonthPlanItemStateConstant}
   */
  state?: number

  /**
   * 完成效果 ，定性必填，定量选填
   */
  completePlanDesc?: string

  /**
   * 完成计划日期
   */
  completePlanTime?: string

  /**
   * 完成量，定性禁填，定量必填
   */
  completePlanValue?: number

  /**
   * 完成率，最大可超过1
   */
  completeRate?: number

  /**
   * 评价ID
   */
  evaluateId?: number

  /**
   * 评价金额
   */
  evaluateAmount?: number

  /**
   * 评价备注
   */
  evaluateRemark?: string
}

export class IAnnualPlanSubmitRequestDTO {
  /**
   * 年度计划ID
   */
  id?: number
}

export class IAnnualPlanDetailRequestDTO {
  /**
   * 年度计划ID
   */
  id?: number

  /**
   * code查询,优先查询生效版本,其次查询待生效版本
   */
  code?: string

  /**
   * 评价控制
   *  {@link EvaluateControlConstant}
   */
  evaluateControl?: number
}

export class IAnnualPlanDetailResponseDTO extends IAnnualPlanDO {
  /**
   * 月度计划
   */
  monthPlans?: IMonthPlanDetailResponseDTO[]

  /**
   * 年度项目
   */
  annualPlanItems?: IAnnualPlanItemDetailResponseDTO[]
}

export class IAnnualPlanItemDetailResponseDTO extends IAnnualPlanItemDO {
  /**
   * 月度项目
   */
  monthPlanItems?: IMonthPlanDetailItemResponseDTO[]

  /**
   * 月度项目, 月份kv结构
   */
  monthPlanEnterItems?: Map<number, IMonthPlanDetailItemResponseDTO[]>
}

export class IMonthPlanDetailItemResponseDTO extends IMonthPlanItemDO {
  planName?: string
  /**
   * 月度评价
   */
  evaluate?: IEvaluateListResponseDTO[]
}
