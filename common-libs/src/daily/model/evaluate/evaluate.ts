import { IBaseDO } from '../../../basic/model/baseModel'

export class IEvaluateListRequestDTO {
  /**
   * 月度计划code
   */
  mpCode?: string

  mpVer?: number

  /**
   * 月度项目code
   */
  mpiCode?: string

  mpiVer?: number

  /**
   * 日清id
   */
  drId?: number

  /**
   * 日激励id
   */
  dreId?: number

  /**
   * 评价类型
   * {@link com.haierbusiness.daily.enums.EvaluateTypeEnum}
   */
  type?: number

  /**
   * 评价金额
   */
  evaluateAmount?: number

  /**
   * 评价系数
   */
  evaluateCoefficient?: string

  /**
   * 评价区位
   */
  evaluateLevel?: string
  /**
   * 评价备注
   */
  evaluateRemark?: string

  /**
   * 受评者
   */
  rateeUsercode?: string

  /**
   * 受评者
   */
  rateeUsername?: string

  /**
   * 受评者部门
   */
  rateeDeptCode?: string

  /**
   * 受评者部门
   */
  rateeDeptName?: string

  /**
   * 评价人
   */

  appraiserUsercode?: string

  appraiserUsername?: string

  appraiserDeptCode?: string

  /**
   * 评价人部门
   */
  appraiserDeptName?: string

  /**
   * 评价状态
   * {@link EvaluateStateEnum}
   */
  state?: number
}

export class IEvaluateDO extends IBaseDO {
  /**
   * 评价ID
   */
  id?: number

  /**
   * 年度计划主表code
   */
  apCode?: string

  apVer?: number

  /**
   * 月度计划code
   */
  mpCode?: string

  mpVer?: number

  /**
   * 月度项目code
   */
  mpiCode?: string

  mpiVer?: number

  /**
   * 日清id
   */
  drId?: number

  /**
   * 日激励id
   */
  dreId?: number

  /**
   * 评价类型
   * {@link com.haierbusiness.daily.enums.EvaluateTypeEnum}
   */
  type?: number

  /**
   * 评价金额
   */
  evaluateAmount?: number

  /**
   * 评价系数
   */
  evaluateCoefficient?: string

  /**
   * 评价区位
   */
  evaluateLevel?: string

  /**
   * 评价备注
   */
  evaluateRemark?: string

  /**
   * 附件地址
   */
  attachmentPath?: number

  /**
   * 受评者
   */
  rateeUsercode?: string

  /**
   * 受评者
   */
  rateeUsername?: string

  /**
   * 受评者部门
   */
  rateeDeptCode?: string

  /**
   * 受评者部门
   */
  rateeDeptName?: string

  /**
   * 评价人
   */

  appraiserUsercode?: string

  appraiserUsername?: string

  appraiserDeptCode?: string

  /**
   * 评价人部门
   */
  appraiserDeptName?: string

  /**
   * 评价时间
   */
  appraiseTime?: string

  /**
   * 评价状态
   * {@link EvaluateStateEnum}
   */
  state?: number

  /**
   * 评价状态
   *
   */
  stateName?: string
}

export class DailEvaluateDO extends IBaseDO {
  /**
   * 人员
   */
  rateeUsername?: string
  /**
   * 工号
   */
  rateeUsercode?: string
  /**
   * 小微
   */
  rateeDeptName?: string
  /**
   * 激励月份
   */
  month?: string
  /**
   * 评价时间
   */
  appraiseTime?: string
  /**
   * 评价类型
   */
  typeName?: string
  /**
   * 评价原因
   */
  evaluateRemark?: string
  /**
   * 级别
   */
  evaluateLevel?: string
  /**
   * 评价金额
   */
  evaluateAmount?: number
  /**
   * 录入时间
   */
  evaluateTime?: string
  startDate?: string
  endDate?: string
}

export class IEvaluateListResponseDTO extends IEvaluateDO {}

export class IEvaluateSaveRequestDTO {
  /**
   * 年度计划ID
   */
  id?: number

  evaluates?: IEvaluateSaveDetailsRequestDTO[]

  condition?: number

  /**
   * 00001 小微主评价   1
   * 00010 战略评价    2
   * 00100 风控评价    4
   * 01000 财务评价    8
   * 10000 人力评价    16
   * == 31 全部完成评价
   */
  groupFlag?: number
}

export class IEvaluateSaveDetailsRequestDTO {
  /**
   * 评价ID
   */
  id?: number

  /**
   * 年度计划主表code
   */
  apCode?: string

  apVer?: number

  /**
   * 月度计划code
   */
  mpCode?: string

  mpVer?: number

  /**
   * 月度项目code
   */
  mpiCode?: string

  mpiVer?: number

  /**
   * 日清id
   */
  drId?: number

  /**
   * 日激励id
   */
  dreId?: number

  /**
   * 评价类型
   * {@link com.haierbusiness.daily.enums.EvaluateTypeEnum}
   */
  type?: number

  /**
   * 评价类型
   */
  evaluateType?: number

  /**
   * 评价金额
   */
  evaluateAmount?: number

  /**
   * 评价系数
   */
  evaluateCoefficient?: string

  /**
   * 评价区位
   */
  evaluateLevel?: string

  /**
   * 评价备注
   */
  evaluateRemark?: string

  /**
   * 附件Path
   */
  attachmentPath?: string

  /**
   * 受评者
   */
  rateeUsercode?: string

  /**
   * 受评者
   */
  rateeUsername?: string

  /**
   * 受评者部门
   */
  rateeDeptCode?: string

  /**
   * 受评者部门
   */
  rateeDeptName?: string

  /**
   * 评价人
   */

  appraiserUsercode?: string

  appraiserUsername?: string

  appraiserDeptCode?: string

  /**
   * 评价人部门
   */
  appraiserDeptName?: string

  /**
   * 评价状态
   * {@link EvaluateStateEnum}
   */
  state?: number
}
