type keys = 'WAIT' | 'RUNNING' | 'COMPLETED' | 'TERMINATED' | 'INCOMPLETE'

/**
 * 月度计划项目状态
 *
 */
export const MonthPlanItemStateConstant = {
  /**
   * 待生效 (主表草稿状态）
   */
  WAIT: { code: 10, desc: '待生效' },

  /**
   * 进行中（主表审批通过）
   */
  RUNNING: { code: 20, desc: '进行中' },

  /**
   * 已完成 （主动修改，完成态）
   */
  COMPLETED: { code: 30, desc: '已完成' },

  /**
   * 已终止（主动修改，完成态）
   */
  TERMINATED: { code: 35, desc: '已终止' },

  /**
   * 未完成（次年进行中自动修改，未完成态）
   */
  INCOMPLETE: { code: 40, desc: '未完成' },

  ofCode: (code?: number): { code: number; desc: string } | null => {
    for (const key in MonthPlanItemStateConstant) {
      const item = MonthPlanItemStateConstant[key as keys]
      if (code === item.code) {
        return item
      }
    }
    return null
  },
}
