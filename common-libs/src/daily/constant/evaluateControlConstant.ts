type keys = 'YES' | 'NO' | 'ALL'

/**
 * 评价控制
 */
export const EvaluateControlConstant = {
  YES: { code: 1, desc: '查询' },
  NO: { code: 2, desc: '不查询' },
  /**
   * 评价（除平台主与管理员外用户传3则与1没有任何区别）
   */
  ALL: { code: 3, desc: '查询所有' },

  ofCode: (code?: number): { code: number; desc: string } | null => {
    for (const key in EvaluateControlConstant) {
      const item = EvaluateControlConstant[key as keys]
      if (code === item.code) {
        return item
      }
    }
    return null
  },
}
