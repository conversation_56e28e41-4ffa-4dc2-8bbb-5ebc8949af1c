type keys = 'WAIT' | 'RUNNING' | 'FINISH' | 'TERMINATED' | 'PLATFORM_TERMINATED' | 'COMPLETED'

/**
 * 月度计划状态
 */
export const MonthPlanStateConstant = {
  /**
   * 待执行（未到开始月份/年度未生效，可变更）
   */
  WAIT: { code: 10, desc: '待执行' },

  /**
   * 执行中 （到达开始月份，可变更）
   */
  RUNNING: { code: 20, desc: '执行中' },

  /**
   * 执行结束 （当前月份结束，不可变更）
   * 预留状态
   */
  FINISH: { code: 30, desc: '执行结束' },

  /**
   * 待评价（三自评价,小微主可评价）
   */
  EVALUATE: { code: 40, desc: '待评价' },

  /**
   * 待平台主评价
   */
  PLATFORM_EVALUATE: { code: 45, desc: '平台评价' },

  /**
   * 已完成（最终态）
   */
  COMPLETED: { code: 50, desc: '已完成' },
  ofCode: (code?: number): { code: number; desc: string } | null => {
    for (const key in MonthPlanStateConstant) {
      const item = (MonthPlanStateConstant as any)[key as keys]
      if (code === item.code) {
        return item
      }
    }
    return null
  },
}
