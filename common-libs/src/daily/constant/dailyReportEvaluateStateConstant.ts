type keys = 'WAIT' | 'PASS' | 'REJECT'

/**
 * 日清评价状态
 * 0：待确认 1：通过 2：驳回
 *
 * <AUTHOR>
 * @since 2023/10/11 16:22
 */
export const DailyReportEvaluateStateConstant = {
  /**
   * 生效版本，任何数据最多只有一条
   */
  WAIT: { code: 0, desc: '待确认' },

  /**
   * 历史版本(从生效版本演化而来)
   */
  PASS: { code: 1, desc: '通过' },

  /**
   * 待生效版本，任何数据最多只有一条
   */
  REJECT: { code: 2, desc: '驳回' },

  ofCode: (code?: number): { code: number; desc: string } | null => {
    for (const key in DailyReportEvaluateStateConstant) {
      const item = DailyReportEvaluateStateConstant[key as keys]
      if (code === item.code) {
        return item
      }
    }
    return null
  },
}
