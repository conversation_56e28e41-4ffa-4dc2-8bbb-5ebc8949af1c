import { IPageRequest } from '../../basic'

export class ServiceExamFilter extends IPageRequest {
  begin?: string
  end?: string
  serviceProviderId?: string
  orderNumber?: string
  meetingName?: string
  status?: number
  creator?: string
  merchantId?: number
}

export interface ServiceExam {
  id?: number | null
  creator?: string
  createTime?: string
  updater?: string
  updateTime?: string
  serviceProviderId?: string
  serviceProviderName?: string
  orderNumber?: string
  meetingName?: string
  assessmentItem?: string
  assessmentDetails?: string
  assessmentScore?: number
  assessmentAmount?: number
  status?: number
  attachment?: string
  reason?: string
  merchantExamItemId?: number | null
  examineCode?: string
}
