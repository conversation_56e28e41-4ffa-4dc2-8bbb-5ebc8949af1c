import { IPageRequest } from '../../basic'

export interface ProviderTablePagination {
  current: number
  pageSize: number
  total: number
}

export interface ServiceProvider {
  id?: number
  merchantName?: string
  type?: number
  score?: number
  fine?: number
  gmtCreate?: string
  merchantType?: string
  title?: string
  violationDisposeEndTime?: string | [string, string] | null
  entry?: string
  details?: string
  mainCode?: string
  miceName?: string
  unifiedSocialCreditCode?: string
  merchantCode?: string
  platformHotelId?: number
  platformHotelName?: string
  resourceHotelLeadIntoId?: number
  createBy?: string
  createName?: string
  enablePlaceChange?: boolean | null
  enableQuarter?: boolean | null
  enableStairs?: boolean | null
  inquiryState?: number
  leadIntoDateEnd?: string | null
  leadIntoDateStart?: string | null
  sourceId?: string | null
  verType?: number
  enterpriseCode?: string
  code?: string
  name?: string
  accountNumber?: string
  bankPhone?: string
  bankBranchAddress?: string
  merchantBankRecords?: string[]
}

export class ServiceProviderFilter extends IPageRequest {
  begin?: string
  end?: string
  merchantCode?: string
  merchantName?: string
  merchantType?: string
  creator?: string
  state?: string
  merchantId?: number
  againTime?: string
  endTime?: string
  id?: number
  enterpriseName?: string
  name?: string
}

export class ServiceProviderExam {
  id?: number | null
  mdrId?: number | null
  creator?: string
  miceName?: string
  createTime?: string
  gmtCreate?: string
  entry?: string
  details?: string
  state?: string
  score?: number
  fine?: number
  status?: number
  result?: number
  disposeIdea?: string
  illustrate?: string
  path?: string[]
  updater?: string
  updateTime?: string
  hotelId?: string
  hotelName?: string
  priceValidStart?: string
  priceValidEnd?: string
  reason?: string
  attachment?: string
  violationTime?: [string, string] | null
  violationDisposeEndTime?: [string, string] | null
  violationDesc?: string
  evidenceMaterial?: string
  acceptCode?: string
  merchantName?: string
  merchantType?: string
}
