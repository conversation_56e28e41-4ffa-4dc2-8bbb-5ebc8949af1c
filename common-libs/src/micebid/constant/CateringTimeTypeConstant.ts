// 用餐时间

type keys = 'WU' | 'WAN'

export const CateringTimeTypeConstant = {
  WU: { code: 0, desc: '午餐' },
  WAN: { code: 1, desc: '晚餐' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in CateringTimeTypeConstant) {
      const item = CateringTimeTypeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(CateringTimeTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return CateringTimeTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
