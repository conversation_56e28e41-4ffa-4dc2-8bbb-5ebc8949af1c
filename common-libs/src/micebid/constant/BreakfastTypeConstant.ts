// 早餐类型

type keys = 'NO' | 'HAVE'

export const BreakfastTypeConstant = {
  NO: { code: 0, desc: '无早' },
  HAVE: { code: 1, desc: '含早' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in BreakfastTypeConstant) {
      const item = BreakfastTypeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(BreakfastTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return BreakfastTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
