// 人员类型

type keys = 'DESK' | 'BANQUET' | 'CINEMA' | 'DRINK' | 'DIRECTORATE' | 'SHOWCASE' | 'SERVICE'

export const AttendantTypeConstant = {
  DESK: { code: 0, desc: '摄影师' },
  BANQUET: { code: 1, desc: '导游' },
  CINEMA: { code: 2, desc: '翻译' },
  DRINK: { code: 3, desc: '主持人' },
  DIRECTORATE: { code: 4, desc: '服务人员' },
  SHOWCASE: { code: 5, desc: '其他' },
  SERVICE: { code: 6, desc: '会务平台服务人员' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in AttendantTypeConstant) {
      const item = AttendantTypeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(AttendantTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return AttendantTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
