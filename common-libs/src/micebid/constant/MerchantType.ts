// 会议服务商类型

type keys = 'HOTEL' | 'TRAVEL' | 'INSURANCE' | 'GIFT' | 'CAR' | 'ALL'

export const MerchantType = {
  HOTEL: { code: 1, desc: '酒店' },
  TRAVEL: { code: 2, desc: '旅行社' },
  INSURANCE: { code: 3, desc: '保险' },
  GIFT: { code: 4, desc: '礼品' },
  CAR: { code: 5, desc: '用车' },
  ALL: { code: 6, desc: '全部' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in MerchantType) {
      const item = MerchantType[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(MerchantType).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MerchantType[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
