type keys = 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'REJECTED'

/**

 */
export const ServiceProviderTypeConstant = {
  COMPLETED: { type: 1, name: '手动停用' },
  CANCELLED: { type: 2, name: '合同到期自动停用' },
  REJECTED: { type: 3, name: '未录账单自动停用' },
  TRIAL: { type: 4, name: '初始化默认停用' },
} as const

/**
 * 根据类型获取状态信息
 */
export const getServiceProviderStatus = (type?: number): { type: number; name: string } | null => {
  for (const key in ServiceProviderTypeConstant) {
    const item = ServiceProviderTypeConstant[key as keys]
    if (type === item.type) {
      return item
    }
  }
  return null
}
