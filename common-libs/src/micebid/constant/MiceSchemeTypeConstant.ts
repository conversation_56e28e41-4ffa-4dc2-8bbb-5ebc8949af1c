// 方案类型

type keys = 'SCHEME_TYPE_INTERACTIVE' | 'SCHEME_TYPE_BIDDING' | 'SCHEME_TYPE_EXECUTION'

export const MiceSchemeTypeConstant = {
  SCHEME_TYPE_INTERACTIVE: { code: 0, desc: '互动方案' },
  SCHEME_TYPE_BIDDING: { code: 1, desc: '竞价方案' },
  SCHEME_TYPE_EXECUTION: { code: 2, desc: '执行方案' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in MiceSchemeTypeConstant) {
      const item = MiceSchemeTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(MiceSchemeTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceSchemeTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
