// 需求添加类型

type keys = 'STAY' | 'PLACE' | 'CATERING' | 'VEHICLE' | 'ATTENDANT' | 'ACTIVITY' | 'INSURANCE'

export const DemandTypeConstant = {
  STAY: { code: 1, type: 'type_stays', desc: '住宿' },
  PLACE: { code: 2, type: 'type_places', desc: '会场' },
  CATERING: { code: 4, type: 'type_caterings', desc: '用餐' },
  VEHICLE: { code: 8, type: 'type_vehicles', desc: '用车' },
  ATTENDANT: { code: 16, type: 'type_attendants', desc: '服务人员' },
  ACTIVITY: { code: 32, type: 'type_activities', desc: '拓展活动' },
  INSURANCE: { code: 64, type: 'type_insurances', desc: '保险' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in DemandTypeConstant) {
      const item = DemandTypeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(DemandTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return DemandTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
