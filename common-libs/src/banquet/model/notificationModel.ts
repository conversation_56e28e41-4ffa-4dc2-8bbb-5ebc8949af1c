// 通知通告列表参数接口
export interface BNotificationReq {
  /* */
  pageNum?: number

  /* */
  pageSize?: number

  /* 经办人工号 */
  creator?: string

  /* 经办人姓名 */
  creatorName?: string

  /* 操作时间 */
  createTime?: string[]

  /* */
  isShow?: boolean

  /* */
  needPage?: boolean
}

// 通知通告列表响应接口
export interface BNotificationRes {
  /* */
  /* */
  pageNum: number

  /* */
  pageSize: number

  /* */
  total: number

  /* */
  totalPage: number

  /* */
  records: Array<Notification>
}

export interface BNotification {
  /* */
  id: number

  /* */
  creator: string

  /* */
  creatorName: string

  /* */
  updater: string

  /* */
  updateTime: string

  /* */
  createTime: string

  /* */
  title: string

  /* */
  reveal: boolean

  /* */
  content: string

  /* */
  type: number
}

// 通知详情请求接口
export interface BNotificationInfoReq {
  id?: number | string
}

// 通知详情响应接口
export interface BNotificationInfoRes {
  /* */
  id: number

  /* */
  creator: string

  /* */
  creatorName: string

  /* */
  updater: string

  /* */
  updateTime: string

  /* */
  createTime: string

  /* */
  title: string

  /* */
  reveal: boolean

  /* */
  content: string

  /* */
  type: number
}

// 首页图片 响应接口
export interface BHomepagePicRes {
  /* */
  id: number

  /* */
  creator: string

  /* */
  creatorName: string

  /* */
  updater: string

  /* */
  updateTime: string

  /* */
  createTime: string

  /* */
  title: string

  /* */
  reveal: boolean

  /* */
  content: string

  /* */
  type: number
}

// 客户端获取登录信息
export interface ClientInvokeLoginReq {
  /* 跳转类型,可用值:ADMIN_CY,ADMIN_WM,STAFF_CY,STAFF_WM */
  type: string

  /* */
  bizParam: {
    /* uuid 作为美团唯一ID  */
    budgetKey?: string

    /* 去支付 需要传值，2: 跳转详情页面-当需要跳转详情页面时，restaurantId必传。 */
    restaurantType?: string

    /* 餐厅业务ID */
    restaurantId?: string

    /* 经纬度 */
    location?: {
      /* 经度 */
      longitude?: string

      /* 纬度 */
      latitude?: string
    }

    /* 用餐申请单信息 */
    repastApplyExtraJson?: {
      /* 美团用餐申请单号 */
      applyNo?: string

      /* 海尔内部系统用餐申请单单号 */
      externalApplyNo?: string
    }
  }
}

export interface ClientInvokeLoginRes {
  /* */
  url: string

  /* */
  accessKey: string

  /* */
  content: string
}

// 获取当前用户受管控信息
export interface BPoliciesRes {
  /* */
  userCode: string

  /* */
  userName: string

  /* */
  dept: string

  /* 是否提前选择餐厅 若为否 则置灰餐厅选择按钮 */
  advanceChooseRestaurant: boolean

  /* 受管控城市 */
  restrictedCity: {
    /* */
    id: number

    /* */
    policiesId: number

    /* 受管控对象code 例如部门code 用户code 城市code 或者餐厅id 等 */
    policyObjectCode: string

    /* */
    policyObjectName: string

    /* 受管控对象类型 0员工 1部门 2城市 3餐厅 */
    objectType: number

    /* 受管控对象为城市时，此字段有值，城市所在省code */
    mealLocationProvinceCode: string

    /* */
    mealLocationProvince: string
  }[]

  /* 受管控餐厅 */
  restrictedRestaurant: {
    /* */
    id: number

    /* */
    policiesId: number

    /* 受管控对象code 例如部门code 用户code 城市code 或者餐厅id 等 */
    policyObjectCode: string

    /* */
    policyObjectName: string

    /* 受管控对象类型 0员工 1部门 2城市 3餐厅 */
    objectType: number

    /* 受管控对象为城市时，此字段有值，城市所在省code */
    mealLocationProvinceCode: string

    /* */
    mealLocationProvince: string
  }[]
}
