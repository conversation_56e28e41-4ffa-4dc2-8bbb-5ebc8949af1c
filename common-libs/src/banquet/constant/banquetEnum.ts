// 美团订单状态
// 10待提交 20已提交 30支付中 40支付完成待消费 50消费中 60完成 70已关闭
export const BanquetStatusEnum = {
  // "10": "待提交",
  '20': '待占预算',
  '30': '审批中',
  '40': '待使用',
  '50': '使用中',
  '70': '已关闭',
}

// 预订单订单状态
export const reservationStatusEnum = {
  // "10": "待提交",
  '1': '待核销',
  '2': '已完成',
  '3': '部分退款',
  '4': '全部退款',
}

export const BanquetReservationStatusEnumMobile = {
  '0': '未核销',
  '10': '已核销',
  '20': '部分核销',
  '30': '已取消',
}

export const BanquetReservationStateTagColorMap = {
  '0': 'blue',
  '10': 'green',
  '20': 'green',
  '30': 'red',
}

// 审批状态
// 0创建1通过2撤销3驳回/拒绝
export const BanquetApproveStatusEnum = {
  '9': '未提交',
  '0': '审批中',
  '1': '审批通过',
  '2': '撤销审批',
  '3': '审批驳回',
}
export const BanquetApproveStatusTagColorEnum = {
  '0': 'blue',
  '1': 'green',
  '2': '#fff',
  '3': 'red',
}

export const BanquetApplicationTypeEnum = {
  1: '宴请',
  2: '外卖',
}

// 预订单状态
export const BanquetStatusEnumMobile = {
  '10': '草稿',
  '20': '待占预算',
  '30': '审批中',
  '40': '待使用',
  '50': '使用中',
  '70': '已关闭',
}

export const BanquetStateTagColorMap = {
  '10': '#999',
  '20': '#1989fa',
  '30': 'green',
  '40': 'blue',
  '50': 'blue',
  '70': '#ef2324',
}

// 支付类型
export const BanquetPayTypeNum = {
  '10': '企业支付',
  '20': '个人支付',
  '50': '组合支付',
}

// 汇总状态
export const statementStatusNum = {
  0: '未确认',
  1: '已确认',
}

// 外卖订单状态
export const TakeoutOorderStatusNum = {
  '1': '提交订单',
  '2': '向餐厅催单',
  '4': '已接单',
  '8': '已完成',
  '9': '取消',
}

// 结算状态
export const SettleStatusNum = {
  '10': '已汇总',
  '20': '已确认',
  '30': '已结算',
  '40': '取消汇总',
}
