import { IPageRequest } from '../../basic'

export interface ParkadeListRes {
  /* */
  data: {
    /* */
    pageNum: number

    /* */
    pageSize: number

    /* */
    total: number

    /* */
    totalPage: number

    /* */
    records: {
      /* 订单编号 */
      orderCode: string

      /* 车辆系统编号 */
      carSystemCode: string

      /* 充值园区 */
      zoneName: string

      /* 所属园区 */
      zoneCode: string

      /* 结算账户 */
      settlementAccount: string

      /* 推送金额 */
      pushedAmount: number

      /* 充值金额 */
      rechargeAmount: number

      /* 到账金额 */
      arrivalAmount: number

      /* 账户所属人id */
      accountUserId: number

      /* 账户所属人 */
      accountUser: string

      /* 充值时间 */
      rechargeTime: Record<string, unknown>

      /* 充值状态（0充值失败 1充值成功） */
      rechargeStatus: number

      /* 汇总状态（0未汇总 1已汇总 2汇总失败） */
      collectStatus: number

      /* 汇总单号 */
      collectCode: string
    }[]
  }

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}

export interface ParkadeRecordReq extends IPageRequest {
  orderCode?: string
  carSystemCode?: string
  collectCode?: string
  accountUserName?: string
  rechargeTimeStart?: string
  rechargeTimeEnd?: string
  zoneName?: string
  zoneCode?: string
  settlementAccount?: string
  rechargeStatus?: string
}

export interface ParkadeExportLogReq extends IPageRequest {
  businessId?: string
  type?: string
  userCode?: string
  userName?: string
  operationTimeStart?: string
  operationTimeEnd?: string
}

export interface ParkadeExportLogRes {
  /* */
  data: {
    /* */
    pageNum: number

    /* */
    pageSize: number

    /* */
    total: number

    /* */
    totalPage: number

    /* */
    records: {
      /* 业务id */
      businessId: number

      /* 导出业务类型 */
      type: number

      /* 经办人 */
      userId: number

      /* 经办人工号 */
      userCode: string

      /* 经办人姓名 */
      userName: string

      /* 操作时间 */
      operationTime: Record<string, unknown>

      /* 文件名称 */
      fileName: string
    }[]
  }

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}

export interface ConfirmParams {
  /* 汇总ID */
  collectId: number

  /* 汇总状态（10-待确认 20-已确认 30-已取消 40-已完成） */
  collectStatus: number

  /* 账单结果（true-账单正确 false-账单错误） */
  result?: boolean

  /* 取消原因 */
  cancelReason?: string
}

export interface CollectInfoByIdRes {
  /* */
  data: {
    /* 主键ID */
    id: number

    /* 汇总单号 */
    collectCode: string

    /* 汇总时间 */
    collectTime: Record<string, unknown>

    /* 所属园区名称 */
    zoneName: string

    /* 所属园区编码 */
    zoneCode: string

    /* 归账月份 */
    month: string

    /* 结算账户 */
    settlementAccount: string

    /* 汇总开始时间 */
    collectDateStart: Record<string, unknown>

    /* 汇总结束时间 */
    collectDateEnd: Record<string, unknown>

    /* 推送金额汇总 */
    collectPushedAmount: number

    /* 实际金额汇总 */
    collectArrivalAmount: number

    /* 经办人ID */
    userId: number

    /* 经办人工号 */
    userCode: number

    /* 经办人姓名 */
    userName: number

    /* 汇总状态（0未汇总 1已汇总 2汇总失败） */
    collectStatus: number

    /* 结算单号 */
    settlementCode: string

    /* 取消原因 */
    cancelReason: string
  }

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}

export interface OperationListRes {
  /* */
  data: {
    /* 汇总ID */
    collectId: number

    /* 节点 */
    node: string

    /* 操作时间 */
    operationTime: Record<string, unknown>

    /* 操作人id */
    userId: number

    /* 操作人 */
    user: string

    /* 操作结果/内结单号 */
    content: string
  }[]

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}
