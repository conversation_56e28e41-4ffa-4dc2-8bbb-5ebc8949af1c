export interface IAbCodeRequest {
  userName?: string
  userNickNmae?: string
  systemCode?: string
}

export interface IAbCodeResponse {
  id?: number

  /* 所属领域编码 */
  curAreaCode?: string

  /* 所属领域名称 */
  curAreaName?: string

  /* 所属平台编码 */
  curPlatformCode?: string

  /* 所属平台名称 */
  curPlatformName?: string

  /* 所属小微编码 */
  microUnitCode?: string

  /* 所属小微名称 */
  microUnitName?: string

  /* 人员编码 */
  memberCode?: string

  /* 人员名称 */
  memberName?: string

  /* 预算主体编码 */
  budgetGroupCode?: string

  /* 预算主体名称 */
  budgetGroupName?: string

  /* 预算主体管理岗编码 */
  budgetMasterCode?: string

  /* 预算主体管理岗名称 */
  budgetMasterName?: string

  /* 执行主体编码 */
  performGroupCode?: string

  /* 执行主体名称 */
  performGroupName?: string

  /* 执行主体管理岗编码 */
  performMasterCode?: string

  /* 执行主体管理岗名称 */
  performMasterName?: string

  /* 是否默认预算体 */
  defaultValue?: string

  /* 财务组织编码 */
  financialCode?: string

  /* 财务组织名称 */
  financialName?: string

  /* 来源系统 */
  systemCode?: string
}

export interface IAbCodeDeptResponse {
  /* 财务组织编码 */
  financialCode?: string

  /* 财务组织名称 */
  financialName?: string

  /* 所属领域编码 */
  areaUnitCode?: string

  /* 所属领域名称 */
  areaUnitName?: string

  /* 所属平台编码 */
  platformUnitCode?: string

  /* 所属平台名称 */
  platformUnitName?: string

  /* 所属小微编码 */
  microUnitCode?: string

  /* 所属小微名称 */
  microUnitName?: string

  /* 是否为预算主体 0-否 1-是 */
  isBudget?: string

  /* 是否为执行主体 0-否 1-是 */
  isPerform?: string

  /* 组织生效时间 */
  startTime?: string

  /* 组织失效时间 */
  endTime?: string

  /* 预算主体编码 */
  budgetCode?: string

  /* 执行主体编码 */
  performCode?: string

  /* 功能范围 */
  functionScope?: string

  /* 线体 */
  lineBody?: string

  /* 内外销 */
  domesticOrExport?: string

  /* 来源系统 */
  systemCode?: string

  /* 部门经理编码 */
  departmentManager?: string

  /* 部门经理名称 */
  departmentManagerName?: string

  /* 预算经理编码 */
  budgetManager?: string

  /* 预算经理名称 */
  budgetManagerName?: string

  masterCode?: string

  masterName?: string

  id?: number
}

export interface IBillAndCostCentersResponse {
  /* 执行组织编码 */
  performCode?: string

  /* 执行组织名称 */
  performGroupName?: string

  /* 一级费用编码 */
  oneLevelCost?: string

  /* 一级费用名称 */
  oneLevelCostName?: string

  /* 二级费用编码 */
  twoLevelCost?: string

  /* 二级费用名称 */
  twoLevelCostName?: string

  /* 三级费用编码 */
  threeLevelCost?: string

  /* 三级费用名称 */
  threeLevelCostName?: string

  /* 四级费用编码 */
  fourLevelCost?: string

  /* 四级费用名称 */
  fourLevelCostName?: string

  /* 法人编码 */
  legalPerson?: string

  /* 法人公司名称 */
  legalPersonName?: string

  /* 成本中心编码 */
  costCenter?: string

  /* 成本中心名称 */
  costCenterName?: string

  /* 功能范围编码 */
  fkber?: string

  /* 线体信息 */
  lineBody?: string

  /* 来源系统编码 */
  systemCode?: string
}
