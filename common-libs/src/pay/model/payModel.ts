import { Dayjs } from 'dayjs'
import { IPageRequest } from '../../basic'
import { IHaierBudgetDepartment } from './budgetHaierPayBccSupportModel'

export class IFindTypeRequest {
  applicationCode?: string
  username?: string
  enterpriseCode?: string
  payTypes?: string
  businessType?: string
}

export class ISearchPaidRecordRequest {
  businessCode?: string
}

export class IPaymentRecord {
  id?: number
  /**
   * 支付中台单号
   */
  code?: string

  /**
   * 业务系统单号
   */
  businessCode?: string

  /**
   * 支付单所有人usercode
   */
  owner?: string

  /**
   * 支付单所有人姓名
   */
  ownerName?: string

  /**
   * 支付类型
   * 参考 pay_type 表
   */
  payType?: number

  /**
   * 支付子类型
   * 不同支付类型的子类型不同
   * 预算: HBC-BCC, BCC , GEMS 等
   * 聚合支付: 支付宝H5, 微信小程序, 微信二维码等
   * 参考 pay_type 表
   */
  paySubtype?: string

  /**
   * 支付平台单号
   */
  providerOrderCode?: string

  /**
   * 二级支付平台单号
   */
  secondProviderOrderCode?: string

  /**
   * 支付的账户ID，可是公司支付号、银行账号等，也可为空。
   */
  accountCode?: string

  /**
   * 支付状态
   * 0：取消；
   * 10：保存；
   * 20：预支付；
   * 30：成功；
   * 999: 失败;
   */
  state?: number

  /**
   * 业务应用code
   */
  applicationCode?: string

  /**
   * 企业编码
   */
  enterprise?: string

  /**
   * 企业
   */
  enterpriseCode?: string

  /**
   * 金额 元
   */
  amount?: number

  /**
   * 支付发起时间
   */
  payTime?: string

  /**
   * 支付成功时间
   */
  successPayTime?: string

  /**
   * 支付平台产生参数, 如二维码地址 ,支付url
   */
  payParam?: string

  /**
   * 支付回调地址
   */
  notifyUrl?: string

  /**
   * 通知回调状态 0:成功 1:重试 2:失败
   */
  notifyState?: number

  /**
   * 已重试次数
   */
  notifyRetry?: number

  /**
   * 最后通知失败原因
   */
  notifyErrorMessage?: string

  /**
   * 通知回调状态 0:成功 1:重试 2:失败
   */
  preNotifyState?: number

  /**
   * 已重试次数
   */
  preNotifyRetry?: number

  /**
   * 最后通知失败原因
   */
  preNotifyErrorMessage?: string

  /**
   * 支付结束后跳转地址
   */
  callbackUrl?: string

  /**
   * 详情地址
   */
  orderDetailsUrl?: string

  gmtCreate?: string
  gmtModified?: string
  createBy?: string
  lastModifiedBy?: string
}

export class IRefundRecord {
  id?: number
  /**
   * 支付中台退款单号
   */
  code?: string

  /**
   * 支付中台支付单号
   */
  paymentCode?: string

  /**
   * 业务系统单号(业务系统退款申请单号)
   */
  businessCode?: string

  /**
   * 业务系统原支付单号
   */
  paymentBusinessCode?: string

  /**
   * 支付单所有人usercode
   */
  owner?: string

  /**
   * 支付单所有人姓名
   */
  ownerName?: string

  /**
   * 支付类型
   * 参考 pay_type 表
   */
  payType?: number

  /**
   * 支付子类型
   * 不同支付类型的子类型不同
   * 预算: HBC-BCC, BCC , GEMS 等
   * 聚合支付: 支付宝H5, 微信小程序, 微信二维码等
   * 参考 pay_type 表
   */
  paySubtype?: string

  /**
   * 支付平台单号
   */
  providerOrderCode?: string

  /**
   * 二级支付平台单号
   */
  secondProviderOrderCode?: string

  /**
   * 支付的账户ID，可是公司支付号、银行账号等，也可为空。
   */
  accountCode?: string

  /**
   * 退款状态
   * 0：取消；
   * 10：保存；
   * 20：退款中；
   * 30：成功；
   * 999: 失败;
   */
  state?: number

  /**
   * 业务应用code
   */
  applicationCode?: string

  /**
   * 企业编码
   */
  enterprise?: string

  /**
   * 企业
   */
  enterpriseName?: string

  /**
   * 实际退款金额金额 单位元
   */
  amount?: number

  /**
   * 退款发起时间
   */
  refundTime?: string

  /**
   * 退款成功时间
   */
  successRefundTime?: string

  /**
   * 退款回调地址
   */
  notifyUrl?: string

  /**
   * 通知回调状态 0:成功 1:重试 2:失败
   */
  notifyState?: number

  /**
   * 已重试次数
   */
  notifyRetry?: number

  /**
   * 最后通知失败原因
   */
  notifyErrorMessage?: string

  /**
   * 详情地址
   */
  orderDetailsUrl?: string
  gmtCreate?: string
  gmtModified?: string
  createBy?: string
  lastModifiedBy?: string
}

export interface IPayBusinessDetailsUrl {
  recordCode?: string
}

export interface IPayRequest {
  /**
   * 应用程序code
   * 每个应用程序对应了多种支付方式
   */
  applicationCode?: string

  /**
   * 业务自己进行控制需要显示哪些支付类型
   * 如果为空则获取应用程序对应的全部支付方式
   */
  payTypes?: number[]

  /**
   * 支付用户
   * 用户对应多种支付方式
   */
  username?: string

  /**
   * 订单号
   */
  orderCode?: string

  /**
   * 订单供应商编码 PS: wyy
   */
  providerCode?: string

  /**
   * 订单金额 保留两位小数
   * 20.02
   */
  amount?: number

  /**
   * 详情地址
   */
  orderDetailsUrl?: string

  /**
   * 支付回调地址
   */
  notifyUrl?: string

  /**
   * 支付结束跳转地址
   */
  callbackUrl?: string

  /**
   * 订单描述
   */
  description?: string

  /**
   * 荷载
   */
  payload?: string

  /**
   * 商务支付中台支付来源
   * 1： pc
   * 2： 移动端
   */
  paySource?: number

  /**
   * H5端聚合支付类型（区分支付宝，微信，云闪付）
   */
  compositionPayType?: string

  /**
   * 1:付款码支付 2 :h5支付
   */
  paymentMethod?: number
}

export interface IPayResponse {
  /**
   * 支付平台单号
   */
  code?: string

  /**
   * 支付url
   */
  url?: string
}

export interface IPayRefreshResponse {
  /**
   * 支付平台单号
   */
  code?: string

  /**
   * 业务单号
   */
  businessCode?: string
}

export class IPaymentRecordListRequest extends IPageRequest {
  businessCode?: string

  businessCodes?: string[]

  /**
   * 支付单号
   */
  paymentRecordCode?: string

  /**
   * 支付平台单号
   */
  providerOrderCode?: string

  /**
   * 二级支付平台单号
   */
  secondProviderOrderCode?: string

  /**
   * 用户账号
   */
  username?: string

  /**
   * 用户昵称
   */
  nickName?: string

  /**
   * 支付状态
   */
  state?: number

  /**
   * 支付通知状态
   */
  notifyState?: number

  /**
   * 预支付通知状态
   */
  preNotifyState?: number

  /**
   * 支付类型
   */
  payType?: number

  /**
   * 支付子类型
   */
  paySubtype?: string

  /**
   * 支付开始时间
   */
  startPayBegin?: string

  /**
   * 支付结束时间
   */
  startPayEnd?: string

  /**
   * 支付成功开始时间
   */
  successPayBegin?: string

  /**
   * 支付成功结束时间
   */
  successPayEnd?: string

  /**
   * true 需要查询关联的预算记录
   */
  needBudget?: boolean

  /**
   * 业务应用code
   */
  applicationCode?: string

  /**
   * 支付的账户ID，可是公司支付号、银行账号等，也可为空。
   */
  accountCode?: string

  /**
   * 支付方式。
   */
  paymentMethod?: number
}

export class IRecordListRequest extends IPageRequest {
  code?: string

  businessCode?: string

  paySource?: string

  recordType?: string

  // 支付人
  owner?: string

  // 支付人姓名
  ownerName?: string

  /**
   * 支付类型
   */
  payType?: number

  /**
   * 支付子类型
   */
  paySubtype?: string

  // 	支付方式
  paymentMethod?: string

  /**
   * 支付状态
   */
  state?: number

  /**
   * 业务应用code
   */
  applicationCode?: string

  /**
   * 支付平台单号
   */
  providerOrderCode?: string

  /**
   * 支付的账户ID，可是公司支付号、银行账号等，也可为空。
   */
  accountCode?: string

  // 金额
  amount?: string

  // 支付(退款)发起时间
  payTime?: [Dayjs, Dayjs] | undefined

  // 支付(退款)成功时间
  successPayTime?: [Dayjs, Dayjs] | undefined

  gmtCreate?: [Dayjs, Dayjs] | undefined

  gmtModified?: [Dayjs, Dayjs] | undefined

  createBy?: string

  lastModifiedBy?: string

  // 通知回调状态
  notifyState?: string

  // 机器编号
  cashRegisterCode?: string
}

export interface IPaymentRecordListResponse extends IPaymentRecord {
  /**
   * 最后一次预算信息
   */
  validBudget: IHaierBudgetDepartment
}

export class IRefundRecordListRequest extends IPageRequest {
  /**
   * 业务程序退款申请订单号,不全局唯一,在相同业务申请单下唯一
   */
  refundBusinessCode?: string

  refundBusinessCodes?: string[]

  businessCode?: string

  businessCodes?: string[]

  /**
   * 支付记录单号
   */
  paymentRecordCode?: string

  /**
   * 退款记录单号
   */
  refundRecordCode?: string

  /**
   * 用户账号
   */
  username?: string

  /**
   * 用户昵称
   */
  nickName?: string

  /**
   * 支付状态
   */
  state?: number

  /**
   * 支付类型
   */
  payType?: number

  /**
   * 支付子类型
   */
  paySubtype?: string

  /**
   * true 需要查询关联的支付记录
   */
  needPayment?: boolean

  /**
   * 支付平台单号
   */
  providerOrderCode?: string

  /**
   * 二级支付平台单号
   */
  secondProviderOrderCode?: string

  /**
   * 退款的账户ID，可是公司支付号、银行账号等，也可为空
   */
  accountCode?: string

  /**
   * 业务应用code
   */
  applicationCode?: string

  /**
   * 退款通知状态
   */
  notifyState?: number

  /**
   * 退款开始时间
   */
  startRefundBegin?: string

  /**
   * 退款结束时间
   */
  startRefundEnd?: string

  /**
   * 退款成功开始时间
   */
  successRefundBegin?: string

  /**
   * 退款成功结束时间
   */
  successRefundEnd?: string

  paymentMethod?: number
}

export interface IRefundRecordListResponse extends IRefundRecord {
  /**
   * 退款对应支付信息
   */
  paymentRecord: IPaymentRecordListResponse
}

export interface IBusinessLog {
  id?: number

  /**
   * 业务单code
   */
  recordCode?: string

  /**
   * 1: 支付
   * 2: 退款
   */
  bizType?: number

  /**
   * 1: 支付
   * 2: 退款
   */
  bizTypeName?: string

  /**
   * 业务单状态编码作为步骤码
   */
  step?: string

  /**
   * 步骤描述
   */
  stepName?: string

  /**
   * 详细内容
   */
  content?: string

  /**
   * 0：失败；1：成功；
   */
  state?: number

  /**
   * 展示类型, 1: 全部可见 2: 管理端可见
   */
  showType?: number
  gmtCreate?: string
  createBy?: string
}

export interface IPayLogsRequest extends IPageRequest {
  recordCode?: string
  recordCodes?: Array<string>
}

export interface IRefundRequest {
  /**
   * 退款申请单号,需唯一
   */
  refundOrderCode?: string

  /**
   * 原单 ,主订单号
   */
  orderCode?: string

  /**
   * 创建人
   */
  username?: string

  /**
   * 原单 ,子订单号，适用于确认金额时，产生新订单的业务
   */
  subOrderCode?: string

  /**
   * 退还金额，出账
   */
  amount?: string

  /**
   * 应用code
   **/
  applicationCode?: string

  /**
   * 回调地址
   */
  notifyUrl?: string
}

export interface IRefundNotifyRequest {
  /**
   * 退款单号
   */
  refundCode?: string
  /**
   * 退款状态
   */
  state?: number
  /**
   * 回调地址
   */
  notifyUrl?: string
}

export interface IRefundResponse {
  /**
   * 支付平台退款单号
   */
  paymentRefundCode?: string
}

export interface IConfirmRequest {
  /**
   * 原单 ,主订单号
   */
  orderCode?: string

  /**
   * 确认子单号
   */
  subOrderCode?: string

  /** 标识是否最后一次确认，适用于有子订单生成的时候 */
  lastTime?: boolean

  /**
   * 确认金额
   */
  amount?: string
}

export interface IConfirmResponse {
  /**
   * 确认金额
   */
  amount?: string
}

export interface ICancelRequest {
  /**
   * 原单 ,主订单号
   */
  orderCode?: string
}

export interface ICancelResponse {}

export class ICoinFlowingWaterRequest extends IPageRequest {
  id?: string
  type?: number
  timeBegin?: string
  timeEnd?: string
  searchType?: number
}

export class ICoinListResponse {
  id?: string
  transactionFrom?: string
  transactionTo?: string
  amount?: string
  transactionType?: string
  transactionHash?: string
  payOrder?: string
  mainOrderCode?: string
  applicationCode?: string
  remark?: string
  transactionTime?: string
}

export class IAccountResponse {
  username?: string
  phone?: string
  balance?: number
  attribute?: number
}
