type keys =
  | 'BCC'
  | 'GEMS'
  | 'KEMS'
  | 'RRSGEMS'
  | 'HBC'
  | 'WXPay'
  | 'Alipay'
  | 'YQB'
  | 'QMF'
  | 'UnionPay'
  | 'BaiDu'
  | 'JD'
  | 'SF'
  | 'COMM'
  | 'BestPay'
  | 'ACP'
  | 'NetPayBills'
  | 'NetPayGtwy'
  | 'QmfWebPay'
  | 'UAC'
  | 'VILLA_CARD'
  | 'RECHARGE_VIRTUAL_CARD'
  | 'CREDIT_CARD'
  | 'PERSONAL_WELFARE_ACCOUNT'

/**
 * 支付类型
 */
export const PayTypeChildConstant = {
  BCC: { code: 'BCC', name: 'BCC', parent: 1 },
  GEMS: { code: 'GEMS', name: 'GEMS', parent: 1 },
  KEMS: { code: 'KEMS', name: 'KEM<PERSON>', parent: 1 },
  RRSGEMS: { code: 'RRSGEMS', name: 'RRSGEMS', parent: 1 },
  HBC: { code: 'H<PERSON>', name: '<PERSON><PERSON>', parent: 1 },
  WXPay: { code: 'WXPay', name: '微信', parent: 2 },
  Alipay: { code: '<PERSON><PERSON><PERSON>', name: '支付宝', parent: 2 },
  YQB: { code: 'YQB', name: '壹钱包', parent: 2 },
  QMF: { code: 'QMF', name: '全民付', parent: 2 },
  UnionPay: { code: 'UnionPay', name: '银联钱包', parent: 2 },
  BaiDu: { code: 'BaiDu', name: '百度钱包', parent: 2 },
  JD: { code: 'JD', name: '京东钱包', parent: 2 },
  SF: { code: 'SF', name: '顺丰顺手付', parent: 2 },
  COMM: { code: 'COMM', name: '交通银行', parent: 2 },
  BestPay: { code: 'BestPay', name: '翼支付', parent: 2 },
  ACP: { code: 'ACP', name: '银联全渠道立码付', parent: 2 },
  NetPayBills: { code: 'NetPayBills', name: '银商网付平台账单模块', parent: 2 },
  NetPayGtwy: { code: 'NetPayGtwy', name: '银商网付平台网关模块', parent: 2 },
  QmfWebPay: { code: 'QmfWebPay', name: 'POS通插件WEB版', parent: 2 },
  UAC: { code: 'ACP', name: '银联全渠道', parent: 2 },
  VILLA_CARD: { code: 'VILLA_CARD', name: '山庄卡', parent: 5 },
  RECHARGE_VIRTUAL_CARD: { code: 'RECHARGE_VIRTUAL_CARD', name: '充值虚拟卡', parent: 5 },
  CREDIT_CARD: { code: 'CREDIT_CARD', name: '挂账卡', parent: 5 },
  PERSONAL_WELFARE_ACCOUNT: { code: 'PERSONAL_WELFARE_ACCOUNT', name: '个人福利账户', parent: 5 },

  ofType: (type?: string): { code: string; name: string; parent: number } | null => {
    for (const key in PayTypeChildConstant) {
      const item = PayTypeChildConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (parent: number | undefined): { code: string; name: string; parent: number }[] => {
    if (!parent) {
      return []
    }
    const array: Array<{ code: string; name: string; parent: number }> = []
    Object.keys(PayTypeChildConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray' && PayTypeChildConstant[i as keys].parent === parent) {
        array.push(PayTypeChildConstant[i as keys])
      }
    })
    const newTypes = array.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
