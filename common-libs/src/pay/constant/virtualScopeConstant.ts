type keys = 'ENTERPRISE' | 'PERSON'

/**
 * 虚拟账户作用域
 */
export const VirtualScopeConstanty = {
  ENTERPRISE: { code: 1, desc: '企业' },
  PERSON: { code: 2, desc: '个人' },

  ofType: (code?: number): { code: number; desc: string } | null => {
    for (const key in VirtualScopeConstanty) {
      const item = VirtualScopeConstanty[key as keys]
      if (code === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(VirtualScopeConstanty).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return VirtualScopeConstanty[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
