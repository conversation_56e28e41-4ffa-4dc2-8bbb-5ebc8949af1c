type keys = 'BCC' | 'GEMS' | 'KEMS' | 'RRSGEMS' | 'HBC' | 'CZY' | 'XW'

/**
 * 预算类型
 */
export const HaierBudgetSourceConstant = {
  BCC: { code: '<PERSON><PERSON>', name: '<PERSON><PERSON>' },
  GEMS: { code: '<PERSON><PERSON>', name: '<PERSON><PERSON>' },
  KEMS: { code: '<PERSON>EM<PERSON>', name: '<PERSON>EM<PERSON>' },
  RRSGEMS: { code: 'R<PERSON><PERSON><PERSON>', name: 'R<PERSON><PERSON><PERSON>' },
  HBC: { code: 'HBC', name: 'H<PERSON>' },
  HSH: { code: 'HSH', name: 'HSH' },
  SCAN_CODE: { code: 'SCAN_CODE', name: 'SCAN_CODE' },
  XW: { code: 'XWFI<PERSON>', name: 'X<PERSON><PERSON><PERSON>' },
  CZY: { code: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON>' },
  ofCode: (code?: string): { code: string; name: string } | null => {
    for (const key in HaierBudgetSourceConstant) {
      const item = HaierBudgetSourceConstant[key as keys]
      if (code === item.code) {
        return item
      }
    }
    return null
  },
}
