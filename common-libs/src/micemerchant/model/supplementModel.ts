import { Dayjs } from 'dayjs'
import { UploadFile } from '../../micebid'

/**
 * 酒店项接口定义
 * 用于 MICE 商户端一手合同管理
 */
export interface HotelItem {
  /** 酒店唯一标识 */
  id: string
  /** 酒店名称 */
  hotelName: string
  /** 合同文件列表 */
  contractFiles: UploadFile[]
}

/**
 * 补充条目接口定义
 * 用于 MICE 商户端账单上传中的补充条目管理
 */
export interface SupplementItem {
  /** 唯一标识键 */
  key: string
  /** 临时ID，用于关联匹配 */
  id: string;
  /** 序号 */
  serialNumber: number
  /** 发生时间 */
  occurDate: Dayjs | string | null
  /** 项目名称 */
  itemName: string
  /** 类型：客损/其他等 */
  type: number | null
  /** 账单数量 */
  billNum: number | null
  /** 账单单价 */
  billUnitPrice: number | null
  /** 描述 */
  description: string
  /** 补充条目附件（字符串数组，API字段） */
  attachments: string[]
  /** 用于UI显示的文件对象 */
  attachmentFiles: UploadFile[]
  /** 关联的发票临时ID */
  invoiceTempId?: string | null
  /** 关联的水单临时ID */
  statementTempId?: string | null
}

/**
 * 补充条目API格式
 * 用于与后端接口交互的数据格式
 */
export interface SupplementItemAPI {
  /** 临时ID */
  id: string;
  /** 发生日期（格式化后的字符串） */
  occurDate: string | null
  /** 项目名称 */
  itemName: string
  /** 类型 */
  type: number | null
  /** 账单数量 */
  billNum: number
  /** 账单单价 */
  billUnitPrice: number
  /** 描述 */
  description: string
  /** 附件URL列表 */
  attachments: string[]
  /** 关联的发票临时ID */
  invoiceTempId?: string | null
  /** 关联的水单临时ID */
  statementTempId?: string | null
}

/**
 * 关联账单项接口定义
 * 用于发票和水单的关联账单数据
 */
export interface RelatedBillItem {
  /** 账单ID */
  id: string
  /** 序号 */
  sequenceNumber: number
  /** 日期 */
  date: string
  /** 项目名称 */
  project: string
  /** 类别 */
  category: string
  /** 合同价格 */
  contractPrice: number
  /** 合同数量 */
  contractQuantity: number
  /** 账单价格 */
  billPrice: number
  /** 账单数量 */
  billQuantity: number
  /** 关联账单标识 */
  relatedBill: string
}

/**
 * 发票项接口定义
 * 用于 MICE 商户端发票信息管理
 */
export interface InvoiceItem {
  /** 临时ID */
  tempId: string
  /** 序号（不传递给父组件） */
  serialNumber: number
  /** 时间字段 - 内部使用Dayjs，传递给父组件时转为string */
  occurDate: Dayjs | string | null
  /** 当地货币 */
  localCurrency: number | null
  /** 汇率 */
  exchangeRate: number | null
  /** 发票总金额（人民币） */
  totalAmountCny: number
  /** 关联金额合计 */
  relatedAmountTotalCny: number
  /** 关联账单 */
  relatedBill: string
  /** 附件路径数组 */
  paths: string[]
  /** 汇率截图路径数组 */
  ratePaths: string[]
  /** 汇率截图 */
  exchangeRateScreenshot?: UploadFile
  /** 单位字段 */
  unit: string | null
  /** 用于UI显示的附件文件对象 */
  attachmentFiles: UploadFile[]
  /** 关联账单数据 */
  relatedBills?: RelatedBillItem[]
}

/**
 * 水单项接口定义
 * 用于 MICE 商户端水单信息管理
 */
export interface WaterBillItem {
  /** 临时ID */
  tempId: string
  /** 序号（不传递给父组件） */
  serialNumber: number
  /** 时间字段 - 内部使用Dayjs，传递给父组件时转为string */
  occurDate: Dayjs | string | null
  /** 当地货币 */
  localCurrency: number | null
  /** 汇率 */
  exchangeRate: number | null
  /** 水单总金额（人民币） */
  totalAmountCny: number
  /** 关联金额合计 */
  relatedAmountTotalCny: number
  /** 关联账单 */
  relatedBill: string
  /** 附件路径数组 */
  paths: string[]
  /** 汇率截图路径数组 */
  ratePaths: string[]
  /** 汇率截图 */
  exchangeRateScreenshot?: UploadFile
  /** 单位字段 */
  unit: string | null
  /** 用于UI显示的附件文件对象 */
  attachmentFiles: UploadFile[]
  /** 关联账单数据 */
  relatedBills?: RelatedBillItem[]
}

/**
 * 住宿详单项接口定义
 * 用于 MICE 商户端住宿详单管理
 */
export interface AccommodationDetailItem {
  /** 临时ID */
  tempId: string
  /** 序号 */
  serialNumber: number
  /** 签到人数 */
  checkInPersonNum: number | null
  /** 详单人数 */
  detailPersonNum: number | null
  /** 比对结果 (0:不一致,1:一致) */
  comparisonResult: number
  /** 附件路径数组 */
  paths: string[]
  /** 附件文件列表（仅用于界面显示） */
  _attachments?: UploadFile[]
  /** 当前条目是否正在上传 */
  _uploading?: boolean
}

/**
 * 会议现场照片项接口定义
 * 用于 MICE 商户端会议现场照片管理
 */
export interface ConferencePhotoItem {
  /** 临时ID */
  tempId: string
  /** 序号（仅用于显示） */
  serialNumber: number
  /** 类型（用户输入） */
  subType: string | null
  /** 附件路径数组 */
  paths: string[]
  /** 附件文件列表 */
  photos: UploadFile[]
  /** 当前条目是否正在上传 */
  _uploading?: boolean
}

/**
 * 其他附件项接口定义
 * 用于 MICE 商户端其他附件管理
 */
export interface OtherAttachmentItem {
  /** 临时ID */
  tempId: string
  /** 序号 */
  serialNumber: number
  /** 其他附件说明 */
  description: string
  /** 附件路径数组 */
  paths: string[]
  /** 附件文件列表 */
  files: UploadFile[]
  /** 当前条目是否正在上传 */
  _uploading?: boolean
}

/**
 * 保单附件项接口定义
 * 用于 MICE 商户端保单附件管理
 */
export interface InsuranceAttachmentItem {
  /** 临时ID */
  tempId: string
  /** 序号（不传递给父组件） */
  serialNumber: number
  /** 保险产品ID */
  insuranceId: string
  /** 保险产品名称 */
  insuranceName: string
  /** 附件路径数组 */
  paths: string[]
  /** 用于UI显示的附件文件对象 */
  attachmentFiles: UploadFile[]
}

/**
 * 文件上传相关常量配置
 */
export const BILL_UPLOAD_FILE_CONFIG = {
  /** 基础文件类型（用于补充条目、酒店合同、住宿详单） */
  BASIC_FILE_TYPES: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'] as string[],

  /** 图片文件类型（用于会议现场照片） */
  IMAGE_FILE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'] as string[],

  /** 扩展文件类型（用于其他附件） */
  EXTENDED_FILE_TYPES: [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
  ] as string[],

  /** 基础文件大小限制（MB） */
  BASIC_FILE_SIZE_LIMIT: 10,

  /** 扩展文件大小限制（MB） */
  EXTENDED_FILE_SIZE_LIMIT: 20,

  /** 基础文件接受格式 */
  BASIC_UPLOAD_ACCEPT: '.pdf,.jpg,.jpeg,.png,.gif,.doc,.docx',

  /** 图片文件接受格式 */
  IMAGE_UPLOAD_ACCEPT: '.jpg,.jpeg,.png,.gif',

  /** 扩展文件接受格式 */
  EXTENDED_UPLOAD_ACCEPT: '.pdf,.jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt',
}

/**
 * @deprecated 使用 BILL_UPLOAD_FILE_CONFIG 代替
 * 保留此常量以向后兼容
 */
export const SUPPLEMENT_FILE_CONFIG = {
  /** 支持的文件类型 */
  SUPPORTED_FILE_TYPES: BILL_UPLOAD_FILE_CONFIG.BASIC_FILE_TYPES,
  /** 文件大小限制（MB） */
  FILE_SIZE_LIMIT: BILL_UPLOAD_FILE_CONFIG.BASIC_FILE_SIZE_LIMIT,
  /** 上传文件接受的格式 */
  UPLOAD_ACCEPT: BILL_UPLOAD_FILE_CONFIG.BASIC_UPLOAD_ACCEPT,
}
