export interface IBillUploadRequest {
    /*上一版本id */
    sourceId?: number;

    /*订单号(不做关联) */
    mainCode?: string;

    /*主表id */
    miceId?: number;

    /*方案表id */
    miceSchemeId?: number;

    /*账单总金额 */
    billTotalPrice: number;

    /*账单住宿信息 */
    stays?: IBillUploadStay[];

    /*账单用餐信息 */
    caterings?: IBillUploadCatering[];

    /*账单会场信息 */
    places?: IBillUploadPlace[];

    /*账单用车信息 */
    vehicles?: IBillUploadVehicle[];

    /*账单服务人员信息 */
    attendants?: IBillUploadAttendant[];

    /*账单拓展活动信息 */
    activities?: IBillUploadActivity[];

    /*账单保险信息 */
    insurances?: IBillUploadInsurance[];

    /*账单礼品信息 */
    presents?: IBillUploadPresent[];

    /*账单布展物料信息 */
    material?: IBillUploadMaterial;

    /*账单交通信息 */
    traffic?: IBillUploadTraffic;

    /*账单其它信息 */
    others?: IBillUploadOther[];

    /*账单服务费信息 */
    serviceFee?: IBillUploadServiceFee;

    /*账单补充条目信息 */
    additionalItems?: IBillUploadAdditionalItem[];

    /*账单附件一手合同信息 */
    attachmentContracts?: IBillUploadAttachmentContract[];

    /*账单附件发票信息 */
    attachmentInvoices?: IBillUploadAttachmentInvoice[];

    /*账单附件水单信息 */
    attachmentStatements?: IBillUploadAttachmentStatement[];

    /*账单附件住宿详单信息 */
    attachmentStayChecks?: IBillUploadAttachmentStayCheck[];

    /*账单会议现场照片信息 */
    attachmentPhotos?: IBillUploadAttachmentPhoto[];

    /*账单附件其他信息 */
    attachmentOthers?: IBillUploadAttachmentOther[];

    /*结算单信息 */
    balances?: IBillUploadBalance[];
}

export interface IBillUploadStay {
    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*上一版本id */
    sourceId?: number;

    /*需求酒店id */
    miceDemandHotelId?: number;

    /*需求住宿id */
    miceDemandStayId?: number;

    /*方案酒店id */
    miceSchemeHotelId?: number;

    /*方案住宿id */
    miceSchemeStayId?: number;

    /*需求日期 */
    demandDate?: string;

    /*房型 大床/双床/套房 */
    roomType?: number;

    /*早餐类型 无早/单早/双早 */
    breakfastType?: number;

    /*人数 */
    personNum?: number;

    /*方案入住房间数 */
    schemeRoomNum?: number;

    /*账单入住房间数 */
    billRoomNum: number;

    /*人数与房间数不一致原因 */
    discrepancyReason?: string;

    /*方案单价 */
    schemeUnitPrice?: number;

    /*账单单价 */
    billUnitPrice: number;

    /*协议产品id */
    agreementProductId?: number;

    /*协议单价, 来源于协议产品关联协议价格 */
    agreementUnitPrice?: number;

    /*市场单价, 来源于市场价比价功能 */
    marketUnitPrice?: number;

    /*门市单价, 来源于协议产品关联门市价格 */
    retailUnitPrice?: number;

    /*市场价询价单记录id */
    msMarketPriceInquiryDetailsId?: number;

    /*方案说明 */
    description?: string;
}

export interface IBillUploadCatering {
    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*上一版本id */
    sourceId?: number;

    /*是否酒店提供用餐 */
    isInsideHotel?: boolean;

    /*需求酒店id */
    miceDemandHotelId?: number;

    /*需求用餐id */
    miceDemandCateringId?: number;

    /*方案酒店id */
    miceSchemeHotelId?: number;

    /*方案用餐id */
    miceSchemeCateringId?: number;

    /*需求日期 */
    demandDate?: string;

    /*用餐类型 */
    cateringType?: number;

    /*用餐时间 午餐/晚餐 */
    cateringTime?: number;

    /*方案人数 */
    schemePersonNum?: number;

    /*账单人数 */
    billPersonNum: number;

    /*用餐标准 */
    demandUnitPrice?: number;

    /*是否包含酒水 */
    isIncludeDrinks?: boolean;

    /*方案单价 */
    schemeUnitPrice?: number;

    /*账单单价 */
    billUnitPrice: number;

    /*方案说明 */
    description?: string;
}

export interface IBillUploadPlace {
    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*上一版本id */
    sourceId?: number;

    /*需求酒店id */
    miceDemandHotelId?: number;

    /*需求会场id */
    miceDemandPlaceId?: number;

    /*方案酒店id */
    miceSchemeHotelId?: number;

    /*方案会场id */
    miceSchemePlaceId?: number;

    /*需求日期 */
    demandDate?: string;

    /*会场数量 */
    placeNum?: number;

    /*使用时间 上午/下午/晚间 bitmap */
    usageTime?: number;

    /*使用用途 会议举行/布展搭建/会议撤场 bitmap */
    usagePurpose?: number;

    /*方案人数 */
    schemePersonNum?: number;

    /*账单人数 */
    billPersonNum: number;

    /*面积 */
    area?: number;

    /*灯下层高 */
    underLightFloor?: number;

    /*摆台形式 */
    tableType?: number;

    /*是否需要led */
    hasLed?: boolean;

    /*方案led数量 */
    schemeLedNum?: number;

    /*账单led数量 */
    billLedNum?: number;

    /*方案led来源 */
    schemeLedSource?: string;

    /*账单led来源 */
    billLedSource?: string;

    /*led规格说明 */
    ledSpecs?: string;

    /*是否需要茶歇 */
    hasTea?: boolean;

    /*茶歇标准/每人 */
    teaEachTotalPrice?: number;

    /*茶歇说明 */
    teaDesc?: string;

    /*方案报价会场单价 */
    schemeUnitPlacePrice?: number;

    /*账单报价会场单价 */
    billUnitPlacePrice?: number;

    /*方案报价led单价 */
    schemeUnitLedPrice?: number;

    /*账单报价led单价 */
    billUnitLedPrice?: number;

    /*方案报价茶歇单价 */
    schemeUnitTeaPrice?: number;

    /*账单报价茶歇单价 */
    billUnitTeaPrice?: number;

    /*市场价询价单记录id */
    msMarketPriceInquiryDetailsId?: number;

    /*市场会场单价 */
    marketPriceUnitPrice?: number;

    /*协议产品id */
    agreementProductId?: number;

    /*协议产品单价 */
    agreementUnitPrice?: number;

    /*门市单价, 来源于协议产品关联门市价格 */
    retailUnitPrice?: number;

    /*方案说明 */
    description?: string;
}

export interface IBillUploadVehicle {
    /*上一版本id */
    sourceId?: number;

    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*需求用车id */
    miceDemandVehicleId?: number;

    /*方案用车id */
    miceSchemeVehicleId?: number;

    /*需求日期 */
    demandDate?: string;

    /*使用方式 单趟/包车 */
    usageType?: number;

    /*使用时长 半天/全天 */
    usageTime?: number;

    /*座位数 */
    seats?: number;

    /*方案车辆数量 */
    schemeVehicleNum?: number;

    /*账单车辆数量 */
    billVehicleNum: number;

    /*品牌 */
    brand?: string;

    /*路线,多程逗号分隔 */
    route?: string;

    /*方案单价 */
    schemeUnitPrice?: number;

    /*账单单价 */
    billUnitPrice: number;

    /*方案说明 */
    description?: string;
}

export interface IBillUploadAttendant {
    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*上一版本id */
    sourceId?: number;

    /*需求服务人员id */
    miceDemandAttendantId?: number;

    /*方案服务人员id */
    miceSchemeAttendantId?: number;

    /*需求日期 */
    demandDate?: string;

    /*人员类型 */
    type?: number;

    /*方案人数 */
    schemePersonNum?: number;

    /*账单人数 */
    billPersonNum: number;

    /*工作范围 */
    duty?: string;

    /*方案单价 */
    schemeUnitPrice?: number;

    /*账单单价 */
    billUnitPrice: number;

    /*方案说明 */
    description?: string;
}

export interface IBillUploadActivity {
    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*上一版本id */
    sourceId?: number;

    /*需求拓展活动id */
    miceDemandActivityId?: number;

    /*方案拓展活动id */
    miceSchemeActivityId?: number;

    /*需求日期 */
    demandDate?: string;

    /*费用标准 */
    demandUnitPrice?: number;

    /*方案人数 */
    schemePersonNum?: number;

    /*账单人数 */
    billPersonNum: number;

    /*方案单价 */
    schemeUnitPrice?: number;

    /*账单单价 */
    billUnitPrice: number;

    /*账单总价 */
    billTotalPrice: number;

    /*方案说明 */
    description?: string;
}

export interface IBillUploadInsurance {
    /*上一版本id */
    sourceId?: number;

    /*需求保险id */
    miceDemandInsuranceId?: number;

    /*方案保险id */
    miceSchemeInsuranceId?: number;

    /*需求日期 */
    demandDate?: string;

    /*需求单价 */
    demandUnitPrice?: number;

    /*方案参保人数 */
    schemePersonNum?: number;

    /*账单参保人数 */
    billPersonNum: number;

    /*保险产品id(以互动时为准,需求时只为意向) */
    productId?: number;

    /*产品所属商户id */
    productMerchantId?: number;

    /*险种名称 */
    insuranceName?: string;

    /*险种条目 */
    insuranceContent?: string;

    /*方案单价 */
    schemeUnitPrice?: number;

    /*账单单价 */
    billUnitPrice: number;

    /*方案说明 */
    description?: string;

    /*保单附件 */
    insuranceAttachments?: string[];
}

export interface IBillUploadPresentDetail {
    /*上一版本id */
    sourceId?: number;

    /*方案礼品id */
    miceSchemePresentId?: number;

    /*需求礼品id */
    miceDemandPresentId?: number;

    /*方案单价 */
    schemeUnitPrice?: number;

    /*账单单价 */
    billUnitPrice: number;

    /*送达日期 */
    deliveryDate?: string;

    /*方案礼品数量 */
    schemePersonNum?: number;

    /*账单礼品数量 */
    billPersonNum: number;

    /*礼品产品id(以互动时为准,需求时只为意向) */
    productId?: number;

    /*产品所属商户id */
    productMerchantId?: number;

    /*产品名称,当未选择产品时可自由修改 */
    productName?: string;

    /*单位 */
    unit?: string;

    /*礼品说明 */
    personSpecs?: string;
}

export interface IBillUploadPresent {
    /*上一版本id */
    sourceId?: number;

    /*需求礼品id */
    miceDemandPresentId?: number;

    /*方案礼品id */
    miceSchemePresentId?: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId?: number;

    /*账单水单id */
    miceBillAttachmentStatementId?: number;

    /*费用标准 */
    demandTotalPrice?: number;

    /*方案总金额 */
    schemeTotalPrice?: number;

    /*账单总金额 */
    billTotalPrice: number;

    /*方案说明 */
    description?: string;

    /*账单礼品明细信息 */
    presentDetails?: IBillUploadPresentDetail[];
}

export interface IBillUploadMaterialDetail {
    /*上一版本id */
    sourceId?: number;

    /*方案布展物料id */
    miceSchemeMaterialId?: number;

    /*需求布展物料表明细id */
    miceDemandMaterialDetailsId?: number;

    /*方案布展物料表明细id */
    miceSchemeMaterialDetailsId?: number;

    /*物料类型 枚举 */
    type?: number;

    /*规格说明 */
    specs?: string;

    /*方案物料数量 */
    schemeMaterialNum?: number;

    /*账单物料数量 */
    billMaterialNum: number;

    /*单位 */
    unit?: string;

    /*需求单价 */
    demandUnitPrice?: number;

    /*方案报价单价 */
    schemeUnitPrice?: number;

    /*账单单价 */
    billUnitPrice: number;
}

export interface IBillUploadMaterial {
    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*上一版本id */
    sourceId?: number;

    /*需求布展物料id */
    miceDemandMaterialId?: number;

    /*方案布展物料id */
    miceSchemeMaterialId?: number;

    /*费用标准/总 */
    demandTotalPrice?: number;

    /*方案总价 */
    schemeTotalPrice?: number;

    /*账单总价 */
    billTotalPrice?: number;

    /*方案说明 */
    description?: string;

    /*账单布展物料信息 */
    materialDetails?: IBillUploadMaterialDetail[];

    /*账单单价(临时字段) */
    billUnitPrice: number;

    /*账单数量(临时字段) */
    billQuantity: number;
}

export interface IBillUploadTraffic {
    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*上一版本id */
    sourceId?: number;

    /*需求交通id */
    miceDemandTrafficId?: number;

    /*方案交通id */
    miceSchemeTrafficId?: number;

    /*需求总金额 */
    demandTotalPrice?: number;

    /*方案总金额 */
    schemeTotalPrice?: number;

    /*账单总金额 */
    billTotalPrice: number;

    /*交通主表id */
    miceSchemeTrafficMainId?: number;
}

export interface IBillUploadOther {
    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*上一版本id */
    sourceId?: number;

    /*需求其它id */
    miceDemandOtherId?: number;

    /*方案其它id */
    miceSchemeOtherId?: number;

    /*需求日期 */
    demandDate?: string;

    /*费用标准 */
    demandTotalPrice?: number;

    /*项目 */
    itemName?: string;

    /*方案数量(不参与总价计算) */
    num?: number;

    /*账单数量(不参与总价计算) */
    billNum: number;

    /*单位 */
    unit?: string;

    /*规格描述 */
    specs?: string;

    /*方案总金额 */
    schemeTotalPrice?: number;

    /*账单总金额 */
    billTotalPrice: number;

    /*方案说明 */
    description?: string;
}

export interface IBillUploadServiceFee {
    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*方案服务费id */
    miceSchemeServiceFeeId?: number;

    /*服务商比例 */
    serviceFeeRate: number;

    /*服务商比例上限 */
    serviceFeeLimitRate?: number;

    /*服务商对应上限金额 */
    serviceFeeLimit?: number;

    /*方案服务费金额 */
    schemeServiceFeeReal?: number;

    /*账单服务费金额 */
    billServiceFeeReal: number;
}

export interface IBillUploadAdditionalItem {
    /*临时id, 用于关联发票表 */
    invoiceTempId?: number;

    /*临时id, 用于关联水单表 */
    statementTempId?: number;

    /*发生时间 */
    occurDate?: string;

    /*项目名称 */
    itemName?: string;

    /*类型 客损/其它等 */
    type?: number;

    /*账单数量 */
    billNum?: number;

    /*账单单价 */
    billUnitPrice?: number;

    /*描述 */
    description?: string;

    /*补充条目附件 */
    attachments?: string[];
}

export interface IBillUploadAttachmentContract {
    /*子类型,传酒店名称/类型是会前会中附件说明之类的 */
    subType?: string;

    /*附件路径 */
    paths?: string[];
}

export interface IBillUploadAttachmentInvoice {
    /*临时发票表id(虚拟id,需要账单住宿/用餐/会场的临时id与之一致) */
    tempId?: number;

    /*发生时间 */
    occurDate?: string;

    /*当地货币 */
    localCurrency: number;

    /*单位 */
    unit?: string;

    /*汇率 */
    exchangeRate?: number;

    /*总金额(人民币) */
    totalAmountCny: number;

    /*关联金额合计（人民币） */
    relatedAmountTotalCny: number;

    /*发票附件路径 */
    paths?: string[];

    /*汇率见证性材料 */
    path?: string;
}

export interface IBillUploadAttachmentStatement {
    /*临时水单表id(虚拟id,需要账单住宿/用餐/会场虚拟id一致) */
    tempId?: number;

    /*发生时间 */
    occurDate?: string;

    /*当地货币 */
    localCurrency: number;

    /*单位 */
    unit?: string;

    /*汇率 */
    exchangeRate?: number;

    /*总金额(人民币) */
    totalAmountCny: number;

    /*关联金额合计（人民币） */
    relatedAmountTotalCny: number;

    /*水单附件路径 */
    paths?: string[];

    /*汇率见证性材料 */
    path?: string;
}

export interface IBillUploadAttachmentStayCheck {
    /*发生时间 */
    occurDate?: string;

    /*签到人数 */
    checkInPersonNum: number;

    /*详单人数 */
    detailPersonNum: number;

    /*比对结果 (0:不一致,1:一致) */
    comparisonResult?: boolean;

    /*附件路径 */
    paths?: string[];
}

export interface IBillUploadAttachmentPhoto {
    /*子类型,传酒店名称/类型是会前会中附件说明之类的 */
    subType?: string;

    /*附件路径 */
    paths?: string[];
}

export interface IBillUploadAttachmentOther {
    /*子类型,传酒店名称/类型是会前会中附件说明之类的 */
    subType?: string;

    /*附件路径 */
    paths?: string[];
}

export interface IBillUploadBalance {
    /*子类型,传项目名称 */
    subType?: string;

    /*结算单附件 */
    balanceAttachment?: string[];
}
