// 酒店接口
export interface MiceSchemeHotel {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*需求酒店id */
  miceDemandHotelId: number;

  /*需求发布酒店id */
  miceDemandPushHotelId: number;

  /*需求发布酒店名称 */
  hotelName: string;

  /*装修年份 */
  decorationYear: string;

  /*距离 */
  distance: string;

  /*酒店所在城市id */
  cityId: number;

  /*酒店所在城市名称 */
  cityName: string;

  /*酒店所在区域id */
  districtId: number;

  /*酒店所在区域名称 */
  districtName: string;

  /*酒店地址 */
  hotelAddress: string;

  /*酒店等级 */
  level: number;
}

// 住宿接口
export interface MiceSchemeStay {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*需求酒店id */
  miceDemandHotelId: number;

  /*需求住宿id */
  miceDemandStayId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*方案酒店id */
  miceSchemeHotelId: number;

  /*需求日期 */
  demandDate: string;

  /*房型 大床/双床/套房 */
  roomType: number;

  /*早餐类型 无早/单早/双早 */
  breakfastType: number;

  /*人数 */
  personNum: number;

  /*方案 入住房间数 */
  schemeRoomNum: number;

  /*人数与房间数不一致原因 */
  discrepancyReason: string;

  /*方案单价 */
  schemeUnitPrice: number;

  /*协议产品id */
  agreementProductId: number;

  /*协议单价, 来源于协议产品关联协议价格 */
  agreementUnitPrice: number;

  /*市场单价, 来源于市场价比价功能 */
  marketUnitPrice: number;

  /*门市单价, 来源于协议产品关联门市价格 */
  retailUnitPrice: number;

  /*市场价询价单记录id */
  msMarketPriceInquiryDetailsId: number;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;

  /*方案说明 */
  description: string;
}

// 会场接口
export interface MiceSchemePlace {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*需求酒店id */
  miceDemandHotelId: number;

  /*需求会场id */
  miceDemandPlaceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*方案酒店id */
  miceSchemeHotelId: number;

  /*需求日期 */
  demandDate: string;

  /*会场数量 */
  placeNum: number;

  /*使用时间 上午/下午/晚间 bitmap */
  usageTime: number;

  /*使用用途 会议举行/布展搭建/会议撤场 bitmap */
  usagePurpose: number;

  /*方案人数 */
  schemePersonNum: number;

  /*面积 */
  area: number;

  /*灯下层高 */
  underLightFloor: number;

  /*摆台形式 */
  tableType: number;

  /*是否需要led */
  hasLed: boolean;

  /*方案led数量 */
  schemeLedNum: number;

  /*方案LED来源 */
  schemeLedSource: number;

  /*会议厅 */
  guildhall: string;

  /*会议厅-图片 */
  guildhallPhotos: string[];

  /*led规格说明 */
  ledSpecs: string;

  /*是否需要茶歇 */
  hasTea: boolean;

  /*茶歇标准/每人 */
  teaEachTotalPrice: number;

  /*茶歇说明 */
  teaDesc: string;

  /*方案报价会场单价 */
  schemeUnitPlacePrice: number;

  /*方案报价led单价 */
  schemeUnitLedPrice: number;

  /*方案报价茶歇单价 */
  schemeUnitTeaPrice: number;

  /*市场价询价单记录id */
  msMarketPriceInquiryDetailsId: number;

  /*市场会场单价 */
  marketPriceUnitPrice: number;

  /*协议产品id */
  agreementProductId: number;

  /*协议产品单价 */
  agreementUnitPrice: number;

  /*门市单价, 来源于协议产品关联门市价格 */
  retailUnitPrice: number;

  /*方案说明 */
  description: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 用餐接口
export interface MiceSchemeCatering {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*是否酒店提供用餐 */
  isInsideHotel: boolean;

  /*需求酒店id */
  miceDemandHotelId: number;

  /*需求用餐id */
  miceDemandCateringId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*方案酒店id */
  miceSchemeHotelId: number;

  /*需求日期 */
  demandDate: string;

  /*用餐类型 */
  cateringType: number;

  /*用餐时间 午餐/晚餐 */
  cateringTime: number;

  /*方案人数 */
  schemePersonNum: number;

  /*用餐标准 */
  demandUnitPrice: number;

  /*是否包含酒水 */
  isIncludeDrinks: boolean;

  /*方案单价 */
  schemeUnitPrice: number;

  /*方案说明 */
  description: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 用车接口
export interface MiceSchemeVehicle {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*需求用车id */
  miceDemandVehicleId: number;

  /*需求日期 */
  demandDate: string;

  /*使用方式 单趟/包车 */
  usageType: number;

  /*使用时长 半天/全天 */
  usageTime: number;

  /*座位数 */
  seats: number;

  /*方案车辆数量 */
  schemeVehicleNum: number;

  /*品牌 */
  brand: string;

  /*路线,多程逗号分隔 */
  route: string;

  /*方案单价 */
  schemeUnitPrice: number;

  /*方案说明 */
  description: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 服务人员接口
export interface MiceSchemeAttendant {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*需求服务人员id */
  miceDemandAttendantId: number;

  /*需求日期 */
  demandDate: string;

  /*人员类型 */
  type: number;

  /*方案人数 */
  schemePersonNum: number;

  /*工作范围 */
  duty: string;

  /*方案单价 */
  schemeUnitPrice: number;

  /*方案说明 */
  description: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 拓展活动接口
export interface MiceSchemeActivity {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*需求拓展活动id */
  miceDemandActivityId: number;

  /*需求日期 */
  demandDate: string;

  /*费用标准 */
  demandUnitPrice: number;

  /*方案人数 */
  schemePersonNum: number;

  /*方案单价 */
  schemeUnitPrice: number;

  /*方案说明 */
  description: string;

  /*方案附件 */
  activityAttachments: string[];

  /*需求活动附件路径 */
  paths: string[];

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 保险接口
export interface MiceSchemeInsurance {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*需求保险id */
  miceDemandInsuranceId: number;

  /*需求日期 */
  demandDate: string;

  /*需求单价 */
  demandUnitPrice: number;

  /*方案参保人数 */
  schemePersonNum: number;

  /*保险产品id(以互动时为准,需求时只为意向) */
  productId: number;

  /*产品所属商户id */
  productMerchantId: number;

  /*险种名称 */
  insuranceName: string;

  /*险种条目 */
  insuranceContent: string;

  /*方案单价 */
  schemeUnitPrice: number;

  /*方案说明 */
  description: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 布展物料详情接口
export interface MiceSchemeMaterialDetail {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*方案布展物料id */
  miceSchemeMaterialId: number;

  /*需求布展物料表明细id */
  miceDemandMaterialDetailsId: number;

  /*物料类型 枚举 */
  type: number;

  /*规格说明 */
  specs: string;

  /*方案物料数量 */
  schemeMaterialNum: number;

  /*单位 */
  unit: string;

  /*需求单价 */
  demandUnitPrice: number;

  /*方案报价单价 */
  schemeUnitPrice: number;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 布展物料接口
export interface MiceSchemeMaterial {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*需求布展物料id */
  miceDemandMaterialId: number;

  /*需求总价 */
  demandTotalPrice: number;

  /*方案总价 */
  schemeTotalPrice: number;

  /*方案说明 */
  description: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;

  /*方案布展物料详情 */
  materialDetails: MiceSchemeMaterialDetail[];
}

// 交通接口
export interface MiceSchemeTraffic {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*需求交通id */
  miceDemandTrafficId: number;

  /*总预算金额 */
  demandTotalPrice: number;

  /*方案总金额 */
  schemeTotalPrice: number;
}

// 礼品明细接口
export interface MiceSchemePresentDetail {
  /*主键 */
  id: number;

  /*方案礼品id */
  miceSchemePresentId: number;

  /*需求礼品id */
  miceDemandPresentId: number;

  /*方案单价 */
  schemeUnitPrice: number;

  /*送达日期 */
  deliveryDate: string;

  /*方案礼品数量 */
  schemePersonNum: number;

  /*礼品产品id(以互动时为准,需求时只为意向) */
  productId: number;

  /*产品所属商户id */
  productMerchantId: number;

  /*产品名称,当未选择产品时可自由修改 */
  productName: string;

  /*选择方式 0:手动填写,1:选择产品 */
  optionType: number;

  /*单位 */
  unit: string;

  /*礼品说明 */
  personSpecs: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 礼品接口
export interface MiceSchemePresent {
  /*主键 */
  id: number;

  /*需求礼品id */
  miceDemandPresentId: number;

  /*费用标准 */
  demandTotalPrice: number;

  /*方案总金额 */
  schemeTotalPrice: number;

  /*方案说明 */
  description: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;

  /*方案礼品明细 */
  presentDetails: MiceSchemePresentDetail[];
}

// 其他接口
export interface MiceSchemeOther {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*需求其它id */
  miceDemandOtherId: number;

  /*需求日期 */
  demandDate: string;

  /*费用标准 */
  demandTotalPrice: number;

  /*项目 */
  itemName: string;

  /*数量(不参与总价计算) */
  num: number;

  /*单位 */
  unit: string;

  /*规格描述 */
  specs: string;

  /*方案总金额 */
  schemeTotalPrice: number;

  /*方案说明 */
  description: string;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 服务费接口
export interface MiceSchemeServiceFee {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*方案主表id */
  miceSchemeId: number;

  /*服务商比例 */
  serviceFeeRate: number;

  /*服务商比例上限 */
  serviceFeeLimitRate: number;

  /*服务商对应上限金额 */
  serviceFeeLimit: number;

  /*实际服务费 */
  schemeServiceFeeReal: number;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId: number;
}

// 差异明细接口
export interface MiceSchemeDifference {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*主表id */
  miceId: number;

  /*需求表id */
  demandId: number;

  /*方案表id */
  schemeId: number;

  /*需求表ids,逗号分隔 */
  demandStayIds: string;

  /*方案表ids */
  schemeStayIds: string;

  /*差异日期 */
  differenceDate: string;

  /*需求当日总人数 */
  demandTotalPerson: number;

  /*方案当日总人数 */
  schemeTotalPerson: number;

  /*需求类型统计 */
  demandRoomType: string;

  /*方案类型统计 */
  schemeRoomType: string;

  /*需求大床数量 */
  demandOneRooms: number;

  /*方案大床数量 */
  schemeOneRooms: number;

  /*需求双床数量 */
  demandTwoRooms: number;

  /*方案双床数量 */
  schemeTwoRooms: number;

  /*需求套床数量 */
  demandSuiteRooms: number;

  /*方案套床数量 */
  schemeSuiteRooms: number;

  /*差异原因 */
  reason: string;
}

export interface MiceSchemeDetailsType {
  /*会议名称 */
  miceName?: string;

  /*会议类型 发布会等enum:[[{"code":1,"desc":"开盘会"},{"code":2,"desc":"客户会"},{"code":3,"desc":"培训会"},{"code":4,"desc":"发布会"},{"code":5,"desc":"展览会"},{"code":6,"desc":"内部沟通会"},{"code":7,"desc":"招商会"}]],可用值:1,2,3,4,5,6,7 */
  miceType?: number;

  /*会议举行城市,逗号分隔 */
  cityIds?: string;

  /*会议举行城市名称,逗号分隔 */
  cityNames?: string;

  /*需求开始时间 */
  startDate?: string;

  /*需求结束时间 */
  endDate?: string;

  /*总人数 */
  personTotal?: number;

  /*最后逆向原因 */
  finalReverseReason?: string;

  /*流程定义主表id */
  pdMainId?: number;

  /*流程定义版本id */
  pdVerId?: number;

  /*流程定义版本名 */
  pdVerName?: string;

  /*逆向发起时所处流程节点 */
  reverseProcessNode?: string;

  /*逆向流转后流程节点 */
  reverseAfterProcessNode?: string;

  /*会议状态enum:[[{"code":100,"desc":"需求提报","isDefault":true,"metaCatalogEnum":"DEMAND_SUBMIT"},{"code":110,"desc":"需求接收驳回","isDefault":false,"metaCatalogEnum":"DEMAND_SUBMIT"},{"code":120,"desc":"需求审批驳回","isDefault":false,"metaCatalogEnum":"DEMAND_SUBMIT"},{"code":200,"desc":"需求接单","isDefault":true,"metaCatalogEnum":"DEMAND_RECEIVE"},{"code":300,"desc":"需求事先交互","isDefault":true,"metaCatalogEnum":"DEMAND_PRE_INTERACT"},{"code":310,"desc":"用户需求确认驳回","isDefault":false,"metaCatalogEnum":"DEMAND_PRE_INTERACT"},{"code":320,"desc":"需求发布取消","isDefault":false,"metaCatalogEnum":"DEMAND_PRE_INTERACT"},{"code":400,"desc":"需求确认","isDefault":true,"metaCatalogEnum":"DEMAND_CONFIRM"},{"code":500,"desc":"需求审批","isDefault":true,"metaCatalogEnum":"DEMAND_APPROVAL"},{"code":600,"desc":"需求发布","isDefault":true,"metaCatalogEnum":"DEMAND_PUSH"},{"code":610,"desc":"需求发布审批撤回","isDefault":false,"metaCatalogEnum":"DEMAND_PUSH"},{"code":620,"desc":"需求发布审批驳回","isDefault":false,"metaCatalogEnum":"DEMAND_PUSH"},{"code":630,"desc":"方案提报撤回重新发布","isDefault":false,"metaCatalogEnum":"DEMAND_PUSH"},{"code":700,"desc":"需求发布复核","isDefault":true,"metaCatalogEnum":"DEMAND_RE_APPROVAL"},{"code":800,"desc":"方案提报","isDefault":true,"metaCatalogEnum":"SCHEME_SUBMIT"},{"code":810,"desc":"酒店锁定释放小时数","isDefault":false,"metaCatalogEnum":"SCHEME_SUBMIT"},{"code":820,"desc":"方案提报开始时间配置","isDefault":false,"metaCatalogEnum":"SCHEME_SUBMIT"},{"code":830,"desc":"各供应商可提报方案数量上限","isDefault":false,"metaCatalogEnum":"SCHEME_SUBMIT"},{"code":900,"desc":"方案审核","isDefault":true,"metaCatalogEnum":"SCHEME_APPROVAL"},{"code":905,"desc":"方案全部排除","isDefault":false,"metaCatalogEnum":"SCHEME_APPROVAL"},{"code":910,"desc":"方案复审驳回","isDefault":false,"metaCatalogEnum":"SCHEME_APPROVAL"},{"code":1000,"desc":"方案复审","isDefault":true,"metaCatalogEnum":"SCHEME_RE_APPROVAL"},{"code":1100,"desc":"方案确认","isDefault":true,"metaCatalogEnum":"SCHEME_CONFIRM"},{"code":1200,"desc":"竞价推送","isDefault":true,"metaCatalogEnum":"BID_PUSH"},{"code":1300,"desc":"竞价中","isDefault":true,"metaCatalogEnum":"BIDDING"},{"code":1400,"desc":"费用支付","isDefault":true,"metaCatalogEnum":"BID_RESULT_CONFIRM"},{"code":1410,"desc":"竞价流标","isDefault":false,"metaCatalogEnum":"BID_RESULT_CONFIRM"},{"code":1500,"desc":"费用审批","isDefault":true,"metaCatalogEnum":"COST_APPROVAL"},{"code":1550,"desc":"会议待执行","isDefault":true,"metaCatalogEnum":"MICE_PENDING"},{"code":1600,"desc":"会议执行中","isDefault":true,"metaCatalogEnum":"MICE_EXECUTION"},{"code":1700,"desc":"会议完成","isDefault":true,"metaCatalogEnum":"MICE_COMPLETED"},{"code":1750,"desc":"账单确认驳回","isDefault":false,"metaCatalogEnum":"MICE_COMPLETED"},{"code":1780,"desc":"账单复审驳回","isDefault":false,"metaCatalogEnum":"MICE_COMPLETED"},{"code":1800,"desc":"账单确认","isDefault":true,"metaCatalogEnum":"BILL_CONFIRM"},{"code":1850,"desc":"账单审批驳回","isDefault":false,"metaCatalogEnum":"BILL_CONFIRM"},{"code":1900,"desc":"账单审批","isDefault":true,"metaCatalogEnum":"BILL_APPROVAL"},{"code":2000,"desc":"账单复审","isDefault":true,"metaCatalogEnum":"BILL_RE_APPROVAL"},{"code":2100,"desc":"财务收款确认","isDefault":true,"metaCatalogEnum":"PAYMENT_CONFIRM"},{"code":2200,"desc":"平台收款发票录入","isDefault":true,"metaCatalogEnum":"PLATFORM_INVOICE_ENTRY"},{"code":2300,"desc":"服务商发票录入","isDefault":true,"metaCatalogEnum":"VENDOR_INVOICE_ENTRY"},{"code":2400,"desc":"发票确认","isDefault":true,"metaCatalogEnum":"INVOICE_CONFIRM"},{"code":2410,"desc":"退款确认（用户）","isDefault":true,"metaCatalogEnum":"REFUND_CONFIRM"},{"code":2510,"desc":"上传退款凭证（财务）","isDefault":true,"metaCatalogEnum":"PLATFORM_REFUND_RECEIPT_UPLOAD"},{"code":2520,"desc":"确认付款金额（平台）","isDefault":true,"metaCatalogEnum":"PLATFORM_PAY_RECEIPT_UPLOAD"},{"code":2530,"desc":"财务上传支付凭证","isDefault":true,"metaCatalogEnum":"PLATFORM_PAY_RECEIPT_UPLOAD"},{"code":2600,"desc":"平台发票确认","isDefault":true,"metaCatalogEnum":"PLATFORM_INVOICE_CONFIRM"},{"code":2900,"desc":"已结算","isDefault":true,"metaCatalogEnum":"END"},{"code":2910,"desc":"已作废","isDefault":false,"metaCatalogEnum":"END"},{"code":3000,"desc":"会议评价完成","isDefault":false,"metaCatalogEnum":"END"}]],可用值:100,110,120,200,300,310,320,400,500,600,610,620,630,700,800,810,820,830,900,905,910,1000,1100,1200,1300,1400,1410,1500,1550,1600,1700,1750,1780,1800,1850,1900,2000,2100,2200,2300,2400,2410,2510,2520,2530,2600,2900,2910,3000 */
  state?: number;

  /*流程所处节点 */
  processNode?: string;

  /*竞价推送状态enum:[[{"code":10,"desc":"待竞价"},{"code":20,"desc":"已竞价"},{"code":30,"desc":"放弃竞价"},{"code":40,"desc":"已中标"},{"code":50,"desc":"未中标"},{"code":60,"desc":"取消执行"}]],可用值:10,20,30,40,50,60 */
  bidPushState?: number;

  /*主键 */
  id?: number;

  /*订单号(不做关联) */
  mainCode?: string;

  /*主表id */
  miceId?: number;

  /*需求主表id */
  miceDemandId?: number;

  /*需求发布表id */
  miceDemandPushId?: number;

  /*需求发布服务商id */
  merchantId?: number;

  /*资源池id */
  pdmMerchantPoolId?: number;

  /*资源池名称 */
  pdmMerchantPoolName?: string;

  /*资源池可承接项目bitmap */
  pdmMerchantPoolItems?: number;

  /*资源池组ids */
  pdmMerchantPoolGroupIds?: string;

  /*资源池组名称逗号分隔 */
  pdmMerchantPoolGroupNames?: string;

  /*服务商code(展示用不关联) */
  merchantCode?: string;

  /*服务商类型 */
  merchantType?: number;

  /*商户名称 */
  merchantName?: string;

  /*服务商对接人 */
  contactMerchantName?: string;

  /*服务商对接人电话 */
  contactMerchantPhone?: string;

  /*服务商评分,取发布时评分并固化 */
  merchantScore?: number;

  /*商户合同号 */
  merchantContract?: string;

  /*服务商平台使用费率 */
  merchantPlatformUsageFeeRate?: number;

  /*当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id */
  sourceId?: number;

  /*标的方案id,记录新记录产生时的标的id */
  targetSchemeId?: number;

  /*中标方案id,新记录产生时记录中标方案id */
  bidSchemeId?: number;

  /*后备方案（多标的时，非最低价的标的方案） */
  isBackup?: boolean;

  /*区块链id */
  blockchainId?: string;

  /*询价单主表id */
  msMarketPriceInquiryId?: number;

  /*方案总金额(后端计算与前端传入比对) */
  schemeTotalPrice?: number;

  /*协议总金额(后端计算与前端传入比对) */
  agreementTotalPrice?: number;

  /*协议价+方案其他总金额(后端计算与前端传入比对) */
  agreementSchemeTotalPrice?: number;

  /*市场价总金额(后端计算与前端传入比对) */
  marketTotalPrice?: number;

  /*市场价+方案其他总金额(后端计算与前端传入比对) */
  marketSchemeTotalPrice?: number;

  /*市场价核验状态 */
  marketVerifyState?: number;

  /*方案备注 */
  remarks?: string;

  /*方案是否排除 */
  isExclude?: boolean;

  /*方案排除原因 */
  excludeRemarks?: string;

  /*是否用户选中方案 */
  isSelected?: boolean;

  /*用户不选择原因 */
  unselectRemarks?: string;

  /*是否指定方案 */
  isSpecified?: boolean;

  /*指定方案原因 */
  specifiedRemarks?: string;

  /*是否标的方案 */
  isTarget?: boolean;

  /*方案提报计划截止时间,到期后作废 */
  submitDeadline?: string;

  /*修改方案提报截止时间证明材料附件, 当修改方案提报截止时间时必传 */
  schemeAttachment?: string[];

  /*方案提报实际截止时间,到期后作废 */
  submitActualEndDate?: string;

  /*竞价计划截止时间 */
  biddingDeadline?: string;

  /*修改竞价截止时间证明材料附件, 当修改竞价截止时间时必传 */
  deadlineAttachment?: string[];

  /*修改推送模式见证性资料附件,当修改推送模式时必传 */
  pushStrategyAttachment?: string[];

  /*竞价实际截止时间 */
  biddingActualEndDate?: string;

  /*放弃类型 到期/主动 */
  abandonType?: number;

  /*放弃提报原因 */
  abandonReason?: string;

  /*方案类型 互动方案/竞价方案/执行方案 */
  schemeType?: number;

  /*方案状态 */
  schemeState?: number;

  /*提报人ip */
  submitterIp?: string;

  /*竞价提报见证性资料附件 */
  attachment?: string[];

  /*酒店 */
  hotels?: MiceSchemeHotel[];

  /*住宿 */
  stays?: MiceSchemeStay[];

  /*会场 */
  places?: MiceSchemePlace[];

  /*用餐 */
  caterings?: MiceSchemeCatering[];

  /*用车 */
  vehicles?: MiceSchemeVehicle[];

  /*服务人员 */
  attendants?: MiceSchemeAttendant[];

  /*拓展活动 */
  activities?: MiceSchemeActivity[];

  /*保险 */
  insurances?: MiceSchemeInsurance[];

  /* */
  material?: MiceSchemeMaterial;

  /* */
  traffic?: MiceSchemeTraffic;

  /*礼品 */
  presents?: MiceSchemePresent[];

  /*其他 */
  others?: MiceSchemeOther[];

  /* */
  serviceFee?: MiceSchemeServiceFee;

  /*mice需求住宿方案差异明细 */
  differences?: MiceSchemeDifference[];

  /*需求日期(临时字段)*/
  demandDate?: string;

  /*临时字段*/
  key?: number;
}

export interface MiceSchemeResFilter {
  // 主订单id, 默认查询最后一次生效需求关联的提报方案, miceId与miceDemandId 不能同时为空!
  miceId?: string
  // 需求id, 查询指定需求的方案, miceId与miceDemandId 不能同时为空!
  miceDemandId?: string
  // 方案主表id
  miceSchemeId?: string
  // 锁定表id
  miceSchemeDemandHotelLockId?: string
  // 方案主表ids
  miceSchemeIds?: string
  //	仅查询后备方案
  isBackup?: string
  // 方案类型, 默认不传查所有类型
  schemeTypes?: string
  // 	排除方案状态, true: 只查询排除方案 ,false: 只查询非排除方案, 默认全部查询
  searchExcludeState?: string
  // 用户选择方案状态, true: 只查询用户选择方案 ,false: 只查询非用户选择方案, 默认全部查询
  searchSelectedState?: string
  //	标的方案状态, true: 只查询标的方案 ,false: 只查询非标的方案, 默认全部查询
  searchTargetState?: string
}