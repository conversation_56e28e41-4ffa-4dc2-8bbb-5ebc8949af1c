/**
 * 货币单位相关常量和枚举定义
 */

/**
 * 货币单位枚举
 */
export enum CurrencyEnum {
  /** 人民币元 */
  CNY = 'CNY',
  /** 美元 */
  USD = 'USD',
  /** 日元 */
  JPY = 'JPY',
  /** 韩元 */
  KRW = 'KRW',
  /** 泰铢 */
  THB = 'THB',
  /** 欧元 */
  EUR = 'EUR',
  /** 英镑 */
  GBP = 'GBP',
  /** 港元 */
  HKD = 'HKD',
  /** 台币 */
  TWD = 'TWD',
}

/**
 * 货币单位标签映射
 */
export const CurrencyLabels = {
  [CurrencyEnum.CNY]: '元',
  [CurrencyEnum.USD]: '美元',
  [CurrencyEnum.JPY]: '日元',
  [CurrencyEnum.KRW]: '韩元',
  [CurrencyEnum.THB]: '泰铢',
  [CurrencyEnum.EUR]: '欧元',
  [CurrencyEnum.GBP]: '英镑',
  [CurrencyEnum.HKD]: '港元',
  [CurrencyEnum.TWD]: '台币',
} as const

/**
 * 根据枚举值获取对应的货币标签
 * @param value 枚举值
 * @returns 对应的标签文本
 */
export function getCurrencyLabel(value: CurrencyEnum): string {
  return CurrencyLabels[value] || '未知'
}

/**
 * 货币单位选项列表，用于下拉框等组件
 */
export const CurrencyOptions = [
  { label: '元', value: CurrencyEnum.CNY },
  { label: '美元', value: CurrencyEnum.USD },
  { label: '日元', value: CurrencyEnum.JPY },
  { label: '韩元', value: CurrencyEnum.KRW },
  { label: '泰铢', value: CurrencyEnum.THB },
  { label: '欧元', value: CurrencyEnum.EUR },
  { label: '英镑', value: CurrencyEnum.GBP },
  { label: '港元', value: CurrencyEnum.HKD },
  { label: '台币', value: CurrencyEnum.TWD },
] as const
