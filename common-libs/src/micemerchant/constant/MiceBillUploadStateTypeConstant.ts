type keys = 'CONFIRM' | 'REJECT' | 'APPROVE' | 'APPROVE_REJECT' | 'RE_APPROVE' | 'COMPLETE'

export const MiceBillUploadStateTypeConstant = {
  // 账单状态类型定义
  CONFIRM: { code: '10', desc: '账单确认中' },
  REJECT: { code: '20', desc: '账单驳回' },
  APPROVE: { code: '30', desc: '账单审核中' },
  APPROVE_REJECT: { code: '40', desc: '账单审批驳回' },
  RE_APPROVE: { code: '50', desc: '账单复审中' },
  COMPLETE: { code: '60', desc: '账单审核完成' },

  // 工具方法
  ofType: (type?: string): { code: string; desc: string } | null => {
    for (const key in MiceBillUploadStateTypeConstant) {
      const item = MiceBillUploadStateTypeConstant[key as keys]
      if (type === item?.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: string; desc: string } | undefined)[] => {
    const types = Object.keys(MiceBillUploadStateTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceBillUploadStateTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter((s): s is { code: string; desc: string } => {
      return s !== undefined
    })
    return newTypes
  },
}
