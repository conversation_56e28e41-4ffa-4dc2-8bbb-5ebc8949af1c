import type { Dayjs } from 'dayjs'
import { IPageRequest } from '../../basic'

export class ILifeRequest extends IPageRequest {
  id?: number | null
  creator?: string
  createTime?: string
  updater?: string
  updateTime?: string
  imgUrl?: string
  jumpLinkApp?: string
  jumpLinkPc?: string
  showOrder?: number
  showStatus?: number
  adSubject?: string
  begin?: string
  end?: string
}

export interface ILifeAccount {
  id?: number | null
  creator?: string
  createTime?: string
  updater?: string
  updateTime?: string
  imgUrl?: string
  jumpLinkApp?: string
  jumpLinkPc?: string
  showOrder?: number
  showStatus?: number
  adSubject?: string
  content?: string
  isTopping?: boolean
  author?: string
}

export class ILifeResponse {
  id?: number
  creator?: string
  createTime?: string
  showOrder?: number
  imgUrl?: string
  jumpLinkApp?: string
  jumpLinkPc?: string
  showStatus?: number
  infoAuthor?: string
  infoDate?: string | Dayjs
  infoTitle?: string
}
