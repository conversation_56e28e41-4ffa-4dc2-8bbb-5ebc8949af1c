import { IPageRequest } from '../../basic'

export class IInformationRequest extends IPageRequest {
  createTime?: [string, string]
  infoTitle?: string
  infoAuthor?: string
  showStatus?: number
  infoDate?: [string, string]
}

export class IInformationType {
  id?: number | null
  creator?: string
  createTime?: string
  showStatus?: number
  imgUrl?: string
  infoAuthor?: string
  infoDate?: string
  infoTitle?: string
  jumpLinkPc?: string
  jumpLinkApp?: string
  content?: string
  isTopping?: number
}

export class IInformationResponse {
  id?: number
  creator?: string
  createTime?: string
  updater?: string
  updateTime?: string
  imgUrl?: string
  jumpLinkApp?: string
  jumpLinkPc?: string
  showOrder?: number
  showStatus?: number
  infoTitle?: string
  infoAuthor?: string
  infoDate?: string
}
