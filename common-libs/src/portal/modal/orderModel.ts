import { Dayjs } from 'dayjs'

// localrest 订餐订单
export interface LocalrestFilter {
  applicantIsOwn?: boolean | string // 业务申请人是自己
  applicantManageIsOwn?: boolean | string // 业务申请人直线经理是自己
  applicantName?: string // 业务申请人
  budgeterName?: string // 预算人
  startTime?: string // yyyy-MM-dd 开始时间
  endTime?: string // yyyy-MM-dd 结束时间
  guestCompany?: string
  applyTime: string | [string, string] | [Dayjs, Dayjs]
  hotelName?: string // 酒店名称
  mainGuestNames?: string
  orderCode?: string // 订单号
  orderPayState?: string // 支付状态
  orderState?: string // 订单状态
  ownerIsOwn?: boolean | string // 经办人是自己
  ownerName?: string // 经办人
  payType?: string // 支付类型
  paymentCard?: string // 酒店储值卡
  signerName?: string // 签单人
  approvalState?: string // 审批状态
  billState?: string // 账单状态
  pageNum: number
  pageSize: number
}

export interface LocalrestType {
  accompanyOverStandardCause: string // 陪同人数超标原因
  accountCompanyName: string // 结算单位
  actualAmount: number // 实际发生金额
  applicant: PersonInfoType
  applicantId: string
  applyCause: string // 申请事由
  approvalState: number // 审批状态，-1：未提交审批； 10：审批中； 20：审批通过；30：审批驳回；
  backCause: number
  backCauseDescription: string // 其他退回原因
  billCode: string // 账单号，账单与订单1对多
  bookingTime: string // yyyy-MM-dd HH:mm:ss 账单号，账单与订单1对多
  budgetDepartmentName: string // 预算部门
  budgetAmount: number // 预算金额 = （餐位费 × 就餐总人数 + 就餐标准 + 工作餐金额）×（1 + 餐厅服务费率）
  businessAim: string // 业务目标
  cateTypeId: number // 餐类ID 外键,可为空 , ==表示不限
  cateTypeInfo: { id: number; name: string }
  code: string // 订单号
  consumptionStandard: number // 就餐标准 含酒水。PS：所有人加起来的
  createBy: string
  eatingTime: string // yyyy-MM-dd HH:mm:ss //就餐时间
  fullname: string // 酒店名称
  gmtCreate: string // yyyy-MM-dd HH:mm:ss //下单时间
  gmtModified: string // yyyy-MM-dd HH:mm:ss
  hotelId: number // 餐厅ID
  billInfo: BillBaseType // 账单详情
  lockedTime: string // yyyy-MM-dd HH:mm:ss //锁定日期
  lockerId: string // 锁定的客服用户名
  owner: PersonInfoType
  payType: number // 支付方式 1:挂账 , 2: 自付 , 3:储值卡支付
  paymentCard: string // 酒店储值卡号
  paymentState: number // 支付状态；00：未支付；10：预支付；20：已支付；90：无需支付；99：取消支付
  serviceFee: number // 支付给国旅的服务费
  state: number // 状态，0：已取消（审批驳回）；10：已保存（可以支付）；20：已提交；30：预定完成；40：已退回（客服）
  treatInfo: TreatInfoType
  workingLunchFee: number // 工作餐金额 = 工作餐提取人数 × 50
}

export interface PersonInfoType {
  email: string
  mobile: string
  name: string
  orderCode: string
  phone: string
}

export interface BillBaseType {
  amount: 0 // 实际金额
  budgetAmount: 0 // 预算金额
  checkComment: string // 核对意见
  checkTime: string // 核对时间
  code: string // 账单号
  createBy: string
  ebill: string // 电子账单地址
  gmtCreate: string // 账单提交日期
  hotelName: string // 酒店名称
  personCount: 0 // 实际就餐人数
  signerName: string // 实际签单人
  state: -1 // 账单状态
}

export interface TreatInfoType {
  accompanyCount: number // 陪同人数
  accompanyLeader: string // 我方主要陪同领导
  guestCompany: string // 来宾单位
  guestCount: number // 来宾人数
  mainGuestNames: string // 主宾姓名，可多个人
  mainGuestPosition: string // 主宾职务
  needParking: number // 是否预留车位，0：否；1：是；
  needSeatCard: number // 需要座牌
  orderCode: string
  seatOrderId: number // upload_file ID，座次文件ID
  signerMobile: string // 签单人手机
  signerName: string // 签单人姓名
  vehicleNo: string // 车牌号，可多个
}

// Localroom 订房订单
export interface LocalroomType {
  accountCompanyName: string // 结算单位
  actualAmount: number // 实际发生金额
  applicant: PersonInfoType
  applicantId: string
  applyCause: string // 申请事由
  approvalState: number // 审批状态，-1：未提交审批； 10：审批中； 20：审批通过；30：审批驳回；
  backCause: number
  backCauseDescription: string // 其他退回原因
  billCode: string // 账单号
  bookingTime: string // yyyy-MM-dd HH:mm:ss 账单号，账单与订单1对多
  budgetDepartmentName: string // 预算部门
  budgetAmount: number // 预算金额 = （餐位费 × 就餐总人数 + 就餐标准 + 工作餐金额）×（1 + 餐厅服务费率）
  businessAim: string // 业务目标
  checkIn: string
  checkOut: string
  code: string // 订单号
  createBy: string
  fullname: string // 酒店名称
  gmtCreate: string // yyyy-MM-dd HH:mm:ss //下单时间
  gmtModified: string // yyyy-MM-dd HH:mm:ss
  hotelId: number // 酒店ID
  billInfo: BillBaseType // 账单详情
  isLocked: number
  keepTime: string
  lastModifiedBy: string
  lockedTime: string
  lockerId: string
  owner: PersonInfoType
  payType: number // 支付方式 1:挂账 , 2: 自付 , 3:储值卡支付
  paymentCard: string // 酒店储值卡号
  paymentState: number // 支付状态；00：未支付；10：预支付；20：已支付；90：无需支付；99：取消支付
  serviceFee: number // 支付给国旅的服务费
  state: number // 状态，0：已取消（审批驳回）；10：已保存（可以支付）；20：已提交；30：预定完成；40：已退回（客服）
  treatInfo: LocalRoomTreatInfoType
  workingLunchFee: number // 工作餐金额 = 工作餐提取人数 × 50
}

export interface LocalRoomTreatInfoType {
  guestCompany: string // 来宾单位
  guestCount: number // 来宾人数
  guestListId: number // 来宾人文件
  guestNames: string // 来宾姓名
  needParking: number // 是否预留车位，0：否；1：是；
  orderCode: string
  signerMobile: string // 签单人手机
  signerName: string // 签单人姓名
  vehicleNo: string // 车牌号，可多个
}

// mice 会展订单
export interface MiceType {
  code: string // 订单号
  city: string // 城市
  isUrgent: number // 是否加急
  startDate: string // 会议开始时间
  finishDate: string // 会议结束时间
  handler: string // 经办人
  subject: string // 会议名称
  incharge: string // 会议负责人
  inchargePhone: string // 负责人电话
  inchargeEmail: string // 负责人邮箱
  status: number // 会议状态
  cbStatus: number // 账单状态
  saAmount: number // 中标金额(预算金额)
  actualAm: number // 账单金额
  havePresent: number // 是否存在礼品方案
  merName: string // 中标商户
  interactionTimeLimit: string // 互动截止时间
  biddingTimeLimit: string // 竞价截止时间
  billCode: string // 账单号
  aoCodes: Array<string> // AO单号
  azCodes: Array<string> // AZ单号
}

export interface MiceFilter {
  coCode: string
  coName: string
  coCity: string
  coIncharge: string
  pageNum: number
  pageSize: number
}

// 网易云
export interface WyyType {
  orderAfterSaleSkuCount: number | string | null
  addressId: string
  createTime: string
  expFee: number
  id: string
  orderId: string
  orderStatus: string
  payStatus: number
  payTime: string
  payablePrice: number
  realPrice: number
  reportUrl: string
  settlementStatus: number
  subOrderStatus: string
  summaryInfoId: string
  refundStatus: string
  userCode: string
  spuList: Array<OrderListSpuType>
  packageList: any
}

export interface OrderListSpuType {
  primaryPicUrl: string
  name: string
}

export interface WyyFilter {
  createTime: string[] | null
  orderId: string | null
  payTime: string[] | null
  spuName: string | null
  receiverName: string | null
  fullAddress: string | null
  receiverPhone: string | null
  paymentType: string | null
  summaryInfo: string | null
  orderStatus: string | null
  pageNo?: number
  pageSize?: number
}

export interface OrderDetailType {
  addressId: number
  createTime: string
  expFee: number
  orderId: string
  orderStatus: string
  payStatus: number
  payTime: string
  payablePrice: number
  realPrice: number
  reportUrl: string
  settlementStatus: number
  subOrderStatus: string
  summaryInfoId: string
  summaryInfo: null | string
  paymentType: number | string
  userCode: string
  orderSkus: Array<OrderSkusType>
}

export interface OrderSkusType {
  createTime: string
  displayString: string
  id: number
  orderId: string
  originPrice: number
  picUrl: string
  productName: string
  saleCount: number
  skuId: number
  spuId: number
  totalAmount: number
}

export interface JdFilter {
  state?: string
  keyWord?: string
  recipients?: string
  receivingPhone?: string
  address?: string
  paymentType?: number
  paymentGroupType?: string
  isAfterSale?: string
  creationStartDate?: string
  creationEndDate?: string
  creationDate?: [string, string]
  summaryInfo?: string
  pageNum: number
  pageSize: number
}

export interface JdType {
  code: string
  budgetId: string
  providerOrderId: string
  deliveryAddressDo: DeliveryAddressDoType
  gmtCreate: string
  subOrders: Array<SubOrderType>
  summaryInfo: string
  amount: number
  freight: number
}

export interface DeliveryAddressDoType {
  address: string
  email: string
  id: string
  name: string
  phone: string
}

export interface SubOrderType {
  products: Array<ProductsType>
  isState: string
  isAfterSale: number
}

export interface ProductsType {
  imagePath: string
  skuId: string
  name: string
  wareQD: string
  salesPrice: number
  num: number
}

export type OrderListResult<T> = {
  data: Array<T>
  message: string
  success: boolean
  mergeStatus?: Array<MergeStatusType>
  pageNum: number
  pageSize: number
  pages: number
  total: number
}

export interface MergeStatusType {
  name: string
  statusStr: string
}

export interface OrderSearchable<T> {
  list(filter: object): Promise<OrderListResult<T>>
}

// 发票确认请求参数
export interface InvoiceConfirmParams {
  id: number // 发票id
  miceId: number // 会务订单id
}

// 发票确认响应结果
export interface InvoiceConfirmResult {
  code: string
  message: string
  success: boolean
  data?: any
}

// 发票确认状态枚举（复用现有的状态模式）
export const InvoiceConfirmStatusConstant = {
  PENDING: { code: 10, desc: '待确认', color: '#FAAD14' },
  CONFIRMED: { code: 20, desc: '已确认', color: '#52C41A' },
  REJECTED: { code: 30, desc: '已驳回', color: '#FF4D4F' },
} as const

// 发票确认状态枚举工具函数
export const InvoiceConfirmStatusHelper = {
  ofCode: (code?: number) => {
    const statuses = [
      InvoiceConfirmStatusConstant.PENDING,
      InvoiceConfirmStatusConstant.CONFIRMED,
      InvoiceConfirmStatusConstant.REJECTED,
    ]
    return statuses.find((item) => item.code === code) || null
  },

  toArray: () => [
    InvoiceConfirmStatusConstant.PENDING,
    InvoiceConfirmStatusConstant.CONFIRMED,
    InvoiceConfirmStatusConstant.REJECTED,
  ],
}
