import { IPageRequest } from '../../basic'

export class IAdvertisementListRequest extends IPageRequest {
  /**
   * 标题
   */
  title?: string

  /**
   * 描述
   */
  content?: string

  /**
   * 图片
   */
  imgUrl?: string
  /**
   * 状态
   */
  showStatus?: number
  id?: number
}

export interface IAdvertisementAccount {
  /**
   * 标题
   */
  title?: string

  /**
   * 描述
   */
  content?: string

  /**
   * 图片
   */
  imgUrl?: string
  /**
   * 状态
   */
  showStatus?: number
  /**
   * 跳转链接
   */
  jumpLinkPc?: string
  /**
   * 顺序
   */
  showOrder?: number
  id?: number

  isTopping?: boolean

  author?: string

  gmtCreate?: string
}

export interface IAdvertisementResponse {
  id?: number

  jumpLinkPc?: string

  /**
   * 标题
   */
  title?: string

  /**
   * 描述
   */
  content?: string

  /**
   * 图片
   */
  imgUrl?: string
}

export interface advertisementResponse {
  /**
   * 标题
   */
  title?: string

  /**
   * 描述
   */
  content?: string

  /**
   * 图片
   */
  imgUrl?: string
  /**
   * 状态
   */
  showStatus?: number
  /**
   * 顺序
   */
  showOrder?: number
  /**
   * 创建时间
   */
  gmtCreate?: string
  id?: number
}
