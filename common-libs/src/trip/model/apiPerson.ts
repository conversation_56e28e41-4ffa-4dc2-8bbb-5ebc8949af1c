import { ICreatTrip } from '@haierbusiness-front/common-libs'
// 新增外部出行人
// 参数接口
export interface IOutPerson {
  /* 姓名 */
  name?: string

  /* 英文名 */
  englishName?: string

  /* 国籍/地区 */
  nature?: string[]
  natureName?: string
  natureCode?: string

  /* 性别1男2女 */
  sex?: string

  /* 出生日期 */
  birthday?: string

  /* 乘机人类型 */
  travelType?: string

  /* 联系电话 */
  telephone?: string

  /* 邮箱 */
  email?: string

  /* 电话号码 */
  phone?: string

  /* 服务保障等级 */
  promiseLevel?: string

  /* 证件类型 */
  cardType?: string

  /* 证件号 */
  cardNo?: string

  dateRange?: Array<string>

  /* 有效期始 */
  effectBeginDate?: string

  /* 有效期止 */
  effectEndDate?: string
}

// 外部出行人响应接口
export interface PageRes {
  /* */
  data: {
    /* */
    pageNum: number

    /* */
    pageSize: number

    /* */
    total: number

    /* */
    totalPage: number

    /* */
    records: {
      /* */
      pageNum: number

      /* */
      pageSize: number

      /* 主键id */
      id: number

      /* 申请单号 */
      applyNo: string

      /* 原申请单号 */
      applyNoOld: string

      /* 出差事由 */
      travelReason: string

      /* 是否商旅预定 */
      travelReserveFlag: number

      /* 费用预算总计 */
      amountSum: Record<string, unknown>

      /* 数据批次 */
      dataVersion: string

      /* 删除标识，0正常1删除，默认0 */
      deleted: number

      /* 发送胜意状态10未发送20已发送 */
      syStatus: string

      /* 预算状态10待占用20占用中30占用成功40占用失败50释放预算 */
      budgetStatus: string

      /* 单据状态10待提交20待生效30出行中40行程结束50已完成90已作废 */
      status: string

      /* 审批状态10未发起20审批中30审批通过40审批驳回50发起失败 */
      auditStatus: string

      /* 变更状态10正常20变更中30已作废40历史版本 */
      changeStatus: string

      /* 审批流程id */
      workFlowId: string

      /* 审批失败原因 */
      workFlowFailInfo: string

      /* 是否超标0未超标1已超标默认0 */
      excessiveFlag: number

      /* 出发地编码 */
      beginCityCode: string

      /* 出发地名称 */
      beginCityName: string

      /* 出发地编码(胜意) */
      beginCityCodeSy: string

      /* 目的地编码 */
      endCityCode: string

      /* 目的地名称 */
      endCityName: string

      /* 目的地编码(胜意) */
      endCityCodeSy: string

      /* 出差日期始 */
      beginDate: Record<string, unknown>

      /* 出差日期止 */
      endDate: Record<string, unknown>

      /* 出差日期始(实际) */
      realBeginDate: Record<string, unknown>

      /* 出差日期止(实际) */
      realEndDate: Record<string, unknown>

      /* 申请人(经办人)id */
      operUserId: number

      /* 申请人(经办人)工号 */
      operUserNo: string

      /* 申请人(经办人)名称 */
      operUserName: string

      /* 申请人(经办人)部门id */
      operDeptId: number

      /* 申请人(经办人)部门名称 */
      operDeptName: string

      /* 创建人 */
      createBy: string

      /* 创建人名称 */
      createName: string

      /* 最后一次修改人 */
      lastModifiedBy: string

      /* 最后一次修改人名称 */
      lastModifiedName: string

      /* 创建时间 */
      gmtCreate: Record<string, unknown>

      /* 修改时间 */
      gmtModified: Record<string, unknown>

      /* 出行人 */
      travelerList: {
        /* 主键id */
        id: number

        /* 申请单号 */
        applyId: number

        /* 出差人id(胜意) */
        travelUserSyId: string

        /* 出差人名称 */
        travelUserName: string

        /* 出差人工号 */
        travelUserNo: string

        /* 出差人部门id */
        travelUserDeptId: string

        /* 出差人部门名称 */
        travelUserDeptName: string

        /* 出差人类型0出差人1外部出行人 */
        travelUserType: string

        /* 是否主出差人0否1是 */
        mainFlag: string

        /* 创建人 */
        createBy: string

        /* 创建人名称 */
        createName: string

        /* 最后一次修改人 */
        lastModifiedBy: string

        /* 最后一次修改人名称 */
        lastModifiedName: string

        /* 创建时间 */
        gmtCreate: Record<string, unknown>

        /* 修改时间 */
        gmtModified: Record<string, unknown>
      }[]

      /* 行程 */
      tripList: {
        /* 主键id */
        id: number

        /* 申请单id */
        applyId: number

        /* 行程出发地编码 */
        beginCityCode: string

        /* 行程出发地编码-胜意 */
        beginCityCodeSy: string

        /* 行程出发地 */
        beginCity: string

        /* 行程目的地编码 */
        endCityCode: string

        /* 行程目的地编码-胜意 */
        endCityCodeSy: string

        /* 行程目的地 */
        endCity: string

        /* 出差日期始 */
        beginDate: Record<string, unknown>

        /* 出差日期始 */
        endDate: Record<string, unknown>

        /* 出差日期始(实际) */
        realBeginDate: Record<string, unknown>

        /* 主键id */
        realEndDate: Record<string, unknown>

        /* 创建人 */
        createBy: string

        /* 创建人名称 */
        createName: string

        /* 最后一次修改人 */
        lastModifiedBy: string

        /* 最后一次修改人名称 */
        lastModifiedName: string

        /* 创建时间 */
        gmtCreate: Record<string, unknown>

        /* 修改时间 */
        gmtModified: Record<string, unknown>

        /* 行程明细关系集合 */
        tripDetailMapList: {
          /* 主键id */
          id: number

          /* 申请单号 */
          applyId: number

          /* 行程表id */
          tripId: number

          /* 产品编码 */
          productCode: string

          /* 产品名称 */
          productName: string

          /* 费用预算总计 */
          budgetAmount: Record<string, unknown>

          /* 是否超标0未超标1已超标默认0 */
          excessiveFlag: number

          /* 创建人 */
          createBy: string

          /* 创建人名称 */
          createName: string

          /* 最后一次修改人 */
          lastModifiedBy: string

          /* 最后一次修改人名称 */
          lastModifiedName: string

          /* 创建时间 */
          gmtCreate: Record<string, unknown>

          /* 修改时间 */
          gmtModified: Record<string, unknown>

          /* 是否购买保险0否1是 */
          insuranceFlag: number

          /* 保险金额，购买保险时必传 */
          insuranceAmount: Record<string, unknown>

          /* 行程明细集合 */
          travelApplyTripDetailList: {
            /* 主键id */
            id: number

            /* 申请单id */
            applyId: number

            /* 行程表id */
            tripId: number

            /* 行程产品关系表id */
            tripDetailMapId: number

            /* 费用名称 */
            costName: string

            /* 出差人员 */
            travelerSyId: string

            /* 出差人员名称 */
            travelerName: string

            /* 出差人员工编号 */
            travelerNo: string

            /* 出差人部门id */
            travelerDeptId: string

            /* 出差人部门名称 */
            travelerDeptName: string

            /* 费用预算 */
            budgetAmount: Record<string, unknown>

            /* 是否超标0未超标1已超标默认0 */
            excessiveFlag: number

            /* 超标原因 */
            excessiveReason: string

            /* 差标 */
            differentialStandard: string

            /* 产品类型1费用2保险3服务费 */
            productType: number
          }[]
        }[]
      }[]

      /* 附件 */
      fileList: {
        /* 主键id */
        id: number

        /* 申请单号 */
        applyId: number

        /* 附件预览地址 */
        filePath: string

        /* 创建人 */
        createBy: string

        /* 创建人名称 */
        createName: string

        /* 最后一次修改人 */
        lastModifiedBy: string

        /* 最后一次修改人名称 */
        lastModifiedName: string

        /* 创建时间 */
        gmtCreate: Record<string, unknown>

        /* 修改时间 */
        gmtModified: Record<string, unknown>
      }[]

      /* */
      tripers: {
        /* 主键id */
        id: number

        /* 申请单号 */
        applyId: number

        /* 出差人id(胜意) */
        travelUserSyId: string

        /* 出差人名称 */
        travelUserName: string

        /* 出差人工号 */
        travelUserNo: string

        /* 出差人部门id */
        travelUserDeptId: string

        /* 出差人部门名称 */
        travelUserDeptName: string

        /* 出差人类型0出差人1外部出行人 */
        travelUserType: string

        /* 是否主出差人0否1是 */
        mainFlag: string

        /* 创建人 */
        createBy: string

        /* 创建人名称 */
        createName: string

        /* 最后一次修改人 */
        lastModifiedBy: string

        /* 最后一次修改人名称 */
        lastModifiedName: string

        /* 创建时间 */
        gmtCreate: Record<string, unknown>

        /* 修改时间 */
        gmtModified: Record<string, unknown>
      }[]

      /* 出行人（返回） */
      travelers: {
        /* 主键id */
        id: number

        /* 申请单号 */
        applyId: number

        /* 出差人id(胜意) */
        travelUserSyId: string

        /* 出差人名称 */
        travelUserName: string

        /* 出差人工号 */
        travelUserNo: string

        /* 出差人部门id */
        travelUserDeptId: string

        /* 出差人部门名称 */
        travelUserDeptName: string

        /* 出差人类型0出差人1外部出行人 */
        travelUserType: string

        /* 是否主出差人0否1是 */
        mainFlag: string

        /* 创建人 */
        createBy: string

        /* 创建人名称 */
        createName: string

        /* 最后一次修改人 */
        lastModifiedBy: string

        /* 最后一次修改人名称 */
        lastModifiedName: string

        /* 创建时间 */
        gmtCreate: Record<string, unknown>

        /* 修改时间 */
        gmtModified: Record<string, unknown>
      }[]

      /* 行程（返回） */
      trips: {
        /* 主键id */
        id: number

        /* 申请单id */
        applyId: number

        /* 行程出发地编码 */
        beginCityCode: string

        /* 行程出发地编码-胜意 */
        beginCityCodeSy: string

        /* 行程出发地 */
        beginCity: string

        /* 行程目的地编码 */
        endCityCode: string

        /* 行程目的地编码-胜意 */
        endCityCodeSy: string

        /* 行程目的地 */
        endCity: string

        /* 出差日期始 */
        beginDate: Record<string, unknown>

        /* 出差日期始 */
        endDate: Record<string, unknown>

        /* 出差日期始(实际) */
        realBeginDate: Record<string, unknown>

        /* 主键id */
        realEndDate: Record<string, unknown>

        /* 创建人 */
        createBy: string

        /* 创建人名称 */
        createName: string

        /* 最后一次修改人 */
        lastModifiedBy: string

        /* 最后一次修改人名称 */
        lastModifiedName: string

        /* 创建时间 */
        gmtCreate: Record<string, unknown>

        /* 修改时间 */
        gmtModified: Record<string, unknown>

        /* 行程明细 */
        tripDetailRespList: {
          /* 主键id */
          id: number

          /* 申请单id */
          applyId: number

          /* 行程表id */
          tripId: number

          /* 行程产品关系表id */
          tripDetailMapId: number

          /* 父级id;主要用于记录保险费和服务费归属 */
          parentId: number

          /* 费用名称 */
          costName: string

          /* 出差人id(胜意) */
          travelUserSyId: string

          /* 出差人名称 */
          travelUserName: string

          /* 出差人工号 */
          travelUserNo: string

          /* 出差人部门id */
          travelUserDeptId: string

          /* 出差人部门名称 */
          travelUserDeptName: string

          /* 出差人类型0出差人1外部出行人 */
          travelUserType: string

          /* 是否主出差人0否1是 */
          mainFlag: string

          /* 费用预算 */
          budgetAmount: Record<string, unknown>

          /* 是否超标0未超标1已超标默认0 */
          excessiveFlag: number

          /* 超标原因 */
          excessiveReason: string

          /* 差标 */
          differentialStandard: string

          /* 产品类型1费用2保险3服务费 */
          productType: number

          /* 创建人 */
          createBy: string

          /* 创建人名称 */
          createName: string

          /* 最后一次修改人 */
          lastModifiedBy: string

          /* 最后一次修改人名称 */
          lastModifiedName: string

          /* 创建时间 */
          gmtCreate: Record<string, unknown>

          /* 修改时间 */
          gmtModified: Record<string, unknown>

          /* 人员列表 */
          travelerList: Record<string, unknown>[]

          /* 保险金额 */
          insuranceAmount: number
        }[]

        /* 行程明细关系集合 */
        tripDetailMapList: {
          /* 主键id */
          id: number

          /* 申请单号 */
          applyId: number

          /* 行程表id */
          tripId: number

          /* 产品编码 */
          productCode: string

          /* 产品名称 */
          productName: string

          /* 费用预算总计 */
          budgetAmount: Record<string, unknown>

          /* 是否超标0未超标1已超标默认0 */
          excessiveFlag: number

          /* 创建人 */
          createBy: string

          /* 创建人名称 */
          createName: string

          /* 最后一次修改人 */
          lastModifiedBy: string

          /* 最后一次修改人名称 */
          lastModifiedName: string

          /* 创建时间 */
          gmtCreate: Record<string, unknown>

          /* 修改时间 */
          gmtModified: Record<string, unknown>

          /* */
          insuranceFlag: number

          /* */
          insuranceAmount: number

          /* */
          travelApplyTripDetailList: {
            /* 主键id */
            id: number

            /* 申请单id */
            applyId: number

            /* 行程表id */
            tripId: number

            /* 行程产品关系表id */
            tripDetailMapId: number

            /* 父级id;主要用于记录保险费和服务费归属 */
            parentId: number

            /* 费用名称 */
            costName: string

            /* 出差人id(胜意) */
            travelUserSyId: string

            /* 出差人名称 */
            travelUserName: string

            /* 出差人工号 */
            travelUserNo: string

            /* 出差人部门id */
            travelUserDeptId: string

            /* 出差人部门名称 */
            travelUserDeptName: string

            /* 出差人类型0出差人1外部出行人 */
            travelUserType: string

            /* 是否主出差人0否1是 */
            mainFlag: string

            /* 费用预算 */
            budgetAmount: Record<string, unknown>

            /* 是否超标0未超标1已超标默认0 */
            excessiveFlag: number

            /* 超标原因 */
            excessiveReason: string

            /* 差标 */
            differentialStandard: string

            /* 产品类型1费用2保险3服务费 */
            productType: number

            /* 创建人 */
            createBy: string

            /* 创建人名称 */
            createName: string

            /* 最后一次修改人 */
            lastModifiedBy: string

            /* 最后一次修改人名称 */
            lastModifiedName: string

            /* 创建时间 */
            gmtCreate: Record<string, unknown>

            /* 修改时间 */
            gmtModified: Record<string, unknown>

            /* 人员列表 */
            travelerList: Record<string, unknown>[]

            /* 保险金额 */
            insuranceAmount: number
          }[]

          /* 费用名称 */
          costName: string

          /* 人员列表 */
          travelerList: Record<string, unknown>[]
        }[]
      }[]

      /* */
      needPage: boolean
    }[]
  }

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}

// 响应接口
export interface DataListRes {
  /* */
  data: {
    /* */
    pageNum: number

    /* */
    pageSize: number

    /* 主键id */
    id: number

    /* 产品编码 */
    productCode: string

    /* 产品名称 */
    productName: string

    /* 创建人 */
    createBy: string

    /* 创建人名称 */
    createName: string

    /* 最后一次修改人 */
    lastModifiedBy: string

    /* 最后一次修改人名称 */
    lastModifiedName: string

    /* 创建时间 */
    gmtCreate: Record<string, unknown>

    /* 修改时间 */
    gmtModified: Record<string, unknown>

    /* */
    needPage: boolean
  }[]

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}

// 响应接口
export interface Page_1Res {
  /* */
  data: {
    /* */
    pageNum: number

    /* */
    pageSize: number

    /* */
    total: number

    /* */
    totalPage: number

    /* */
    records: {
      /* 主键id */
      id: number

      /* 申请单号 */
      applyNo: string

      /* 原申请单号 */
      applyNoOld: string

      /* 出差事由 */
      travelReason: string

      /* 是否商旅预定 */
      travelReserveFlag: number

      /* 费用预算总计 */
      amountSum: Record<string, unknown>

      /* 实际占用总计 */
      realAmountSum: Record<string, unknown>

      /* 数据批次 */
      dataVersion: string

      /* 删除标识，0正常1删除，默认0 */
      deleted: number

      /* 发送胜意状态10未发送20已发送 */
      syStatus: string

      /* 预算状态10待占用20占用中30占用成功40占用失败50释放预算 */
      budgetStatus: string

      /* 单据状态10待提交20待生效30出行中40行程结束50已完成90已作废 */
      status: string

      /* 审批状态10未发起20审批中30审批通过40审批驳回50发起失败 */
      auditStatus: string

      /* 变更状态10正常20变更中30已作废40历史版本 */
      changeStatus: string

      /* 审批流程id */
      workFlowId: string

      /* 审批失败原因 */
      workFlowFailInfo: string

      /* 是否超标0未超标1已超标默认0 */
      excessiveFlag: number

      /* 出发地编码 */
      beginCityCode: string

      /* 出发地名称 */
      beginCityName: string

      /* 出发地编码(胜意) */
      beginCityCodeSy: string

      /* 目的省编码 */
      beginProvinceCode: string

      /* 目的地省名称 */
      beginProvinceName: string

      /* 目的地省编码(胜意) */
      beginProvinceCodeSy: string

      /* 目的地编码 */
      endCityCode: string

      /* 目的地名称 */
      endCityName: string

      /* 目的地编码(胜意) */
      endCityCodeSy: string

      /* 目的省编码 */
      endProvinceCode: string

      /* 目的地省名称 */
      endProvinceName: string

      /* 目的地省编码(胜意) */
      endProvinceCodeSy: string

      /* 出差日期始 */
      beginDate: string

      /* 出差日期止 */
      endDate: string

      /* 出差日期始(实际) */
      realBeginDate: string

      /* 出差日期止(实际) */
      realEndDate: string

      /* 申请人(经办人)id */
      operUserId: number

      /* 申请人(经办人)工号 */
      operUserNo: string

      /* 申请人(经办人)名称 */
      operUserName: string

      /* 申请人(经办人)部门id */
      operDeptId: number

      /* 申请人(经办人)部门名称 */
      operDeptName: string

      /* 变更原因 */
      changeReason: string

      /* 未预定原因 */
      unbookedReason: string

      /* 创建人 */
      createBy: string

      /* 创建人名称 */
      createName: string

      /* 最后一次修改人 */
      lastModifiedBy: string

      /* 最后一次修改人名称 */
      lastModifiedName: string

      /* 创建时间 */
      gmtCreate: Record<string, unknown>

      /* 修改时间 */
      gmtModified: Record<string, unknown>

      /* 出发省、市编码 */
      beginCityCodeStrs: Record<string, unknown>[]

      /* 目的省、市编码 */
      endCityCodeStrs: Record<string, unknown>[]

      /* 出行人 */
      travelerList: {
        /* 主键id */
        id: number

        /* 申请单号 */
        applyId: number

        /* 出差人id(胜意) */
        travelUserSyId: string

        /* 出差人名称 */
        travelUserName: string

        /* 出差人工号 */
        travelUserNo: string

        /* 出差人部门id */
        travelUserDeptId: string

        /* 出差人部门名称 */
        travelUserDeptName: string

        /* 出差人类型0出差人1外部出行人 */
        travelUserType: string

        /* 是否主出差人0否1是 */
        mainFlag: string

        /* 创建人 */
        createBy: string

        /* 创建人名称 */
        createName: string

        /* 最后一次修改人 */
        lastModifiedBy: string

        /* 最后一次修改人名称 */
        lastModifiedName: string

        /* 创建时间 */
        gmtCreate: Record<string, unknown>

        /* 修改时间 */
        gmtModified: Record<string, unknown>
      }[]

      /* 行程 */
      tripList: {
        /* 主键id */
        id: number

        /* 申请单id */
        applyId: number

        /* 行程出发地编码 */
        beginCityCode: string

        /* 行程出发地编码-胜意 */
        beginCityCodeSy: string

        /* 行程出发地 */
        beginCity: string

        /* 出发省编码 */
        beginProvinceCode: string

        /* 出发地省名称 */
        beginProvinceName: string

        /* 出发地省编码(胜意) */
        beginProvinceCodeSy: string

        /* 行程目的地编码 */
        endCityCode: string

        /* 行程目的地编码-胜意 */
        endCityCodeSy: string

        /* 行程目的地 */
        endCity: string

        /* 目的省编码 */
        endProvinceCode: string

        /* 目的地省名称 */
        endProvinceName: string

        /* 目的地省编码(胜意) */
        endProvinceCodeSy: string

        /* 出差日期始 */
        beginDate: string

        /* 出差日期始 */
        endDate: string

        /* 出差日期始(实际) */
        realBeginDate: string

        /* 省、市编码 */
        beginCityCodeStrs: Record<string, unknown>[]

        /* 省、市编码 */
        endCityCodeStrs: Record<string, unknown>[]

        /* 主键id */
        realEndDate: string

        /* 创建人 */
        createBy: string

        /* 创建人名称 */
        createName: string

        /* 最后一次修改人 */
        lastModifiedBy: string

        /* 最后一次修改人名称 */
        lastModifiedName: string

        /* 创建时间 */
        gmtCreate: Record<string, unknown>

        /* 修改时间 */
        gmtModified: Record<string, unknown>

        /* 行程明细关系集合 */
        tripDetailMapList: {
          /* 主键id */
          id: number

          /* 申请单号 */
          applyId: number

          /* 行程表id */
          tripId: number

          /* 产品编码 */
          productCode: string

          /* 产品名称 */
          productName: string

          /* 费用预算总计 */
          budgetAmount: Record<string, unknown>

          /* 是否超标0未超标1已超标默认0 */
          excessiveFlag: number

          /* 超标原因 */
          excessiveReason: string

          /* 出发地火车站编码 */
          startTrainCode: string

          /* 目的地火车站编码 */
          endTrainCode: string

          /* 总价公式 */
          budgetAmountDesc: string

          /* 创建人 */
          createBy: string

          /* 创建人名称 */
          createName: string

          /* 最后一次修改人 */
          lastModifiedBy: string

          /* 最后一次修改人名称 */
          lastModifiedName: string

          /* 创建时间 */
          gmtCreate: Record<string, unknown>

          /* 修改时间 */
          gmtModified: Record<string, unknown>

          /* 是否购买保险 */
          insuranceFlag: boolean

          /* 保险金额，购买保险时必传 */
          insuranceAmount: Record<string, unknown>

          /* 总价标准 */
          budgetAmountBz: Record<string, unknown>

          /* 出发地火车站编码集合 */
          startTrainCodeArray: Record<string, unknown>[]

          /* 目的地火车站编码集合 */
          endTrainCodeArray: Record<string, unknown>[]

          /* 行程明细集合 */
          travelApplyTripDetailList: {
            /* 主键id */
            id: number

            /* 申请单id */
            applyId: number

            /* 行程表id */
            tripId: number

            /* 行程产品关系表id */
            tripDetailMapId: number

            /* 费用名称 */
            costName: string

            /* 出差人id(胜意) */
            travelUserSyId: string

            /* 出差人名称 */
            travelUserName: string

            /* 出差人工号 */
            travelUserNo: string

            /* 出差人部门id */
            travelUserDeptId: string

            /* 出差人部门名称 */
            travelUserDeptName: string

            /* 出差人类型0出差人1外部出行人 */
            travelUserType: string

            /* 费用预算 */
            budgetAmount: Record<string, unknown>

            /* 差标 */
            differentialStandard: string

            /* 产品类型1费用2保险3服务费 */
            productType: number

            /* 出发地火车站编码 */
            startTrainCode: string

            /* 目的地火车站编码 */
            endTrainCode: string

            /* 出发地火车站编码集合 */
            startTrainCodeArray: Record<string, unknown>[]

            /* 目的地火车站编码集合 */
            endTrainCodeArray: Record<string, unknown>[]
          }[]
        }[]

        /* 出行方式聚合 */
        travelModeAggregate: string
      }[]

      /* 附件 */
      fileList: {
        /* 主键id */
        id: number

        /* 申请单号 */
        applyId: number

        /* 附件名称 */
        fileName: string

        /* 附件预览地址 */
        filePath: string

        /* 创建人 */
        createBy: string

        /* 创建人名称 */
        createName: string

        /* 最后一次修改人 */
        lastModifiedBy: string

        /* 最后一次修改人名称 */
        lastModifiedName: string

        /* 创建时间 */
        gmtCreate: Record<string, unknown>

        /* 修改时间 */
        gmtModified: Record<string, unknown>
      }[]
    }[]
  }

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}

// 获取差旅标准响应
export interface TrainStandardRes {
  describes?: string
  productCode?: string
  productName?: string
}

export interface QueryCertificatesRes {
  /* */
  createBy: string

  /* */
  createName: string

  /* */
  lastModifiedBy: string

  /* */
  lastModifiedName: string

  /* */
  gmtCreate: Record<string, unknown>

  /* */
  gmtModified: Record<string, unknown>

  /* */
  id: number

  /* */
  certificatesCode: string

  /* */
  certificates: string
}

export interface ICreatOutPersonReq {
  /* 姓名 */
  name?: string

  /* 英文名 */
  englishName?: string

  /* 国籍/地区 */
  nature?: Record<string, unknown>[]

  /* 性别1男2女 */
  sex?: string

  /* 出生日期 */
  birthday?: string

  /* 乘机人类型 */
  travelType?: string

  /* 联系电话 */
  telephone?: string

  /* 邮箱 */
  email?: string

  /* 电话号码 */
  phone?: string

  /* 服务保障等级 */
  promiseLevel?: string

  /* 证件类型 */
  cardType?: string

  /* 证件号 */
  cardNo?: string

  /* 有效期始 */
  effectBeginDate?: string

  /* 有效期止 */
  effectEndDate?: string
}

export interface ExternalPersonnelReq {
  size: number
  current: number
  sfmrss: number
}

export interface QueryServiceGuaranteeRes {
  /* */
  createBy: string

  /* */
  createName: string

  /* */
  lastModifiedBy: string

  /* */
  lastModifiedName: string

  /* */
  gmtCreate: Record<string, unknown>

  /* */
  gmtModified: Record<string, unknown>

  /* */
  id: number

  /* */
  serviceGuarantee: string
}

// 响应接口
export interface BudgeQueryRes {
  /* */
  data: ICreatTrip

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}

// 城市相应接口
export interface QueryCityListRes {
  /* */
  data: Record<string, unknown>[]

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}
