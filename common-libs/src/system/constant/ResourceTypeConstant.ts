type keys = 'INTERFACE' | 'PAGE' | 'PAGE_MENU' | 'PAGE_GROUP' | 'WIDGET' | 'MANAGE_APPLICATION' | 'APPLICATION'

/**
 * 资源类型
 */
export const ResourceTypeConstant = {
  INTERFACE: { type: 1, name: '接口' },
  PAGE: { type: 2, name: '页面' },
  PAGE_MENU: { type: 3, name: '页面（菜单）' },
  PAGE_GROUP: { type: 4, name: '页面组' },
  WIDGET: { type: 5, name: '组件' },
  MANAGE_APPLICATION: { type: 6, name: '应用（管理）' },
  APPLICATION: { type: 7, name: '应用' },

  ofType: (type?: number): { type: number; name: string } | null => {
    for (const key in ResourceTypeConstant) {
      const item = ResourceTypeConstant[key as keys]
      if (type === item.type) {
        return item
      }
    }
    return null
  },
}
