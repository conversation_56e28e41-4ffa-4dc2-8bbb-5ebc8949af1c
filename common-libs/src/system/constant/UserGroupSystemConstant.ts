type keys = 'GUEST' | 'SUPER_MANAGE' | 'LOGIN' | 'REPORT_CONTROL' | 'PPT_CONTROL'

/**
 * 当前组在系统中被设置为常量，参与了代码逻辑的判断，不可删除
 */
export const UserGroupSystemConstant = {
  GUEST: { groupId: 1, name: '访客组' },
  SUPER_MANAGE: { groupId: 2, name: '超级管理员' },
  LOGIN: { groupId: 3, name: '登录组' },
  BAN: { groupId: 4, name: '用户禁止访问组' },

  // 数据中台
  REPORT_CONTROL: { groupId: 156, name: '报表控制角色组' },
  PPT_CONTROL: { groupId: 168, name: 'PPT控制角色组' },

  // 日清系统
  /** 平台主,组id*/
  DAILY_PLATFORM_MANAGER: { groupId: parseInt(import.meta.env.VITE_GROUP_PLATFORM_MANAGER), name: '日清平台主组' },
  /** 三自,组id*/
  DAILY_THREE_SELF: { groupId: parseInt(import.meta.env.VITE_GROUP_THREE_SELF), name: '日清三自组' },
  /** 小微主,组id*/
  DAILY_MICRO: { groupId: parseInt(import.meta.env.VITE_GROUP_MICRO), name: '小微主组' },
  /** 战略,组id*/
  DAILY_STRATEGY: { groupId: parseInt(import.meta.env.VITE_GROUP_STRATEGY), name: '日清战略组' },
  /** 财务,组id*/
  DAILY_FINANCE: { groupId: parseInt(import.meta.env.VITE_GROUP_FINANCE), name: '日清财务组' },
  /** 人力,组id*/
  DAILY_HUMAN_RESOURCES: { groupId: parseInt(import.meta.env.VITE_GROUP_HUMAN_RESOURCES), name: '日清人力组' },
  /** 风控,组id*/
  DAILY_RISK_MANAGEMENT: { groupId: parseInt(import.meta.env.VITE_GROUP_RISK_MANAGEMENT), name: '日清风控组' },
  /** 特权组,组id*/
  DAILY_SPECIAL: { groupId: parseInt(import.meta.env.VITE_GROUP_SPECIAL), name: '日清特权组' },

  of: (groupId?: number): { groupId: number; name: string } | null => {
    for (const key in UserGroupSystemConstant) {
      const item = UserGroupSystemConstant[key as keys]
      if (groupId === item.groupId) {
        return item
      }
    }
    return null
  },
  values: (filed: string): any[] => {
    return Object.values(UserGroupSystemConstant)
      .map((it) => (it as any)[filed])
      .filter((it) => it != undefined)
  },
}
