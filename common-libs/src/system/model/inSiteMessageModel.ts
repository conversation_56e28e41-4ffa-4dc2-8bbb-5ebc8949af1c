import { IPageRequest } from '../../basic'

export class IInSiteMessageFilter extends IPageRequest {
  // 接收消息的用户
  userName?: string
  // 主题
  subject?: string
  // 消息体
  content?: string
  // 发送应用
  applicationCode?: string
  // 已读开始时间（区间查询）
  readStartTime?: string
  // 已读结束时间（区间查询）
  readEndTime?: string
  // 发送开始时间（区间查询）
  startTime?: string
  // 发送结束时间（区间查询）
  endTime?: string
  // 0已读 1未读
  isRead?: number
  // 已读时间
  readTime?: string
  // 发送时间
  gmtCreate?: string
  begin?: string
  end?: string
}

export interface IInSiteMessage {
  id: number
  creator?: string
  createTime?: string
  updater?: string
  updateTime?: string
  // 接收消息的用户
  userName?: string
  // 主题
  subject?: string
  // 消息体
  content?: string
  // 发送应用
  applicationCode?: string
  // 链接
  url?: string
  // 已读开始时间（区间查询）
  readStartTime?: string
  // 已读结束时间（区间查询）
  readEndTime?: string
  // 发送开始时间（区间查询）
  startTime?: string
  // 发送结束时间（区间查询）
  endTime?: string
  // 0已读 1未读
  isRead?: number
  // 已读时间
  readTime?: string
  // 发送时间
  gmtCreate?: string
}
