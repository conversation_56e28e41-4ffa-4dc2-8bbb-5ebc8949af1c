export interface SearchListRes {
  /* 主键ID */
  id?: number

  /* 通知标题 */
  title?: string

  /* 通知内容 */
  content?: string

  /* 经办人 */
  operator?: string

  /* 经办人姓名 */
  operatorName?: string

  /* 接收角色，多个用英文逗号分隔 */
  receiverRole?: string

  /* 计划发送时间 */
  scheduledTime?: Record<string, unknown>

  /* 实际发送时间 */
  actualSendTime?: Record<string, unknown>

  /* 图片BASE64编码 */
  imageBase64?: string

  /* 飞书图片存储ID */
  imageFeishuId?: string

  /* 跳转链接地址 */
  linkUrl?: string

  /* 通知模板名称 */
  templateName?: string

  /* 通知模板编码 */
  templateCode?: string

  /* 审批状态(10未提交/20审批中/30审批完成)，新增时 */
  approvalStatus?: string

  /* 审批完成时间 */
  approvalCompleteTime?: Record<string, unknown>

  /* 业务单号-自动生成 */
  serialNumber?: string

  /* 发送状态(10待发送/15发送中/20发送完成/30已撤回) */
  sendStatus?: string

  /* 1即时发送 2定时发送 */
  sendType?: number

  /* 计划发送时间开始 */
  scheduledTimeBegin?: Record<string, unknown>

  /* 计划发送时间截止 */
  scheduledTimeEnd?: Record<string, unknown>
}

export interface Send_1Params {
  /* 主键ID */
  id?: number

  /* 通知标题 */
  title?: string

  /* 通知内容 */
  content?: string

  /* 经办人 */
  operator?: string

  /* 经办人姓名 */
  operatorName?: string

  /* 接收角色，多个用英文逗号分隔 */
  receiverRole?: string

  /* 计划发送时间 */
  scheduledTime?: Record<string, unknown>

  /* 实际发送时间 */
  actualSendTime?: Record<string, unknown>

  /* 图片BASE64编码 */
  imageBase64?: string

  /* 飞书图片存储ID */
  imageFeishuId?: string

  /* 跳转链接地址 */
  linkUrl?: string

  /* 通知模板名称 */
  templateName?: string

  /* 通知模板编码 */
  templateCode?: string

  /* 审批状态(10未提交/20审批中/30审批完成) */
  approvalStatus?: string

  /* 审批完成时间 */
  approvalCompleteTime?: Record<string, unknown>

  /* 业务单号-自动生成 */
  serialNumber?: string

  /* 发送状态(10待发送/15发送中/20发送完成/30已撤回) */
  sendStatus?: string

  /* 1即时发送 2定时发送 */
  sendType?: number | string

  /* */
  file?: Record<string, unknown>

  /* */
  fixUserDTOList?: {
    /* */
    username?: string

    /* */
    nickName?: string

    /* */
    notificationId?: number
  }[]
}

export interface SearchTemplateRes {
  /* */
  id: number

  /* */
  code: string

  /* */
  content: string

  /* */
  description: string

  /* */
  msgType: number

  /* */
  selectFlag: string

  /* 预设模板包含的元素种类，按位与后得到实际元素，1正文 2链接 4图片 */
  element: number
}

export interface GetByIdRes {
  /* */
  data: {
    /* 主键ID */
    id?: number

    /* 通知标题 */
    title?: string

    /* 通知内容 */
    content?: string

    /* 经办人 */
    operator?: string

    /* 经办人姓名 */
    operatorName?: string

    /* 接收角色，多个用英文逗号分隔 */
    receiverRole?: string

    /* 计划发送时间 */
    scheduledTime?: Record<string, unknown>

    /* 实际发送时间 */
    actualSendTime?: Record<string, unknown>

    /* 图片BASE64编码 */
    imageBase64?: string

    /* 飞书图片存储ID */
    imageFeishuId?: string

    /* 跳转链接地址 */
    linkUrl?: string

    /* 通知模板名称 */
    templateName?: string

    /* 通知模板编码 */
    templateCode?: string

    /* 审批状态(10未提交/20审批中/30审批完成) */
    approvalStatus?: string

    /* 审批完成时间 */
    approvalCompleteTime?: Record<string, unknown>

    /* 业务单号-自动生成 */
    serialNumber?: string

    /* 发送状态(10待发送/15发送中/20发送完成/30已撤回) */
    sendStatus?: string

    /* 1即时发送 2定时发送 */
    sendType?: number

    /* */
    file?: Record<string, unknown>
  }

  /* */
  code: string

  /* */
  message: string

  /* */
  success: boolean
}
