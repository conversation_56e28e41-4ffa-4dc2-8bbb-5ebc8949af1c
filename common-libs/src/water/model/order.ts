// 引入方式 import { xxx } from '@/api/water'

/**
 * @申请单
 * */

/* 查询表单 */
export interface WaterApplicationTableForm {
  applicationForm: string // 申请单号
  orderDown: string // 申请单位
  orderPrint: string // 结算公司
  orderState: string | null // 订单状态
  payState: string | null // paymentStatus
}

/* 模态框表单 */
export interface WaterApplicationModalForm {
  applicationDate: string
  type: string
  deliveryArea: string
  receiver: string
  remark: string
  deliveryAddress: string
  contactPhone: string | null
}

/**
 * @送水申请
 * */

/*  查询表单 */
export interface WaterDeliveryTableForm {
  deliveryForm: string // 送水单号
  orderDown: string // 送水单位
  orderPrint: string // 结算公司
}

/* 模态框表单 */
export interface WaterDeliveryModalForm {
  payType: string[]
}
