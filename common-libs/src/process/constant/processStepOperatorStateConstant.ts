type keys = 'PASS' | 'REJECT' | 'ABSTENTION'

/**
 * 审批待办状态
 * 1: 待审批 2: 已审批 3:已撤回
 */
export const ProcessStepOperatorStateConstant = {
  PASS: { type: 1, name: '通过' },
  REJECT: { type: 2, name: '驳回' },
  ABSTENTION: { type: 3, name: '未抉择' },

  ofType: (type?: number): { type: number; name: string } | null => {
    for (const key in ProcessStepOperatorStateConstant) {
      const item = ProcessStepOperatorStateConstant[key as keys]
      if (type === item.type) {
        return item
      }
    }
    return null
  },
}
