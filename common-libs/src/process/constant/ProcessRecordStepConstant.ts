type keys = 'PASS' | 'REJECT' | 'SKIP' | 'EXEMPTION' | 'CANCEL'

export const ProcessRecordStepConstant = {
  PASS: { type: 1, name: '通过' },
  REJECT: { type: 2, name: '驳回' },
  SKIP: { type: 3, name: '跳审(重复审批人)' },
  EXEMPTION: { type: 4, name: '免审' },
  CANCEL: { type: 5, name: '取消' },

  ofType: (type?: number): { type: number; name: string } | null => {
    for (const key in ProcessRecordStepConstant) {
      const item = ProcessRecordStepConstant[key as keys]
      if (type === item.type) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ type: number; name: string } | undefined)[] => {
    const types = Object.keys(ProcessRecordStepConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return ProcessRecordStepConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
