type keys = 'UNTRIGGERED' | 'APPROVAL' | 'COMPLETE'

/**
 * 审批步骤状态枚举
 * 0: 未触发 10: 审批中 20: 审批完成
 */
export const ProcessStepStateConstant = {
  UNTRIGGERED: { type: 0, name: '未触发' },
  APPROVAL: { type: 10, name: '审批中' },
  COMPLETE: { type: 20, name: '审批完成' },

  ofType: (type?: number): { type: number; name: string } | null => {
    for (const key in ProcessStepStateConstant) {
      const item = ProcessStepStateConstant[key as keys]
      if (type === item.type) {
        return item
      }
    }
    return null
  },
}
