type keys = 'CUSTOMER_LOSSES' | 'OTHER'
type types = 'STAY' | 'CATERING' | 'VEHICLE' | 'PLACE' | 'PRESENT' | 'MANPOWER' | 'SERVICE' | 'OTHER' | 'INSURANCE'

export const SupplementTypeConstant = {
  CUSTOMER_LOSSES: { code: 1, desc: '客损' },
  OTHER: { code: 0, desc: '其他' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in SupplementTypeConstant) {
      const item = SupplementTypeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (type: types): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(SupplementTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        if (type !== 'STAY' && i === 'CUSTOMER_LOSSES') {
          return undefined
        }
        return SupplementTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
