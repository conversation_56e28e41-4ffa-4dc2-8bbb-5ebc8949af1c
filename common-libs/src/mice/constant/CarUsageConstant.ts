type keys = 'DOMESTIC' | 'ABROAD'

export const CarUsageConstant = {
  DOMESTIC: { code: 1, desc: '单趟' },
  ABROAD: { code: 2, desc: '包车' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in CarUsageConstant) {
      const item = CarUsageConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(CarUsageConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return CarUsageConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
