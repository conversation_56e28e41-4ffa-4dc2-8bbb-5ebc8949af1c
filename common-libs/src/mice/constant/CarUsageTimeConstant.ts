type keys = 'DOMESTIC' | 'ABROAD'

export const CarUsageTimeConstant = {
  DOMESTIC: { code: 1, desc: '半天（4小时）' },
  ABROAD: { code: 2, desc: '全天（8小时）' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in CarUsageTimeConstant) {
      const item = CarUsageTimeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(CarUsageTimeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return CarUsageTimeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
