type keys = 'CHARGE' | 'GIVE'

export const TeaUseTypeConstant = {
  CHARGE: { code: 1, desc: '收费' },
  GIVE: { code: 2, desc: '赠送' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in TeaUseTypeConstant) {
      const item = TeaUseTypeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(TeaUseTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return TeaUseTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
