type keys = 'STAY' | 'CATERING' | 'VEHICLE' | 'PLACE' | 'PRESENT' | 'MANPOWER' | 'SERVICE' | 'OTHER' | 'INSURANCE'

export const ConferenceTypeConstant = {
  STAY: { code: 1, desc: '住宿' },
  CATERING: { code: 2, desc: '餐饮' },
  VEHICLE: { code: 3, desc: '车辆' },
  PLACE: { code: 4, desc: '会场' },
  PRESENT: { code: 5, desc: '礼品' },
  MANPOWER: { code: 6, desc: '人工费' },
  SERVICE: { code: 7, desc: '全单服务费' },
  OTHER: { code: 8, desc: '其他' },
  INSURANCE: { code: 9, desc: '保险' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in ConferenceTypeConstant) {
      const item = ConferenceTypeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(ConferenceTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return ConferenceTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
