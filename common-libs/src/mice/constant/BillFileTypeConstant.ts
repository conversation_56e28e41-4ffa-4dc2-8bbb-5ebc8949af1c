// BillFileTypeConstant

type keys = 'INVOICE' | 'MEMO' | 'CONTRACT' | 'OTHER'

export const BillFileTypeConstant = {
  INVOICE: { code: 1, desc: '发票' },
  MEMO: { code: 2, desc: '水单' },
  CONTRACT: { code: 3, desc: '一手合同' },
  OTHER: { code: 4, desc: '其他' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in BillFileTypeConstant) {
      const item = BillFileTypeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(BillFileTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return BillFileTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
