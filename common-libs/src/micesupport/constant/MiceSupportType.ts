type keys = keyof typeof MiceSupportTypeConstant

export const MiceSupportTypeConstant = {
  // 会议类型定义
  OPENING_SESSION: { code: 1, desc: '开盘会' },
  CLIENT_MEETING: { code: 2, desc: '客户会' },
  TRAINING_SESSION: { code: 3, desc: '培训会' },
  PRODUCT_LAUNCH: { code: 4, desc: '发布会' },
  EXHIBITION: { code: 5, desc: '展览会' },
  INTERNAL_COMMUNICATION: { code: 6, desc: '内部沟通会' },
  INVESTMENT_FORUM: { code: 7, desc: '招商会' },

  // 工具方法
  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in MiceSupportTypeConstant) {
      const item = MiceSupportTypeConstant[key as keys]
      if (typeof item === 'object' && item?.code === type) {
        return item
      }
    }
    return null
  },

  toArray: (): { code: number; desc: string }[] => {
    const types = Object.keys(MiceSupportTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceSupportTypeConstant[i as keys]
      }
      return null
    })
    return types.filter(
      (s): s is { code: number; desc: string } => s !== null && typeof s === 'object' && 'code' in s && 'desc' in s,
    )
  },
}

export const MiceSupportStatusConstant = {
  // 需求状态
  DRAFT: { code: 0, desc: '草稿' },
  SUBMITTED: { code: 1, desc: '已提交' },
  IN_PROGRESS: { code: 2, desc: '进行中' },
  COMPLETED: { code: 3, desc: '已完成' },
  CANCELLED: { code: 4, desc: '已取消' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in MiceSupportStatusConstant) {
      const item = MiceSupportStatusConstant[key as keyof typeof MiceSupportStatusConstant]
      if (typeof item === 'object' && item?.code === type) {
        return item
      }
    }
    return null
  },

  toArray: (): { code: number; desc: string }[] => {
    const statuses = Object.keys(MiceSupportStatusConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceSupportStatusConstant[i as keyof typeof MiceSupportStatusConstant]
      }
      return null
    })
    return statuses.filter(
      (s): s is { code: number; desc: string } => s !== null && typeof s === 'object' && 'code' in s && 'desc' in s,
    )
  },
}

export const MiceSupportDistrictTypeConstant = {
  // 会议地点类型定义
  DOMESTIC: { code: 0, desc: '国内' },
  INTERNATIONAL: { code: 1, desc: '国际' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in MiceSupportDistrictTypeConstant) {
      const item = MiceSupportDistrictTypeConstant[key as keyof typeof MiceSupportDistrictTypeConstant]
      if (typeof item === 'object' && item?.code === type) {
        return item
      }
    }
    return null
  },

  toArray: (): { code: number; desc: string }[] => {
    const types = Object.keys(MiceSupportDistrictTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceSupportDistrictTypeConstant[i as keyof typeof MiceSupportDistrictTypeConstant]
      }
      return null
    })
    return types.filter(
      (s): s is { code: number; desc: string } => s !== null && typeof s === 'object' && 'code' in s && 'desc' in s,
    )
  },
}

export const attendeeMiceStatusConstant = {
  // 会议地点类型定义
  WAIT_APPROVE: { code: 10, desc: '待审核' },
  APPROVE_PASS: { code: 20, desc: '审核通过' },
  APPROVE_REJECT: { code: 30, desc: '审核驳回' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in attendeeMiceStatusConstant) {
      const item = attendeeMiceStatusConstant[key as keyof typeof attendeeMiceStatusConstant]
      if (typeof item === 'object' && item?.code === type) {
        return item
      }
    }
    return null
  },

  toArray: (): { code: number; desc: string }[] => {
    const types = Object.keys(attendeeMiceStatusConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return attendeeMiceStatusConstant[i as keyof typeof attendeeMiceStatusConstant]
      }
      return null
    })
    return types.filter(
      (s): s is { code: number; desc: string } => s !== null && typeof s === 'object' && 'code' in s && 'desc' in s,
    )
  },
}
