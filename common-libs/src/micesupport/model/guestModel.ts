import { IPageRequest } from '../../basic'

/* 会议嘉宾 */
export class IMeetingGuestFilter extends IPageRequest {
  /* 会议ID */
  miceId?: number
}

/* 会议嘉宾字段 */
export class IMeetingGuest {
  index?: number | null
  miceCode?: number
  /* 主键 */
  id?: number

  /* 会议表主键 */
  miceInfoId?: number

  /* 嘉宾来源 */
  guestSource?: number

  /* 姓名 */
  name?: string

  /* 所属单位名称 */
  companyName?: string

  /* 所属单位code */
  companyCode?: string

  /* 嘉宾头衔 */
  title?: string

  /* 嘉宾简介 */
  description?: string

  /* 禁忌与偏好 */
  specialRequest?: string

  /* 备注 */
  remark?: string

  /* 排序 */
  sort?: number

  /* 照片id */
  photoId?: number | null

  /* 照片类型 */
  type?: number

  /* 照片 */
  imageUrl?: string
}
