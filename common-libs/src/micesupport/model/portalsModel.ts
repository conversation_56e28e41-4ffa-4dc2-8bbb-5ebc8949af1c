// 参数接口
export interface MiceSupportCreateOrderParams {
  /* 流程定义主表id */
  pdMainId?: number

  /* 会议名称 */
  miceName?: string

  /* 需求开始时间 */
  startDate?: string

  /* 需求结束时间 */
  endDate?: string

  /* 需求提报类型enum?:[[{"code"?:0,"desc"?:"用户提报"},{"code"?:1,"desc"?:"顾问代提"}]],可用值?:0,1 */
  demandType?: number

  /* 会议地点 国内/国际enum?:[[{"code"?:0,"desc"?:"国内"},{"code"?:1,"desc"?:"国际"}]],可用值?:0,1 */
  districtType?: number

  /* 会议人数 */
  personTotal?: number

  /* 会议类型enum?:[[{"code"?:1,"desc"?:"开盘会"},{"code"?:2,"desc"?:"客户会"},{"code"?:3,"desc"?:"培训会"},{"code"?:4,"desc"?:"发布会"},{"code"?:5,"desc"?:"展览会"},{"code"?:6,"desc"?:"内部沟通会"},{"code"?:7,"desc"?:"招商会"}]],可用值?:1,2,3,4,5,6,7 */
  miceType?: number

  /* 意向会务顾问工号 */
  intentionConsultantUserCode?: string
}

export interface MiceSupportConsultant {
  /* 主键id */
  id?: number

  /* 工作年限 */
  seniority?: number

  /* 顾问介绍 */
  description?: string

  /* 图片路径 */
  path?: string

  /* 顾问姓名 */
  name?: string

  /* 顾问工号 */
  userCode?: string

  /* 擅长领域 */
  expertise?: string

  /* 服务评分 */
  rating?: number
}

export interface MiceSupportCalendarFile {
  /* 文件id */
  id?: number

  /* 文件路径 */
  path?: string

  /* 文件名称 */
  fileName?: string

  /* 文件大小 */
  fileSize?: number
}

export interface MiceSupportCalendar {
  /* 主键id */
  id?: number

  /* 会议名称 */
  name?: string

  /* 会议时间 */
  time?: string

  /* 会议城市 */
  city?: string

  /* 会议地点 */
  place?: string

  /* 会议面积 */
  floorSpace?: number

  /* 会议来源 */
  meetingType?: number

  /* 文件地址 */
  pathList?: MiceSupportCalendarFile[]

  /* 会议状态 */
  status?: number

  /* 创建时间 */
  createTime?: string

  /* 更新时间 */
  updateTime?: string
}

// 支持相关的接口
export interface MiceSupportDemand {
  /* 需求id */
  id?: number

  /* 需求编号 */
  demandCode?: string

  /* 会议名称 */
  miceName?: string

  /* 需求描述 */
  description?: string

  /* 需求状态 */
  status?: number

  /* 创建人 */
  createBy?: string

  /* 创建时间 */
  createTime?: string

  /* 预算金额 */
  budget?: number

  /* 参会人数 */
  attendeeCount?: number

  /* 会议开始时间 */
  startDate?: string

  /* 会议结束时间 */
  endDate?: string

  /* 会议地点 */
  location?: string

  /* 会议类型 */
  miceType?: number
}
