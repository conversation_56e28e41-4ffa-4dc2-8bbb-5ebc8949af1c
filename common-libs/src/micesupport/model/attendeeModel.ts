import { IPageRequest } from '../../basic'

/* 获取参会人列表 */
export class IMeetingAttendeeFilter extends IPageRequest {
  /* 会议表主键 */
  miceInfoId?: number
  /* 报名审核状态 */
  approveState?: number | null
}

/* 参会人 */
export class IMeetingAttendee {
  /* 主键 */
  id?: number

  /* 会议表主键 */
  miceInfoId?: number

  /* 参会人企业code */
  enterpriseCode?: string

  /* 参会人企业名称 */
  enterpriseName?: string

  /* 参会人工号 */
  userName?: string

  /* 参会人姓名 */
  nickName?: string

  /* 性别 */
  sex?: number

  /* 手机号 */
  phone?: string

  /* 是否需要住宿 0否1是 */
  isStay?: boolean | number

  /* 身份证号 */
  idCard?: string

  /* 标签id */
  tagId?: string[] | string

  /* 标签名字 */
  tagName?: string[] | string

  /* 所属单位名称 */
  companyName?: string

  /* 所属部门名称 */
  departName?: string

  /* 禁忌与偏好 */
  specialRequest?: string

  /* 备注 */
  remark?: string

  /* 是否需要座牌 0否 1是 */
  isSeatSign?: boolean | number

  /* 是否需要胸牌 0否 1是 */
  isBreastPiece?: boolean | number

  /* 参会人来源 */
  userSource?: number

  /* 报名审核状态 */
  approveState?: number

  /* 创建时间 */
  gmtCreate?: string | Record<string, unknown>
}

/* 导出 */
export class IMeetingAttendeeFile {
  /* 会议表主键 */
  miceInfoId?: number

  /* 文件 */
  file?: string
}

/* 会议详情*/
export class IMeetingDetails {
  /* 主键 */
  id?: number

  /* 会议单号 */
  miceCode?: string

  /* 会议名称 */
  miceName?: string

  /* 会议来源(枚举) 1 会务导入 2 会中创建 */
  miceSource?: number

  /* 会议开始时间 */
  miceStartDate?: string

  /* 会议结束时间 */
  miceEndDate?: string

  /* 会议酒店名称, 多个用,隔开 */
  miceHotelName?: string

  /* 会议酒店编码, 多个用,隔开 */
  miceHotelCode?: string

  /* 关联会议名称, 多个用,隔开 */
  miceConnectMeetingId?: string

  /* 经办人工号 */
  operatorCode?: string | string[]

  /* 经办人姓名 */
  operatorName?: string | string[]

  /* 会议顾问工号 */
  consultantUserCode?: string | string[]

  /* 会议顾问姓名 */
  consultantUsername?: string | string[]

  /* 是否支持报名登记 */
  isSignUp?: boolean

  /* 报名链接 */
  signUpUrl?: string

  /* 是否开启AI助手 */
  aiOpen?: boolean

  /* 创建时间 */
  gmtCreate?: Record<string, unknown>

  /* 更新时间 */
  gmtModified?: Record<string, unknown>
}
