import { IPageRequest } from '../../basic'

// 收款确认筛选接口
export class IPaymentFromFilter extends IPageRequest {
  begin?: string
  end?: string
  startTime?: string
  endTime?: string
  serviceProviderName?: string
  merchantCode?: string // 服务商
  receivePaymentCode?: string // 业务单号
  applicationCode?: string // 业务类别
  state?: number // 状态
  status?: number
}

// 收款记录接口
export interface ReceiptRecord {
  id: string
  sapReceiveNo: string
  merchantName: string
  payTime: string
  amount: number
  originalData?: {
    budat?: string
    belnr?: string
    kunnr?: string
    vtext?: string
    txt50?: string
    zuonr?: string
    xref1?: string
    dmbtr?: string
    name1?: string
    [key: string]: any
  }
}

// 收款搜索参数
export interface ReceiptSearchParam {
  merchantName: string
  payTimeStart: any | null
  payTimeEnd: any | null
  confirmTime?: null
  beginAndEnd?: null
  code?: string[]
}