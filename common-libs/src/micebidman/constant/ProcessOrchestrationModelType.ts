// 流程编排模型状态枚举
export const ProcessOrchestrationModelStatusEnum = {
  DISABLED: 0, // 停用
  ENABLED: 1, // 启用

  // 获取状态文本
  getStatusText: (status?: number): string => {
    if (status === ProcessOrchestrationModelStatusEnum.ENABLED) {
      return '启用'
    }
    if (status === ProcessOrchestrationModelStatusEnum.DISABLED) {
      return '停用'
    }
    return '未知'
  },

  // 获取状态样式类名
  getStatusClass: (status?: number): string => {
    if (status === ProcessOrchestrationModelStatusEnum.ENABLED) {
      return 'status-active'
    }
    if (status === ProcessOrchestrationModelStatusEnum.DISABLED) {
      return 'status-used'
    }
    return ''
  },

  // 获取状态选项，用于下拉框
  getStatusOptions: (): Array<{ value: number; label: string }> => {
    return [
      { value: ProcessOrchestrationModelStatusEnum.ENABLED, label: '启用' },
      { value: ProcessOrchestrationModelStatusEnum.DISABLED, label: '停用' },
    ]
  },
}

// 流程编排服务类型枚举
export const ProcessOrchestrationServiceTypeEnum = {
  STAY: 1, // 住宿
  PLACE: 2, // 会场
  CATERING: 4, // 用餐
  VEHICLE: 8, // 用车
  ATTENDANT: 16, // 服务人员
  ACTIVITY: 32, // 拓展活动
  INSURANCE: 64, // 保险
  MATERIAL: 128, // 布展物料
  TRAFFIC: 256, // 交通
  PRESENT: 512, // 礼品
  OTHER: 1024, // 其他
  FULL_SERVICE_FEE: 2048, // 全单服务费
  PLATFORM_SERVICE_FEE: 4096, // 平台服务费

  // 获取服务类型文本
  getTypeText: (type?: number): string => {
    if (type === ProcessOrchestrationServiceTypeEnum.STAY) {
      return '住宿'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.PLACE) {
      return '会场'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.CATERING) {
      return '用餐'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.VEHICLE) {
      return '用车'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.ATTENDANT) {
      return '服务人员'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.ACTIVITY) {
      return '拓展活动'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.INSURANCE) {
      return '保险'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.MATERIAL) {
      return '布展物料'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.TRAFFIC) {
      return '交通'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.PRESENT) {
      return '礼品'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.OTHER) {
      return '其他'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.FULL_SERVICE_FEE) {
      return '全单服务费'
    }
    if (type === ProcessOrchestrationServiceTypeEnum.PLATFORM_SERVICE_FEE) {
      return '平台服务费'
    }
    return '未知'
  },

  // 获取服务类型选项，用于下拉框
  getTypeOptions: (): Array<{ value: number; label: string }> => {
    return [
      { value: ProcessOrchestrationServiceTypeEnum.STAY, label: '住宿' },
      { value: ProcessOrchestrationServiceTypeEnum.PLACE, label: '会场' },
      { value: ProcessOrchestrationServiceTypeEnum.CATERING, label: '用餐' },
      { value: ProcessOrchestrationServiceTypeEnum.VEHICLE, label: '用车' },
      { value: ProcessOrchestrationServiceTypeEnum.ATTENDANT, label: '服务人员' },
      { value: ProcessOrchestrationServiceTypeEnum.ACTIVITY, label: '拓展活动' },
      { value: ProcessOrchestrationServiceTypeEnum.INSURANCE, label: '保险' },
      { value: ProcessOrchestrationServiceTypeEnum.MATERIAL, label: '布展物料' },
      { value: ProcessOrchestrationServiceTypeEnum.TRAFFIC, label: '交通' },
      { value: ProcessOrchestrationServiceTypeEnum.PRESENT, label: '礼品' },
      { value: ProcessOrchestrationServiceTypeEnum.OTHER, label: '其他' },
      { value: ProcessOrchestrationServiceTypeEnum.FULL_SERVICE_FEE, label: '全单服务费' },
      { value: ProcessOrchestrationServiceTypeEnum.PLATFORM_SERVICE_FEE, label: '平台服务费' },
    ]
  },
}

// 会议照片类型枚举
export const ConferencePhotoTypeEnum = {
  BEFORE_MEETING: 'before_meeting', // 会前
  DURING_MEETING: 'during_meeting', // 会中
  AFTER_MEETING: 'after_meeting', // 会后

  // 获取类型文本
  getTypeText: (type?: string): string => {
    if (type === ConferencePhotoTypeEnum.BEFORE_MEETING) {
      return '会前'
    }
    if (type === ConferencePhotoTypeEnum.DURING_MEETING) {
      return '会中'
    }
    if (type === ConferencePhotoTypeEnum.AFTER_MEETING) {
      return '会后'
    }
    return '未知'
  },

  // 获取类型选项，用于下拉框
  getTypeOptions: (): Array<{ value: string; label: string }> => {
    return [
      { value: ConferencePhotoTypeEnum.BEFORE_MEETING, label: '会前' },
      { value: ConferencePhotoTypeEnum.DURING_MEETING, label: '会中' },
      { value: ConferencePhotoTypeEnum.AFTER_MEETING, label: '会后' },
    ]
  },
}
