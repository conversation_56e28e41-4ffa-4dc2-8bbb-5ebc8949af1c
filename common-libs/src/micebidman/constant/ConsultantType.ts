type keys = 'ENABLED' | 'DISABLED'

export const ConsultantType = {
  // 状态类型定义
  ENABLED: { code: 0, desc: '启用' },
  DISABLED: { code: 1, desc: '停用' },

  // 工具方法
  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in ConsultantType) {
      const item = ConsultantType[key as keys]
      if (type === item?.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(ConsultantType).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return ConsultantType[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter((s): s is { code: number; desc: string } => {
      return s !== undefined
    })
    return newTypes
  },
}
