type keys = 'CANCEL' | 'UNDER_APPROVAL' | 'PASS' | 'REJECTED' | 'REVOKE'

export const ApproveListConstant = {
  // 取消
  CANCEL: { code: '0', desc: '取消' },
  // 审批中
  UNDER_APPROVAL: { code: '10', desc: '审批中' },
  // 审批通过
  PASS: { code: '20', desc: '审批通过' },
  // 驳回
  REJECTED: { code: '30', desc: '驳回' },
  // 撤回
  REVOKE: { code: '40', desc: '撤回' },

  // 工具方法
  ofType: (type?: string): { code: string; desc: string } | null => {
    for (const key in ApproveListConstant) {
      const item = ApproveListConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: string; desc: string } | undefined)[] => {
    const types = Object.keys(ApproveListConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return ApproveListConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
