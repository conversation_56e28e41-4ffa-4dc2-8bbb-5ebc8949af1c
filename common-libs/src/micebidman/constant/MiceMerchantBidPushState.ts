type keys = 'WAIT' | 'ALREADY' | 'ABANDON' | 'WON_THE_BID' | 'NOT_THE_BID'

export const BidPushState = {
  // 会议类型定义
  WAIT: { code: 10, desc: '待竞价', color: 'blue' },
  ALREADY: { code: 20, desc: '已竞价', color: 'green' },
  ABANDON: { code: 30, desc: '放弃竞价', color: 'red' },
  WON_THE_BID: { code: 40, desc: '已中标', color: 'cyan' },
  NOT_THE_BID: { code: 50, desc: '未中标', color: 'orange' },

  // 工具方法
  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in BidPushState) {
      const item = BidPushState[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(BidPushState).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return BidPushState[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
