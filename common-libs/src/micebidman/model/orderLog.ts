// 会议日志列表
export interface MiceBidManOrderLogList {
  /** 主键 */
  id?: number
  /** 业务主表id */
  mainId?: number
  /** 日志类型 枚举 */
  type?: number
  /** 描述   */
  remark?: string
  /** 初始状态 */
  originalState?: string
  /** 初始状态描述 */
  originalStateDesc?: string
  /** 结束描述 */
  newState?: string
  /** 结束状态描述   */
  newStateDesc?: boolean
  /** 操作人名称   */
  operator?: string
  /** 操作人工号   */
  operatorCode?: string
  /** 操作时间   */
  gmtCreate?: string
}

// 会议备忘录
export interface MiceBidManMemorandum {
  /** 主键 */
  id?: number
  /** 业务主表id */
  miceId?: number
  /** 订单号   */
  mainCode?: string
  /** 内容   */
  content?: string
  // 完成说明
  completeExplain?: string
  /** 是否已处理 false 未处理 true 已处理 */
  isHandled?: boolean
  attachments?: {
    id?: number
    type?: number
    path?: string
  }[]
  attachmentPaths?: {
    id?: number
    type?: number
    path?: string
  }[]
}

// 会议备忘录
export interface MiceBidManMemorandumfilter {
  /** 主键 */
  id?: number
  /** 业务主表id */
  miceId?: number
  /** 订单号   */
  mainCode?: string
  /** 内容   */
  content?: string | undefined
  // 完成说明
  completeExplain?: string
  /** 是否已处理 false 未处理 true 已处理 */
  isHandled?: boolean
  attachments?: {
    id?: number
    type?: number
    path?: string
  }[]
  attachmentPaths?: {
    id?: number
    type?: number
    path?: string
  }[]
}
