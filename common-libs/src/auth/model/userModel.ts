export interface ILoginUserInfo {
  accountNonExpired?: boolean
  accountNonLocked?: boolean
  authorities?: { authority?: string; group?: number }[]
  credentialsNonExpired?: boolean
  email?: string
  enabled?: string
  enterpriseCode?: string
  enterpriseName?: string
  departmentCode?: string
  departmentName?: string
  id?: string
  nickName?: string
  phone?: string
  username?: string
  extended?: {
    iamToken?: string
  }
}
