import { IPageRequest } from '../../basic'

export interface IReservationReq extends IPageRequest {
  /* */
  pageNum?: number

  /* */
  pageSize?: number

  /* 主键ID */
  id?: number

  /* 预订单号 */
  orderBookingCode?: string

  /* 美团预订单号(订单ID) */
  mtBookingCode?: string

  /* 关联申请单号 */
  orderCode?: string

  /* 对账单号 */
  settleCode?: string

  /* 签字人工号 */
  signerCode?: string

  /* 签字人名称 */
  signerName?: string

  /* 就餐时间 */
  mealTime?: Record<string, unknown>

  /* 就餐时间0开始1结束 */
  mealTimes?: Record<string, unknown>[]

  /* 场景类型1宴请2外卖 */
  sceneType?: number

  /* 餐厅名字 */
  restaurantName?: string

  /* 餐厅地址 */
  restaurantLocation?: string

  /* 餐厅电话 */
  restaurantPhone?: string

  /* 核对状态 0未核对1核对正常2核对异常 */
  checkStatus?: number

  /* 核对时间 */
  checkTime?: Record<string, unknown>

  /* 核对人工号 */
  checkerCode?: string

  /* 核对人名称 */
  checkerName?: string

  /* 关闭时间 */
  closeTime?: Record<string, unknown>

  /* 关闭人工号 */
  closerCode?: string

  /* 关闭人名称 */
  closerName?: string

  /* 用餐地点省份编码 */
  mealLocationProvinceCode?: string

  /* 用餐地点省份 */
  mealLocationProvince?: string

  /* 用餐地点城市编码 */
  mealLocationCityCode?: string

  /* 用餐地点城市 */
  mealLocationCity?: string

  /* 签到人工号 */
  checkinPersonCode?: string

  /* 签到人名称 */
  checkinPersonName?: string

  /* 签到时间 */
  checkinTime?: Record<string, unknown>

  /* 签到地址 */
  checkinLocation?: string

  /* 支付人工号 */
  payerCode?: string

  /* 支付人名称 */
  payerPersonName?: string

  /* 支付时间 */
  payTime?: Record<string, unknown>

  /* 支付开始时间 */
  payStartTime?: Record<string, unknown>

  /* 支付结束时间 */
  payEndTime?: Record<string, unknown>

  /* 实际支付金额 */
  actualPaymentAmount?: number

  /* 水票信息 */
  waterTicketInformation?: string

  /* 异常处理附件证明 */
  exceptionAttachment?: string

  /* 异常处理结果 */
  exceptionResult?: string

  /* 核对备注 */
  checkRemark?: string

  /* 是否发生退款1是2否 */
  refundFlag?: number

  /* 企业支付金额，单位元，精确到小数点后两位（不包含服务费） */
  entPayAmount?: number

  /* 个人支付金额，单位元，精确到小数点后两位（不包含服务费） */
  staffPayAmount?: number

  /* 申请时间0开始1结束 */
  applicationTimes?: Record<string, unknown>[]

  /* 对账时间0开始1结束 */
  settleTimes?: Record<string, unknown>[]

  /* */
  needPage?: boolean
}

export interface IReservationRes {
  /* 主键ID */
  id: number

  /* 预订单号 */
  orderBookingCode: string

  /* 美团预订单号(订单ID) */
  mtBookingCode: string

  /* 关联申请单号 */
  orderCode: string

  /* 对账单号 */
  settleCode: string

  /* 签字人工号 */
  signerCode: string

  /* 签字人名称 */
  signerName: string

  /* 就餐时间 */
  mealTime: Record<string, unknown>

  /* 场景类型1宴请2外卖 */
  sceneType: number

  /* 场景类型1宴请2外卖 */
  sceneTypeStr: string

  /* 用餐地点省份编码 */
  mealLocationProvinceCode: string

  /* 用餐地点省份 */
  mealLocationProvince: string

  /* 用餐地点城市编码 */
  mealLocationCityCode: string

  /* 用餐地点城市 */
  mealLocationCity: string

  /* 餐厅名字 */
  restaurantName: string

  /* 餐厅地址 */
  restaurantLocation: string

  /* 餐厅电话 */
  restaurantPhone: string

  /* 实际支付金额 */
  actualPaymentAmount: number

  /* 核对状态 0未核对1核对正常2核对异常 */
  checkStatusStr: string

  /* 核对状态 0未核对1核对正常2核对异常 */
  checkStatus: number

  /* 核对时间 */
  checkTime: Record<string, unknown>

  /* 核对人工号 */
  checkerCode: string

  /* 核对人名称 */
  checkerName: string

  /* 核对人信息 */
  checkerCodeInfo: string

  /* 核对备注 */
  checkRemark: string

  /* 关闭时间 */
  closeTime: Record<string, unknown>

  /* 关闭人工号 */
  closerCode: string

  /* 关闭人名称 */
  closerName: string

  /* 关闭人信息 */
  closerNameInfo: string

  /* 签到人工号 */
  checkinPersonCode: string

  /* 签到人名称 */
  checkinPersonName: string

  /* 签到时间 */
  checkinTime: Record<string, unknown>

  /* 签到地址 */
  checkinLocation: string

  /* 支付人工号 */
  payerCode: string

  /* 支付人名称 */
  payerPersonName: string

  /* 支付时间 */
  payTime: Record<string, unknown>

  /* 水票信息 */
  waterTicketInformation: string

  /* 异常处理附件证明 */
  exceptionAttachment: string

  /* 异常处理结果 */
  exceptionResult: string

  /* 是否发生退款1是2否 */
  refundFlag: number

  /* 企业支付金额，单位元，精确到小数点后两位（不包含服务费） */
  entPayAmount: number

  /* 个人支付金额，单位元，精确到小数点后两位（不包含服务费） */
  staffPayAmount: number
}
