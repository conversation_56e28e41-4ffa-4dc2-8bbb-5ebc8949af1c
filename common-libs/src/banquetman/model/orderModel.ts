// 餐厅订单列表
export interface BOrderListReq {
  /* */
  pageNum?: number

  /* */
  pageSize?: number

  /* 主键ID */
  id?: number

  /* 预订单号 */
  orderBookingCode?: string

  /* 美团预订单号(订单ID) */
  mtBookingCode?: string

  /* 关联申请单号 */
  orderCode?: string

  /* 对账单号 */
  settleCode?: string

  /* 签字人工号 */
  signerCode?: string

  /* 签字人名称 */
  signerName?: string

  /* 就餐时间 */
  mealTime?: Record<string, unknown>

  /* 场景类型1宴请2外卖 */
  sceneType?: number

  /* 餐厅名字 */
  restaurantName?: string

  /* 餐厅地址 */
  restaurantLocation?: string

  /* 餐厅电话 */
  restaurantPhone?: string

  /* 核对状态 0未核对1核对正常2核对异常 */
  checkStatus?: number

  /* 核对时间 */
  checkTime?: Record<string, unknown>

  /* 核对人工号 */
  checkerCode?: string

  /* 核对人名称 */
  checkerName?: string

  /* 用餐地点省份编码 */
  mealLocationProvinceCode?: string

  /* 用餐地点省份 */
  mealLocationProvince?: string

  /* 用餐地点城市编码 */
  mealLocationCityCode?: string

  /* 用餐地点城市 */
  mealLocationCity?: string

  /* 签到人工号 */
  checkinPersonCode?: string

  /* 签到人名称 */
  checkinPersonName?: string

  /* 签到时间 */
  checkinTime?: Record<string, unknown>

  /* 签到地址 */
  checkinLocation?: string

  /* 支付人工号 */
  payerCode?: string

  /* 支付人名称 */
  payerPersonName?: string

  /* 支付时间 */
  payTime?: Record<string, unknown>

  /* 实际支付金额 */
  actualPaymentAmount?: number

  /* 水票信息 */
  waterTicketInformation?: string

  /* 异常处理附件证明 */
  exceptionAttachment?: string

  /* 异常处理结果 */
  exceptionResult?: string

  /* 核对备注 */
  checkRemark?: string

  /* 是否发生退款1是2否 */
  refundFlag?: number

  /* 企业支付金额，单位元，精确到小数点后两位（不包含服务费） */
  entPayAmount?: number

  /* 个人支付金额，单位元，精确到小数点后两位（不包含服务费） */
  staffPayAmount?: number

  /* */
  needPage?: boolean
}

export interface BOrderListRes {
  /* 主键ID */
  id: number

  /* 预订单号 */
  orderBookingCode: string

  /* 美团预订单号(订单ID) */
  mtBookingCode: string

  /* 关联申请单号 */
  orderCode: string

  /* 对账单号 */
  settleCode: string

  /* 签字人工号 */
  signerCode: string

  /* 签字人名称 */
  signerName: string

  /* 就餐时间 */
  mealTime: Record<string, unknown>

  /* 场景类型1宴请2外卖 */
  sceneType: number

  /* 餐厅名字 */
  restaurantName: string

  /* 餐厅地址 */
  restaurantLocation: string

  /* 餐厅电话 */
  restaurantPhone: string

  /* 核对状态 0未核对1核对正常2核对异常 */
  checkStatus: number

  /* 核对时间 */
  checkTime: Record<string, unknown>

  /* 核对人工号 */
  checkerCode: string

  /* 核对人名称 */
  checkerName: string

  /* 用餐地点省份编码 */
  mealLocationProvinceCode: string

  /* 用餐地点省份 */
  mealLocationProvince: string

  /* 用餐地点城市编码 */
  mealLocationCityCode: string

  /* 用餐地点城市 */
  mealLocationCity: string

  /* 签到人工号 */
  checkinPersonCode: string

  /* 签到人名称 */
  checkinPersonName: string

  /* 签到时间 */
  checkinTime: Record<string, unknown>

  /* 签到地址 */
  checkinLocation: string

  /* 支付人工号 */
  payerCode: string

  /* 支付人名称 */
  payerPersonName: string

  /* 支付时间 */
  payTime: Record<string, unknown>

  /* 实际支付金额 */
  actualPaymentAmount: number

  /* 水票信息 */
  waterTicketInformation: string

  /* 异常处理附件证明 */
  exceptionAttachment: string

  /* 异常处理结果 */
  exceptionResult: string

  /* 核对备注 */
  checkRemark: string

  /* 是否发生退款1是2否 */
  refundFlag: number

  /* 企业支付金额，单位元，精确到小数点后两位（不包含服务费） */
  entPayAmount: number

  /* 个人支付金额，单位元，精确到小数点后两位（不包含服务费） */
  staffPayAmount: number

  /* 预订单数量 */
  totalBookingCount: number

  /* 已核对数量 */
  checkedCount: number

  /* 未核对数量 */
  uncheckedCount: number
}

;[]

// 订单列表

export interface BOrderPageReq {
  /* */
  pageNum?: number

  /* */
  pageSize?: number

  /* 主键ID */
  id?: number

  /* 预订单号 */
  orderBookingCode?: string

  /* 美团预订单号(订单ID) */
  mtBookingCode?: string

  /* 关联申请单号 */
  orderCode?: string

  /* 对账单号 */
  settleCode?: string

  /* 签字人工号 */
  signerCode?: string

  /* 签字人名称 */
  signerName?: string

  /* 就餐时间 */
  mealTime?: Record<string, unknown>

  /* 场景类型1宴请2外卖 */
  sceneType?: number

  /* 餐厅名字 */
  restaurantName?: string

  /* 餐厅地址 */
  restaurantLocation?: string

  /* 餐厅电话 */
  restaurantPhone?: string

  /* 核对状态 0未核对1核对正常2核对异常 */
  checkStatus?: number

  /* 核对时间 */
  checkTime?: Record<string, unknown>

  /* 核对人工号 */
  checkerCode?: string

  /* 核对人名称 */
  checkerName?: string

  /* 用餐地点省份编码 */
  mealLocationProvinceCode?: string

  /* 用餐地点省份 */
  mealLocationProvince?: string

  /* 用餐地点城市编码 */
  mealLocationCityCode?: string

  /* 用餐地点城市 */
  mealLocationCity?: string

  /* 签到人工号 */
  checkinPersonCode?: string

  /* 签到人名称 */
  checkinPersonName?: string

  /* 签到时间 */
  checkinTime?: Record<string, unknown>

  /* 签到地址 */
  checkinLocation?: string

  /* 支付人工号 */
  payerCode?: string

  /* 支付人名称 */
  payerPersonName?: string

  /* 支付时间 */
  payTime?: Record<string, unknown>

  /* 实际支付金额 */
  actualPaymentAmount?: number

  /* 水票信息 */
  waterTicketInformation?: string

  /* 异常处理附件证明 */
  exceptionAttachment?: string

  /* 异常处理结果 */
  exceptionResult?: string

  /* 核对备注 */
  checkRemark?: string

  /* 是否发生退款1是2否 */
  refundFlag?: number

  /* 企业支付金额，单位元，精确到小数点后两位（不包含服务费） */
  entPayAmount?: number

  /* 个人支付金额，单位元，精确到小数点后两位（不包含服务费） */
  staffPayAmount?: number

  /* */
  needPage?: boolean
}

export interface BOrderPageRes {
  /* 主键ID */
  id: number

  /* 预订单号 */
  orderBookingCode: string

  /* 美团预订单号(订单ID) */
  mtBookingCode: string

  /* 关联申请单号 */
  orderCode: string

  /* 对账单号 */
  settleCode: string

  /* 签字人工号 */
  signerCode: string

  /* 签字人名称 */
  signerName: string

  /* 就餐时间 */
  mealTime: Record<string, unknown>

  /* 场景类型1宴请2外卖 */
  sceneType: number

  /* 餐厅名字 */
  restaurantName: string

  /* 餐厅地址 */
  restaurantLocation: string

  /* 餐厅电话 */
  restaurantPhone: string

  /* 核对状态 0未核对1核对正常2核对异常 */
  checkStatus: number

  /* 核对时间 */
  checkTime: Record<string, unknown>

  /* 核对人工号 */
  checkerCode: string

  /* 核对人名称 */
  checkerName: string

  /* 用餐地点省份编码 */
  mealLocationProvinceCode: string

  /* 用餐地点省份 */
  mealLocationProvince: string

  /* 用餐地点城市编码 */
  mealLocationCityCode: string

  /* 用餐地点城市 */
  mealLocationCity: string

  /* 签到人工号 */
  checkinPersonCode: string

  /* 签到人名称 */
  checkinPersonName: string

  /* 签到时间 */
  checkinTime: Record<string, unknown>

  /* 签到地址 */
  checkinLocation: string

  /* 支付人工号 */
  payerCode: string

  /* 支付人名称 */
  payerPersonName: string

  /* 支付时间 */
  payTime: Record<string, unknown>

  /* 实际支付金额 */
  actualPaymentAmount: number

  /* 水票信息 */
  waterTicketInformation: string

  /* 异常处理附件证明 */
  exceptionAttachment: string

  /* 异常处理结果 */
  exceptionResult: string

  /* 核对备注 */
  checkRemark: string

  /* 是否发生退款1是2否 */
  refundFlag: number

  /* 企业支付金额，单位元，精确到小数点后两位（不包含服务费） */
  entPayAmount: number

  /* 个人支付金额，单位元，精确到小数点后两位（不包含服务费） */
  staffPayAmount: number

  /* 预订单数量 */
  totalBookingCount: number

  /* 已核对数量 */
  checkedCount: number

  /* 未核对数量 */
  uncheckedCount: number
}

;[]
