export interface BPolicyReq {
  /* 传入是编辑 */
  id?: number

  /* 就餐类型 */
  sceneType: number
  xzType: number

  /* 是否限制城市 0否 1是 */
  cityScope: boolean

  /* 提前选择餐厅（0:否, 1:是） */
  advanceChooseRestaurant: boolean
  restaurantScope: boolean
  // 选择完成的城市字符串
  jsonFormat?: string
  cityCodes?: any

  /* 政策管控对象 */
  persons?: {
    /* 受管控对象code */
    policyObjectCode?: string

    /* */
    policyObjectName?: string

    /* 受管控对象为城市时，此字段有值，城市所在省code */
    mealLocationProvinceCode?: string

    /* */
    mealLocationProvince?: string
  }[]

  /* 政策管控对象 */
  depts?: {
    /* 受管控对象code */
    policyObjectCode?: string

    /* */
    policyObjectName?: string

    /* 受管控对象为城市时，此字段有值，城市所在省code */
    mealLocationProvinceCode?: string

    /* */
    mealLocationProvince?: string
  }[]

  /* 政策管控对象 */
  citys?: {
    /* 受管控对象code */
    policyObjectCode?: string

    /* */
    policyObjectName?: string

    /* 受管控对象为城市时，此字段有值，城市所在省code */
    mealLocationProvinceCode?: string

    /* */
    mealLocationProvince?: string
  }[]
  restaurants?: {
    /* 受管控对象code */
    policyObjectCode?: string

    /* */
    policyObjectName?: string

    /* 受管控对象为城市时，此字段有值，城市所在省code */
    mealLocationProvinceCode?: string

    /* */
    mealLocationProvince?: string
  }[]
}

// 响应接口
export interface BPolicyRes {}

// 列表

export interface BPolicyListReq {
  /* */
  pageNum?: number

  /* */
  pageSize?: number

  /* 场景 */
  sceneType?: number

  /* 员工 */
  userCodeOrName?: string

  /* 部门 */
  dept?: string

  /* */
  needPage?: boolean
}

export interface BPolicyListRes {
  /* */
  id: number

  /* */
  creator: string

  /* */
  creatorName: string

  /* */
  updater: string

  /* */
  createTime: Record<string, unknown>

  /* */
  updateTime: Record<string, unknown>

  /* */
  sceneType: number

  /* */
  cityScope: boolean

  /* */
  advanceChooseRestaurant: boolean

  /* */
  restaurantScope: boolean

  /* 城市 */
  citys: string

  /* 部门 */
  depts: string

  /* 人员 */
  persons: string

  /* 餐厅 */
  restaurants: string
}

;[]
