/// <reference types="vite/client" />

interface ImportMetaEnv {
  /**
   * 应用运行模式 (development/production)
   * @example 'development'
   */
  MODE: 'development' | 'production' | 'test'

  /**
   * 项目基础 API 地址
   * @example 'https://api.example.com'
   */
  readonly VITE_BASE_URL: string

  // 其他自定义环境变量...
  // readonly VITE_API_KEY: string;
}

interface ImportMetaGlob<T = unknown> {
  /**
   * Vite 的 glob 导入方法
   * @param pattern - 匹配模式 (如 './modules/*.ts')
   * @param options - 配置选项
   */
  (
    pattern: string | string[],
    options?: {
      eager?: false
      import?: string
      exclude?: string | string[]
      query?: string | Record<string, string | number | boolean>
    },
  ): Record<string, () => Promise<T>>

  /**
   * 启用 eager 模式返回同步模块
   */
  (
    pattern: string | string[],
    options: {
      eager: true
      import?: string
      exclude?: string | string[]
      query?: string | Record<string, string | number | boolean>
    },
  ): Record<string, T>
}

interface ImportMeta {
  /** 环境变量 */
  readonly env: ImportMetaEnv

  /** Vite 特有的 glob 导入方法 */
  readonly glob: ImportMetaGlob
}
