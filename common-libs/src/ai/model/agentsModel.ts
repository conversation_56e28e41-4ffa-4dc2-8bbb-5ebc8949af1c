// AI助手请求参数接口
export interface IAgentExecuteRequest {
  message: string;
  chatId: string;
  serviceName: string;
  enableMessageCheck?: boolean;
}

export interface IAgentDTO {
  errorContent?: string | null;
  thinkingLoading?: boolean | null;
  message?: string | null;
  thinkingContent?: string | null;
  executionDuration?: bigint | null;
  relatedOpinions?: any;
  dialogueContent?: string | null;
  echartsOption?: string | null;
  sql?: string | null;
  sqlContentId?: bigint | null;
  chartData?: Array<Record<string, any>> | null;
  tableData?: any | null;
  miceRequest?: any;
  userMessageTemplateResult?: IUserMessageTemplateResult;
}

export interface IUserMessageTemplateResult {
  userMessage?: string | null;
  isSatisfy?: boolean | null;
  messageTemplate?: string | null;
  params?: Array<IUserMessageTemplateParamsResult> | null;
}

export interface IUserMessageTemplateParamsResult {
  key?: string | null;
  name?: string | null;
  values?: Array<IUserMessageTemplateParamsValuesResult> | null;
}

export interface IUserMessageTemplateParamsValuesResult {
  value?: string | null;
  valueDesc?: string | null;
}

export interface IAiChartContextDO {
  id: bigint;
  chatId?: string | null;
  subject?: string | null;
  message?: string | null;
  executionDuration?: bigint | null;
  username?: string | null;
  context?: string | null;
}

export interface IAiPromptListRequest {
  id?: number;
  aiIntroduceId?: number;
  content?: string;
  serviceName?: string;
}

export interface IAiIntroduceListRequest {
  /* */
  id?: number;

  /* */
  subject?: string;

  /* */
  description?: string;

  /* */
  isFrontShow?: boolean;
}

export interface AiIntroduceDO {
  /* */
  id: number;

  /* */
  subject: string;

  /* */
  description: string;

  /* */
  isFrontShow: boolean;
}

export interface AiIntroduceResult {
  /* */
  id: number;

  /* */
  subject: string;

  /* */
  description: string;

  /* */
  isFrontShow: boolean;

  prompts: AiPromptDO[];
}

export interface AiPromptDO {
  /* */
  id: number;

  /* */
  aiIntroduceId: number;

  /* */
  content: string;

  /* */
  serviceName: string;
}
