export interface RRechargeParmas {
  fileList: Array<file>
}

export interface file {
  filePath: string
  fileName: string
  name: string
}

// 预算充值-- 添加
export interface RRechargeSaveParams {
  id?: string
  fileList?: Array<file>
  fileName: string
  fileUrl: string
  budgetAmount: string
  reason: string
  businessFlag?: number | string
  riskName?: string
  checked: boolean
  paymentAccount?: string
  paymentAccountName?: string
}

// 预算充值-- 流水记录
export interface RRechargeAccountRecordParams {
  pageNo: number
  pageSize: number
  changeType?: number
  operBeginDate: string // 操作开始日期
  operEndDate: string // 操作结束日期
}

// 预算充值-- 交接
export interface RRechargeHandoverParams {
  handoverCode?: string
  handoverName?: string
  handoverDeptCode?: string
  handoverDeptName?: string
  handoverTel?: string
  handoverEmail?: string
  handoverType?: number // 交接类型1主动2强制
  undertakeCode?: string
  undertakeName?: string
  undertakeDeptCode?: string
  undertakeDeptName?: string
  undertakeTel?: string
  undertakeEmail?: string
  handoverReason?: string
  undertakeSuperiorCode?: string
  undertakeSuperiorName?: string
}

// 充值下发 -- 新增
export interface RRechargeAccountAddParams {
  amountSum: number
  fileName?: string
  fileUrl?: string
  applyCode: string
  applyName: string
  applyTime: string
  confirmFlag: number // 是否确认0未确认1已确认
  notifyFlag: number // 是否通知0否1是
  incentiveMessage?: string
  rechargeIssueDetailList: Array<RRechargeIssueDetail>
}

export interface RRechargeIssueDetail {
  ownerCode: string
  ownerName: string
  amount: number
  type: string // 充值状态
}

// 预算充值—列表查询
export interface RRechargeQueryParams {
  pageNo: number
  pageSize: number
  orderCode?: string
  serialCode?: string
  statusList?: number
  applyBeginDate?: string
  applyEndDate?: string
  applyName?: string
  payCode?: string
  timeRange?: Array<string>
}
