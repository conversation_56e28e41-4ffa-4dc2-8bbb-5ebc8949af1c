import { IPageRequest } from '../../basic'

export class IAnnualMeetingBudgetFilter extends IPageRequest {
  begin?: string
  end?: string
  year?: string
}

export class IAnnualMeetingBudget {
  id?: number | null
  creator?: string
  createTime?: string
  updater?: string
  updateTime?: string

  // 新增字段
  name?: string
  miceTime?: string
  day?: number
  budget?: number
  place?: string
  hotelLevel?: number
  isCloth?: boolean
  item?: number[]
  personTotal?: number
  description?: string
}

// 月份数据接口
export interface MonthData {
  month: string
  meetings: IAnnualMeetingBudget[]
}
