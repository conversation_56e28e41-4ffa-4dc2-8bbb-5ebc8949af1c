export interface MerchantBusiness {
  /* 主键 */
  id?: string

  /* 供应商编码，自维护或来源其它系统 */
  code?: string

  /* 供应商名称，自维护或来源其它系统 */
  name?: string

  /**/

  /* 企业id */
  enterpriseId?: number

  /* 企业code */
  enterpriseCode?: string

  /* 企业name */
  enterpriseName?: string

  /* 统一信用代码号 */
  unifiedSocialCreditCode?: string
}

export interface MerchantBank {
  /* 主键 */
  id?: number

  /* 供应商id */
  merchantId?: number

  /* 银行账号 */
  accountNumber?: string

  /* 开户行编码 */
  bankBranchCode?: string

  /* 开户行地址 */
  bankBranchAddress?: string

  /* 银行户主 */
  accountHolderName?: string

  /* 银行所属国家 */
  bankCountry?: string

  /* 来源 本地创建/haiermdm同步 */
  resource?: string

  /* 创建人工号 */
  createBy?: string

  /* 创建人姓名 */
  createName?: string

  /* 创建时间 */
  gmtCreate?: string

  /* 最后修改人工号 */
  lastModifiedBy?: string

  /* 最后修改人姓名 */
  lastModifiedName?: string

  /* 最后修改时间 */
  gmtModified?: string

  /* 是否删除 0否 1是 */
  deleted?: number
}
