// BillFileTypeConstant

type keys = 'CONNECTION' | 'SIGNING' | 'RESTAURANT' | 'MICE' | 'FLIGHT' | 'TRAIN'

export const MerchantTypeConstant = {
  CONNECTION: { code: 1, desc: '直连酒店' },
  SIGNING: { code: 2, desc: '直签酒店' },
  RESTAURANT: { code: 3, desc: '订餐' },
  MICE: { code: 4, desc: '会务' },
  FLIGHT: { code: 5, desc: '机票' },
  TRAIN: { code: 6, desc: '火车票' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in MerchantTypeConstant) {
      const item = MerchantTypeConstant[key as keys]
      if (type === item.code) {
        return item
      }
    }
    return null
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(MerchantTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MerchantTypeConstant[i as keys]
      }
      return undefined
    })
    const newTypes = types.filter(function (s) {
      return s && s
    })
    return newTypes
  },
}
