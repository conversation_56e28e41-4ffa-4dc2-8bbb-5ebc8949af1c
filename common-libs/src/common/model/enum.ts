/**
 * 通用枚举定义
 */

/**
 * 是否枚举 - 通用的布尔值数字表示
 * 0: 否
 * 1: 是
 */
export enum YesNoEnum {
  /** 否 */
  NO = 0,
  /** 是 */
  YES = 1,
}

/**
 * 是否枚举的标签映射
 */
export const YesNoLabels = {
  [YesNoEnum.NO]: '否',
  [YesNoEnum.YES]: '是',
} as const

/**
 * 根据枚举值获取对应的标签
 * @param value 枚举值
 * @returns 对应的标签文本
 */
export function getYesNoLabel(value: YesNoEnum): string {
  return YesNoLabels[value] || '未知'
}

/**
 * 是否枚举选项列表，用于下拉框等组件
 */
export const YesNoOptions = [
  { label: '否', value: YesNoEnum.NO },
  { label: '是', value: YesNoEnum.YES },
] as const
