import vueParser from 'vue-eslint-parser'
import tsParser from '@typescript-eslint/parser'
import vuePlugin from 'eslint-plugin-vue'
import importPlugin from 'eslint-plugin-import'
import tsPlugin from '@typescript-eslint/eslint-plugin'
import globals from 'globals'
import eslint from '@eslint/js'
import prettier from 'eslint-config-prettier'

// ！！！默认验证规则，规则严禁私自更改， 如需修改请报备
export default [
  {
    ignores: [
      // 忽略 node_modules 目录
      '**/node_modules/',
      // 忽略构建输出目录
      '**/dist/',
      'projects/**/src/utils/rem.js',
      'projects/haierbusiness-mice-bid-front/src/assets/js/svgLoader.js',
      'projects/haierbusiness-mice-bid-front/src/assets/js/classie.js',
    ],
  },
  eslint.configs.recommended,
  /** vue推荐配置 */
  ...vuePlugin.configs['flat/recommended'],
  /** prettier 配置 */
  prettier,
  {
    //包含的文件类型
    files: ['**/*.vue', '**/*.ts', '**/*.js'],

    /* 针对语言进行配置 */
    languageOptions: {
      globals: {
        //node环境运行
        ...globals.node,
        //浏览器环境运行
        ...globals.browser,
      },
      //顶层vue解析器
      parser: vueParser,
      parserOptions: {
        // 嵌套typescript解析器
        parser: tsParser,
        // 指定es版本
        ecmaVersion: 'latest',
        // 指定源码类型,模块化
        sourceType: 'module',
      },
    },
    plugins: {
      // 指定vue插件，rules中使用
      vue: vuePlugin,
      // 指定typescript插件，rules中使用
      '@typescript-eslint': tsPlugin,
      // 指定import插件，rules中使用
      import: importPlugin,
    },
    rules: {
      // ==================== TypeScript 相关规则 ====================
      // 禁用 @ts-ignore（默认值：2）
      '@typescript-eslint/ban-ts-ignore': 0,
      // 允许使用 any 类型（默认值：1）
      '@typescript-eslint/no-explicit-any': 0,
      // 允许 require() 导入（默认值：2）
      '@typescript-eslint/no-require-imports': 0,
      // 允许 require() 语句（默认值：2）
      '@typescript-eslint/no-var-requires': 0,
      // 关闭 for-of 循环强制使用（默认值：2）
      '@typescript-eslint/prefer-for-of': 0,
      // 关闭函数返回类型注解,使用自动推断（默认值：1）
      '@typescript-eslint/explicit-function-return-type': 0,
      // 添加这行来正确处理 TypeScript 变量未使用检查
      '@typescript-eslint/no-unused-vars': 2,

      // ==================== 模块相关规则 ====================
      // 允许未声明依赖（默认值：2）
      'import/no-extraneous-dependencies': 0,
      // 允许无法解析的导入（默认值：2）
      'import/no-unresolved': 0,
      // 推荐默认导出（默认值：2）
      'import/prefer-default-export': 0,
      // 导入后空行（默认值：0）
      'import/newline-after-import': 2,
      // ==================== 代码风格规则 ====================
      // 关闭缩进检查（由 Prettier 处理）
      // 'indent': [2, 2, {"SwitchCase": 1}],
      'indent': [2, 2, { 'SwitchCase': 1 }],
      // 强制 if、else if、else、for、while、do...while 语句使用大括号
      'curly': [2, 'all'],
      // 关闭驼峰命名强制（默认值：2）
      'camelcase': 0,
      // 允许未使用 this 的类方法（默认值：2）
      'class-methods-use-this': 0,
      // 关闭构造函数大写检查（默认值：2）
      'new-cap': 0,
      // 允许变量遮蔽（默认值：2）
      'no-shadow': 0,
      // 允许下划线开头变量（默认值：2）
      'no-underscore-dangle': 0,
      // 允许箭头函数与比较混淆（默认值：2）
      'no-confusing-arrow': 0,
      // 关闭对象简写强制（默认值：2）
      'object-shorthand': 0,
      // 注释空格要求（默认值：0）
      'spaced-comment': 2,
      // ==================== 逻辑控制规则 ====================
      // 不允许不一致的返回值
      'consistent-return': 2,
      // 允许 else 中 return（默认值：2）
      'no-else-return': 0,
      // 关闭操作符简写强制（默认值：2）
      'operator-assignment': 0,
      // 禁用基础的 no-unused-vars 规则，避免与'@typescript-eslint/no-unused-vars'冲突
      'no-unused-vars': 0,
      // ==================== Vue 相关规则 ====================
      // 允许defineProps 的返回值进行解构（默认值：2）
      'vue/no-setup-props-destructure': 0,
      // 允许单个单词命名vue文件（默认值：2）
      'vue/multi-word-component-names': 0,
      // 项目vue文件组件已经采用camelCase命名（但此项配置为PascalCase并且不支持camelCase，VUE建议PascalCase）（默认值：1）
      'vue/component-definition-name-casing': 0,
      // prop有值的情况下不建议设置必填（默认值：1）
      'vue/no-required-prop-with-default': 0,
      // 属性顺序（默认值：1）
      'vue/attributes-order': 0,
      // 要求当一个元素有多个属性时，第一个属性应该放在新行上（默认值：1）
      'vue/first-attribute-linebreak': 0,
      // 要求所有非原生HTML属性应该使用连字符（kebab-case）格式（默认值：1）
      'vue/attribute-hyphenation': 2,
      // 要求所有非原生HTML事件应该使用连字符（kebab-case）格式（默认值：1）
      'vue/v-on-event-hyphenation': 2,
      // v-html可能引起xss攻击（默认值：1）
      'vue/no-v-html': 0,
      // 模板中变量和全局的存在冲突，但vue可正常识别（默认值：1）
      'vue/no-template-shadow': 0,
      // Vue 要求所有 prop 都应该定义其类型（默认值：1）
      'vue/require-prop-types': 2,
      // Vue 要求所有 prop 添加默认值（默认值：1）
      'vue/require-default-prop': 2,
      // 不允许只出现没有任何属性参数的template（默认值：1）
      'vue/no-lone-template': 2,
      // 不允许使用 v-slot（默认值：1）
      'vue/v-slot-style': 2,
      // emits必须显示声明（默认值：1）
      'vue/require-explicit-emits': 2,
      // prop命名规则（默认值：1）
      'vue/prop-name-casing': 1,
      // ==================== 其他通用规则 ====================
      // 允许 console（默认值：1）
      'no-console': 0,
      // 允许 debugger，但警告（默认值：2）
      'no-debugger': 1,
      // 允许 ++/-- 操作符（默认值：2）
      'no-plusplus': 0,
      // 允许参数重赋值（默认值：2）
      'no-param-reassign': 0,
      // 关闭行长度限制（由 Prettier 处理）
      'max-len': 0,
      // 导入顺序检查（默认值：2）
      'import/order': 0,
      // todo提醒（默认值：2）
      'no-warning-comments': [1, { terms: ['todo', 'fixme'], location: 'anywhere' }]
    },
  },

  // 添加针对 vite.config.ts 文件的特殊配置
  {
    files: ['**/vite-env.d.ts'],
    rules: {
      // 适配三斜杠不报错
      'spaced-comment': 0,
      'no-unused-vars': 0,
      '@typescript-eslint/no-unused-vars': 0,
    },
  },
]
