import { message, Modal } from 'ant-design-vue'
import { Deleteable } from '@haierbusiness-front/common-libs'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import { createVNode } from 'vue'

export const useDelete = (api: Deleteable, fetchData: () => void) => {
  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: '确认要删除吗？',
      icon: createVNode(ExclamationCircleFilled),
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        api.remove(id, 2).then(() => {
          message.success('删除成功！')
          fetchData()
        })
      },
      onCancel: async () => {},
    })
  }

  return {
    handleDelete,
  }
}
