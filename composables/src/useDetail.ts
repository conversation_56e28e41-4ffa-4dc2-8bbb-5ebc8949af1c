export interface Detailable {
  detail(id: number): Promise<any>
}

import type { Ref } from 'vue'
import { ref } from 'vue'
import { isFinite } from 'lodash-es'
import { message } from 'ant-design-vue'

export const useDetail = <T>(api: Detailable, id: number, modelLabel = '') => {
  const currentDetail = <Ref<null | T>>ref(null)

  const fetchDetail = (id: number) => {
    if (isFinite(id)) {
      api.detail(id).then((res) => {
        currentDetail.value = res
      })
    } else {
      message.error(`未找到${modelLabel}信息！`)
    }
  }

  // onMounted(() => fetchDetail(id))

  return {
    currentDetail,
    fetchDetail,
  }
}
