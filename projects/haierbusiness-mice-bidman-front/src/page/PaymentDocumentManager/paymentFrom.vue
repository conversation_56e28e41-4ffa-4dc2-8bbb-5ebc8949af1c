<!-- 付款单 -->
<script lang="ts" setup>
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  message,
  Modal,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Table as ATable,
  Tag as hTag,
  Tooltip,
} from 'ant-design-vue'
import { ColumnType } from 'ant-design-vue/lib/table/interface'
import { Key } from 'ant-design-vue/lib/vc-table/interface'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { paymentFromApi } from '@haierbusiness-front/apis'
import {
  InvoiceStatusEnum,
  InvoiceStatusMap,
  InvoiceStatusTagColorMap,
  IPaymentFromFilter,
} from '@haierbusiness-front/common-libs'
import dayjs, { Dayjs } from 'dayjs'
import { computed, onMounted, ref, watch } from 'vue'
import { DataType, usePagination } from 'vue-request'
import router from '../../router'
import Actions from '@haierbusiness-front/components/actions/Actions.vue'
import type { MenuInfo, MenuItemType } from 'ant-design-vue/lib/menu/src/interface'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton'
import { routerParam } from '@haierbusiness-front/utils'
// const router = useRouter()

const currentRouter = ref()
const store = applicationStore()

// 权限判断：检查是否为会务顾问（会务顾问不能上传付款凭证）
const isConsultant = computed(() => {
  if (!store.loginUser?.authorities) return false

  return store.loginUser.authorities.some((item) => item.authority === '211')
})

onMounted(async () => {
  currentRouter.value = await router
  // 页面初始化时调用列表接口
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  })
})

const columns: ColumnType[] = [
  {
    title: '付款单号',
    dataIndex: 'paymentCode',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}元` : ''),
  },
  {
    title: '付款比例',
    dataIndex: 'settlementRate',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}元` : ''),
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '100px',
    fixed: 'right',
    align: 'center',
  },
]
const searchParam = ref<IPaymentFromFilter>({})
const { data, run: listApiRun, loading, current, pageSize } = usePagination(paymentFromApi.getPage)

const reset = () => {
  searchParam.value = {}
  beginAndEnd.value = undefined
}

const dataSource = computed(() => data.value?.records || [])

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}))

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  })
}

// 删除操作已移除

// 查看详情 - 跳转到详情页面
const handleView = (record: any) => {
  // 跳转到详情页面，传递记录信息和查看模式
  currentRouter.value.push({
    path: '/bidman/PaymentDocumentManager/billUploadPaymentProof',
    query: {
      record: routerParam(record),
      mode: 'view', // 查看模式
    },
  })
}

const beginAndEnd = ref<[Dayjs, Dayjs]>()
// 生成付款单弹窗的独立时间范围变量
const paymentOrderBeginAndEnd = ref<[Dayjs, Dayjs]>()

watch(
  () => beginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
    } else {
      searchParam.value.startTime = undefined
      searchParam.value.endTime = undefined
    }
  },
)

// 跳转到上传付款凭证页面
const openUploadPage = (record: any) => {
  // 跳转到上传付款凭证页面，传递记录信息
  currentRouter.value.push({
    path: '/bidman/PaymentDocumentManager/billUploadPaymentProof',
    query: {
      record: routerParam(record),
    },
  })
}

// 关闭生成付款单弹窗
const closePaymentOrderModal = () => {
  PaymentOrderVisible.value = false
  settlementList.value = undefined
  selectedRowKeys.value = [] // 重置选中状态
  // 重置弹窗中的查询条件
  paymentOrderBeginAndEnd.value = undefined
}

//上传付款单
const PaymentOrderVisible = ref(false)
//选择的结算单
const settlementList = ref()
// 选中的行键
const selectedRowKeys = ref<Key[]>([])

const {
  data: PaymentOrderData,
  run: PaymentOrderlist,
  loading: paymentOrderLoading,
  current: paymentOrderCurrent,
  pageSize: paymentOrderPageSize,
} = usePagination(paymentFromApi.getBillList)

const handlePaymentOrder = () => {
  // 使用弹窗独立的时间范围变量
  const startTime = paymentOrderBeginAndEnd.value
    ? dayjs(paymentOrderBeginAndEnd.value[0]).format('YYYY-MM-DD 00:00:00')
    : undefined
  const endTime = paymentOrderBeginAndEnd.value
    ? dayjs(paymentOrderBeginAndEnd.value[1]).format('YYYY-MM-DD 23:59:59')
    : undefined

  PaymentOrderlist({
    startTime,
    endTime,
    pageNum: 1,
    pageSize: 10,
  })
}

// PaymentOrder表格分页
const paymentOrderPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: PaymentOrderData.value?.total || 0,
  current: PaymentOrderData.value?.pageNum || 1,
  pageSize: PaymentOrderData.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}))

// 处理PaymentOrder表格分页变化
const handlePaymentOrderTableChange = (pag: any, filters?: any, sorter?: any) => {
  // 使用弹窗独立的时间范围变量
  const startTime = paymentOrderBeginAndEnd.value
    ? dayjs(paymentOrderBeginAndEnd.value[0]).format('YYYY-MM-DD 00:00:00')
    : undefined
  const endTime = paymentOrderBeginAndEnd.value
    ? dayjs(paymentOrderBeginAndEnd.value[1]).format('YYYY-MM-DD 23:59:59')
    : undefined

  PaymentOrderlist({
    startTime,
    endTime,
    pageNum: pag.current || 1,
    pageSize: pag.pageSize || 10,
  })
}
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (selectedKeys: Key[], selectedRows: DataType[]) => {
    selectedRowKeys.value = selectedKeys
    settlementList.value = selectedRows
  },
  getCheckboxProps: () => ({}),
}))
//订单
const PaymentOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '会议时间',
    dataIndex: 'meetingTime',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`
      }
      return ''
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '结算金额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}元` : ''),
  },
  {
    title: '付款比例',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'receiveAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}元` : ''),
  },
]

// 提交生成付款单
const submitPaymentOrder = () => {
  if (!settlementList.value || settlementList.value.length === 0) {
    message.error('请选择会议')
    return
  }

  // 提取选中会议的结算单ID列表
  const balanceIds = settlementList.value.map((item: any) => item.id).filter(Boolean)

  if (balanceIds.length === 0) {
    message.error('选中的会议中没有有效的结算单ID')
    return
  }

  // 提取merchantCode并检查是否为同一服务商
  const merchantCodes = settlementList.value.map((item: any) => item.merchantCode).filter(Boolean)
  const uniqueMerchantCodes = [...new Set(merchantCodes)]

  if (uniqueMerchantCodes.length === 0) {
    message.error('选中的会议中没有有效的服务商信息')
    return
  }

  if (uniqueMerchantCodes.length > 1) {
    message.error('只能选择同一个服务商的会议进行生成付款单')
    return
  }

  // 调用生成付款单接口
  paymentFromApi
    .create({
      balanceIds: balanceIds,
    })
    .then(() => {
      message.success('付款单生成成功')
      closePaymentOrderModal()
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      })
    })
    .catch((error) => {
      console.error('生成付款单失败:', error)
    })
}

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string
  switch (key) {
    case 'view':
      // 查看详情
      handleView(record)
      break
    case 'upload':
      openUploadPage(record)
      break
    default:
      break
  }
}

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = []

  // 查看按钮始终显示
  options.push({
    key: 'view',
    label: '查看',
  })

  // 权限判断：211-会务顾问只能查看，213-会务负责人可以上传凭证
  const userAuthorities = store.loginUser?.authorities || []
  const isConsultantRole = userAuthorities.some((item) => item.authority === '211') // 会务顾问
  const isManagerRole = userAuthorities.some((item) => item.authority === '213') // 会务负责人

  // 根据状态和权限添加不同的操作选项
  if (record.status === InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD) {
    // 待服务商上传发票：无操作
  } else if (record.status == InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM) {
    // 待财务确认收款：只有会务负责人(213)可以上传付款凭证
    if (isManagerRole && !isConsultantRole) {
      options.push({
        key: 'upload',
        label: '上传付款凭证',
      })
    }
  }
  // 已完成状态：所有角色都只显示查看，不需要额外的菜单选项

  return options
}

// 生成付款单
const generatePaymentOrder = () => {
  PaymentOrderVisible.value = true
  // 打开弹窗时就调用接口获取数据，使用弹窗独立的时间范围变量
  const startTime = paymentOrderBeginAndEnd.value
    ? dayjs(paymentOrderBeginAndEnd.value[0]).format('YYYY-MM-DD 00:00:00')
    : undefined
  const endTime = paymentOrderBeginAndEnd.value
    ? dayjs(paymentOrderBeginAndEnd.value[1]).format('YYYY-MM-DD 23:59:59')
    : undefined

  PaymentOrderlist({
    startTime,
    endTime,
    pageNum: 1,
    pageSize: 10,
  })
}
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 0px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="serviceProvider">服务商：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.merchantCode" allow-clear placeholder="请输入服务商" />
          </h-col>

          <h-col :span="3" style="text-align: right; padding-right: 10px">
            <label for="createTime">付款单创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" allow-clear style="width: 100%" value-format="YYYY-MM-DD" />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.status" allow-clear placeholder="请选择状态" style="width: 100%">
              <h-select-option :value="InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD">
                {{ InvoiceStatusMap[InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM">
                {{ InvoiceStatusMap[InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.FINANCIAL_REJECTED">
                {{ InvoiceStatusMap[InvoiceStatusEnum.FINANCIAL_REJECTED] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.COMPLETED">
                {{ InvoiceStatusMap[InvoiceStatusEnum.COMPLETED] }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 0px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 0px">
          <h-col :span="12" style="text-align: left">
            <h-button type="primary" @click="generatePaymentOrder">
              <PlusOutlined />
              生成付款单
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          :row-key="(record) => record.id"
          :scroll="{ x: 1400 }"
          :size="'small'"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'paymentCode'">
              <Tooltip :title="record.paymentCode">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.paymentCode }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'merchantName'">
              <Tooltip :title="record.merchantName">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.merchantName }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <h-tag
                :color="InvoiceStatusTagColorMap[record.status as keyof typeof InvoiceStatusTagColorMap] || 'default'"
              >
                {{ InvoiceStatusMap[record.status as keyof typeof InvoiceStatusMap] || '未知状态' }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <Actions
                :menu-options="getMenuOptions(record)"
                :on-menu-click="(e: MenuInfo) => handleMenuClick(record, e)"
              />
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 生成付款单弹窗 -->
    <Modal
      v-model:open="PaymentOrderVisible"
      :footer="null"
      title="生成付款单"
      width="60%"
      @cancel="closePaymentOrderModal"
    >
      <div>
        <h-row :align="'middle'" class="modal-search-row">
          <h-col :span="3" class="search-label">
            <label for="createTime">会议时间：</label>
          </h-col>
          <h-col :span="7">
            <h-range-picker
              v-model:value="paymentOrderBeginAndEnd"
              allow-clear
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </h-col>
          <h-col :span="6" class="modal-search-button">
            <h-button type="primary" @click="handlePaymentOrder()">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <div class="modal-table-container">
          <a-table
            :columns="PaymentOrderColumns"
            :data-source="PaymentOrderData?.records || []"
            :loading="paymentOrderLoading"
            :pagination="paymentOrderPagination"
            :row-key="(record) => record.id"
            :row-selection="rowSelection"
            :scroll="{ x: 'max-content' }"
            class="modal-table"
            @change="handlePaymentOrderTableChange"
          >
            <template #bodyCell="{ column, text }"></template>
          </a-table>
        </div>
        <div class="modal-footer">
          <h-button class="button-margin" @click="closePaymentOrderModal">取消</h-button>
          <h-button :loading="loading" type="primary" @click="submitPaymentOrder">生成付款单</h-button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.main-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.padding-standard {
  padding: 10px 10px 0px 10px;
}

.text-right-padding {
  text-align: right;
  padding-right: 10px;
}

.width-full {
  width: 100%;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.margin-right-10 {
  margin-right: 10px;
}

.modal-padding {
  padding: 20px 0;
}

.info-item {
  margin-bottom: 16px;
}

.info-item-12 {
  margin-bottom: 12px;
}

.upload-section {
  margin: 16px 0;
  display: flex;
}

.textarea-section {
  margin-bottom: 16px;
  display: flex;
}

.footer-buttons {
  text-align: right;
  margin-top: 20px;
}

.table-margin {
  margin-top: 15px;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}



:deep(table) {
  table-layout: auto !important; /* 恢复默认自适应布局 */
}

/* 对齐 PaymentBill.vue 的弹框样式 */
.modal-search-row {
  padding: 10px 10px 0px 10px;
}

.modal-search-button {
  text-align: left;
  padding-left: 10px;
}

.modal-table {
  margin-top: 15px;
}

.modal-footer {
  text-align: right;
  margin-top: 20px;
}

.button-margin {
  margin-right: 10px;
}

/* 固定高度的tabs */
.fixed-height-tabs {
  .tab-content-wrapper {
    height: 170px; /* 调整为大约显示3条记录的高度 */
    overflow-y: auto; /* 超出3条记录时向下滚动 */
    padding: 10px 0;
  }
}

/* 固定的上传区域 */
.upload-section-fixed {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  background-color: #fafafa;
  margin: 20px -24px -20px -24px;
  padding-left: 24px;
  padding-right: 24px;
  padding-bottom: 16px;
}
</style>
