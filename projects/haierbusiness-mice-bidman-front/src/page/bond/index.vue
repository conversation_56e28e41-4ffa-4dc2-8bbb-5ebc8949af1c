<!-- 保证金管理端 -->

<script lang="ts" setup>
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  message,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  Tooltip as hTooltip,
  Upload as hUpload,
} from 'ant-design-vue'
import { SearchOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { ColumnType } from 'ant-design-vue/lib/table/interface'
import { bondApi, fileApi } from '@haierbusiness-front/apis'
import {
  Bond,
  BondFilter,
  DepositForm,
  ExtendedBond,
  FileTypeConstant,
  MarginStatusEnum,
  MarginStatusMap,
  ReceiptRecord,
  UploadFiles,
} from '@haierbusiness-front/common-libs'
import { formatNumberThousands } from '@haierbusiness-front/utils'
import dayjs, { Dayjs } from 'dayjs'
import { computed, h, nextTick, onMounted, ref, watch } from 'vue'
import { usePagination } from 'vue-request'
import router from '../../router'

import DetailDialog from './detail-dialog.vue'
import ReceiptSelect from '@haierbusiness-front/components/mice/receipt/ReceiptSelect.vue'
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  })
})

const columns: ColumnType<ExtendedBond>[] = [
  {
    title: '单号',
    dataIndex: 'recordNo',
    width: '200px',
    align: 'center',
  },
  {
    title: '操作类别',
    dataIndex: 'type',
    width: '100px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      const type = text === 1 ? '缴纳' : text === 2 ? '退款' : '-'
      const color = text === 1 ? 'green' : text === 2 ? 'blue' : 'default'
      return h(hTag, { color }, () => `保证金${type}`)
    },
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => formatNumberThousands(text) + ' 元', // 使用千分位格式化
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '160px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '核对人',
    dataIndex: 'checkName',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text || '-',
  },
  {
    title: '核对时间',
    dataIndex: 'checkTime',
    width: '160px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text || '-',
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      const statusText = MarginStatusMap[text as MarginStatusEnum] || '核对中'
      const color = text === MarginStatusEnum.COMPLETED ? 'green' : text === MarginStatusEnum.REJECTED ? 'red' : 'blue'
      return h(hTag, { color }, () => statusText)
    },
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '100px',
    fixed: 'right',
    align: 'center',
  },
]
const searchParam = ref<BondFilter>({
  state: undefined, //state
  recordNo: '', //单号
  type: undefined, //操作类别
  amount: undefined, //金额
  merchantName: '', //服务商名称
  receiveName: '', //核对人
  receiveTime: ['', ''], //核对时间
  createStart: undefined,
  createEnd: undefined,
})
const { data, run: listApiRun, loading, current, pageSize } = usePagination(bondApi.list)

const reset = () => {
  searchParam.value = {
    state: undefined,
    merchantName: '',
    createStart: undefined,
    createEnd: undefined,
  }
  beginAndEnd.value = undefined
  confirmTime.value = undefined

  console.log('重置后的搜索参数:', searchParam.value)

  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10,
    }
    console.log('重置后调用接口参数:', params)
    listApiRun(params)
  })
}

const dataSource = computed(() => (data.value?.records || []) as ExtendedBond[])

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}))

const handleTableChange = (pag: { current: number; pageSize: number }) => {
  // 确保使用最新的搜索参数和过滤参数，优先使用filterInputs中的非空值
  const params = {
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  }

  console.log('最终查询参数:', params)
  listApiRun(params)
}

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(
  () => beginAndEnd.value,
  (n: any) => {
    if (n) {
      searchParam.value.createStart = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
      searchParam.value.createEnd = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
    } else {
      searchParam.value.createStart = undefined
      searchParam.value.createEnd = undefined
    }
  },
)
const confirmTime = ref<[Dayjs, Dayjs]>()
watch(
  () => confirmTime.value,
  (n: any) => {
    if (n) {
      searchParam.value.receiveStart = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
      searchParam.value.receiveEnd = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
    } else {
      searchParam.value.receiveStart = undefined
      searchParam.value.receiveEnd = undefined
    }
  },
)

const detailVisible = ref(false)
const currentId = ref<number>()

const handleDetail = (record: Bond) => {
  currentId.value = record.id as number
  detailVisible.value = true
}

const depositConfirmVisible = ref(false)
const refundConfirmVisible = ref(false)
const confirmRecord = ref<ExtendedBond | null>(null)
const confirmLoading = ref(false)
const depositLoading = ref(false)

// 添加驳回原因相关变量
const rejectReasonVisible = ref(false)
const rejectReason = ref('')
const depositRejectVisible = ref(false) // 添加保证金缴纳驳回弹窗状态

// 添加动态z-index管理
const rejectModalZIndex = ref(2000)

const fileList = ref<UploadFiles[]>([])
const depositFileList = ref<UploadFiles[]>([])
const confirmRemark = ref('')

// 清空退款确认弹框数据
const clearRefundForm = () => {
  // 先重置为空数组，强制UI组件更新
  fileList.value = []
  confirmRemark.value = ''
  rejectReason.value = '' // 清空驳回原因

  // 确保DOM更新后再次检查
  nextTick(() => {
    // 二次确认文件列表已清空
    if (fileList.value.length > 0) {
      fileList.value = []
    }
  })
}

const handleConfirm = async (record: ExtendedBond) => {
  if (!record.id) return
  confirmRecord.value = record
  console.log(fileList.value, 'fileList.value')

  try {
    const detail = await bondApi.get(record.id)
    const extendedDetail = detail as ExtendedBond

    confirmRecord.value = extendedDetail
    if (record.state === MarginStatusEnum.UNDER_VERIFICATION) {
      if (record.type === 1) {
        depositConfirmVisible.value = true
      } else if (record.type === 2) {
        // 打开退款弹框前先清空数据
        clearRefundForm()
        refundConfirmVisible.value = true
      }
    }
  } catch (error) {
    console.error('获取详情失败:', error)
  }
}

// 修改退款确认函数，添加驳回原因处理
const handleRefundConfirm = async (state: number = 10) => {
  if (!confirmRecord.value?.id) return

  // 如果是驳回操作，先弹出驳回原因输入框
  if (state === MarginStatusEnum.REJECTED) {
    // 动态设置更高的z-index
    rejectModalZIndex.value = 2000 + Math.floor(Math.random() * 100)
    rejectReasonVisible.value = true
    return
  }

  // 只有在确认通过时才检查必填项
  if (state === MarginStatusEnum.COMPLETED) {
    if (!fileList.value || fileList.value.length === 0) {
      message.error('请上传付款凭证')
      return
    }
  }

  try {
    confirmLoading.value = true
    await bondApi.refundConfirm({
      id: confirmRecord.value.id,
      state: state, // 确认状态
      pathType: FileTypeConstant.RECEIPT.code,
      path: fileList.value.map((file) => file.filePath).filter(Boolean) as string[],
      confirmRemark: confirmRemark.value,
      rejectReason: rejectReason.value, // 添加驳回原因
    })
    refundConfirmVisible.value = false
    handleTableChange({ current: 1, pageSize: 10 })
    clearRefundForm()
    message.success(state === MarginStatusEnum.COMPLETED ? '操作成功' : '驳回成功')
  } catch (error) {
    console.error('确认失败:', error)
    message.error('操作失败，请重试')
  } finally {
    confirmLoading.value = false
  }
}

// 修改confirmReject函数，处理保证金缴纳和退款确认的驳回
const confirmReject = async () => {
  if (!rejectReason.value) {
    message.error('请输入驳回原因')
    return
  }

  try {
    confirmLoading.value = true

    // 判断是保证金缴纳驳回还是退款确认驳回
    if (confirmRecord.value?.isDepositReject) {
      // 保证金缴纳驳回
      const submitData = {
        id: confirmRecord.value?.id,
        sapReceiveNo: depositForm.value.sapReceiveNo,
        state: MarginStatusEnum.REJECTED, // 驳回状态
        pathType: FileTypeConstant.RECEIPT.code,
        path: depositFileList.value.map((file) => file.filePath).filter(Boolean) as string[],
        rejectReason: rejectReason.value,
      }

      await bondApi.deposit(submitData)
      depositConfirmVisible.value = false
    } else {
      // 退款确认驳回
      await bondApi.refundConfirm({
        id: confirmRecord.value?.id,
        state: MarginStatusEnum.REJECTED, // 驳回状态
        pathType: FileTypeConstant.RECEIPT.code,
        path: fileList.value.map((file) => file.filePath).filter(Boolean) as string[],
        confirmRemark: confirmRemark.value,
        rejectReason: rejectReason.value,
      })
      refundConfirmVisible.value = false
    }

    // 关闭驳回原因弹窗
    rejectReasonVisible.value = false

    // 刷新数据
    handleTableChange({ current: 1, pageSize: 10 })
    clearRefundForm()
    clearDepositForm()
    message.success('驳回成功')
  } catch (error) {
    console.error('驳回失败:', error)
    message.error('操作失败，请重试')
  } finally {
    confirmLoading.value = false
    // 重置标记
    if (confirmRecord.value) {
      confirmRecord.value.isDepositReject = false
    }
  }
}

const depositForm = ref<DepositForm>({
  amount: null,
  sapReceiveNo: '',
  merchantName: '',
  fileList: [] as UploadFiles[],
})

// 清空缴纳表单
const clearDepositForm = () => {
  depositForm.value = {
    amount: null,
    sapReceiveNo: '',
    merchantName: '',
    fileList: [],
  }
  depositFileList.value = []
  fileList.value = []
  rejectReason.value = '' // 清空驳回原因
}

// 添加收款记录选择弹窗相关状态
const receiptSelectVisible = ref(false)

// 打开选择收款记录弹窗
const openReceiptSelect = () => {
  receiptSelectVisible.value = true
}

// 选择收款记录
const handleSelectReceipt = (record: ReceiptRecord) => {
  const extendedRecord = record as any

  if (extendedRecord.originalData) {
    const budat = extendedRecord.originalData.budat || ''
    const belnr = extendedRecord.originalData.belnr || ''
    if (budat) {
      try {
        const date = new Date(Number(budat))
        const year = date.getFullYear()
        depositForm.value.sapReceiveNo = `${year}${belnr}`
      } catch (error) {
        depositForm.value.sapReceiveNo = belnr
      }
    } else {
      depositForm.value.sapReceiveNo = belnr
    }

    depositForm.value.merchantName = extendedRecord.originalData.name1 || ''
    depositForm.value.amount = parseFloat(extendedRecord.originalData.dmbtr || '0')
  } else {
    depositForm.value.sapReceiveNo = record.sapReceiveNo || ''
    depositForm.value.merchantName = record.merchantName || ''
    depositForm.value.amount = record.amount || 0
  }
}

const handleDepositSubmit = async (state: number = 10) => {
  try {
    // 如果是驳回操作，先弹出驳回原因输入框
    if (state === MarginStatusEnum.REJECTED) {
      // 动态设置更高的z-index
      rejectModalZIndex.value = 2000 + Math.floor(Math.random() * 100)
      rejectReasonVisible.value = true
      // 存储当前操作是保证金缴纳驳回
      confirmRecord.value.isDepositReject = true
      return
    }

    // 只有在确认通过时才检查必填项
    if (state === MarginStatusEnum.COMPLETED) {
      if (!depositFileList.value || depositFileList.value.length === 0) {
        message.error('请上传收据')
        return
      }

      if (!depositForm.value.sapReceiveNo) {
        message.error('请选择收款单号')
        return
      }
    }

    depositLoading.value = true
    // 准备提交的数据
    const submitData = {
      id: confirmRecord.value?.id,
      sapReceiveNo: depositForm.value.sapReceiveNo,
      state: state,
      pathType: FileTypeConstant.RECEIPT.code,
      path: depositFileList.value.map((file) => file.filePath).filter(Boolean) as string[],
      rejectReason: rejectReason.value, // 添加驳回原因
    }

    await bondApi.deposit(submitData)

    depositConfirmVisible.value = false
    handleTableChange({ current: 1, pageSize: 10 })

    // 清空表单
    depositForm.value.sapReceiveNo = ''
    depositForm.value.merchantName = ''
    depositForm.value.amount = null
    depositFileList.value = []
    rejectReason.value = '' // 清空驳回原因
    message.success(state === MarginStatusEnum.COMPLETED ? '操作成功' : '驳回成功')
    handleTableChange({ current: 1, pageSize: 10 })
  } catch (err) {
    console.error('表单验证失败:', err)
    message.error('操作失败，请重试')
  } finally {
    depositLoading.value = false
  }
}

const uploadLoading = ref<boolean>(false)
// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL || ''
const uploadRequest = (options: any) => {
  uploadLoading.value = true

  const formData = new FormData()
  formData.append('file', options.file)

  console.log('开始上传文件:', options.file.name)

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path
      options.file.fileName = options.file.name
      options.onProgress(100)
      options.onSuccess(it, options.file)

      console.log('文件上传成功:', options.file)

      // 确保文件被添加到相应的列表中
      if (options.data && options.data.type === 'refund') {
        if (!fileList.value.some((f) => f.fileName === options.file.name)) {
          fileList.value.push(options.file)
        }
      } else {
        if (!depositFileList.value.some((f) => f.fileName === options.file.name)) {
          depositFileList.value.push(options.file)
          console.log(depositFileList.value, depositFileList.value.length, '添加文件')
        }
      }
    })
    .catch((error) => {
      console.error('上传失败:', error)
      message.error('文件上传失败，请重试')
    })
    .finally(() => {
      uploadLoading.value = false
    })
}

//删除上传凭证
const handleRemove = (file: UploadFiles) => {
  const targetIndex = fileList.value.findIndex((item) => item.fileName === file.fileName)
  if (targetIndex > -1) {
    fileList.value.splice(targetIndex, 1)
  }
  console.log(fileList.value, 'fileList.value')
}

const handleDepositFileRemove = (file: UploadFiles) => {
  const targetIndex = depositFileList.value.findIndex(
    (item) => item.uid === file.uid, // 使用文件唯一标识比对更可靠
  )

  if (targetIndex !== -1) {
    depositFileList.value.splice(targetIndex, 1)
    console.log(`已移除文件: ${file.name}`, depositFileList.value)
  } else {
    console.warn('未找到待删除文件', file)
  }
}
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="merchantName">服务商名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="merchantName"
              v-model:value="searchParam.merchantName"
              allow-clear
              placeholder="请输入服务商名称"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="state">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.state" allow-clear placeholder="请输入状态" style="width: 100%">
              <h-select-option :value="MarginStatusEnum.COMPLETED">{{ MarginStatusMap[MarginStatusEnum.COMPLETED] }}</h-select-option>
              <h-select-option :value="MarginStatusEnum.REJECTED">{{ MarginStatusMap[MarginStatusEnum.REJECTED] }}</h-select-option>
              <h-select-option :value="MarginStatusEnum.UNDER_VERIFICATION">{{ MarginStatusMap[MarginStatusEnum.UNDER_VERIFICATION] }}</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="createTime">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" allow-clear style="width: 100%" value-format="YYYY-MM-DD" />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="verificationTime">核对时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="confirmTime" allow-clear style="width: 100%" value-format="YYYY-MM-DD" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="merchantName">单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="merchantName"
              v-model:value="searchParam.recordNo"
              allow-clear
              placeholder="请输入服务商名称"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="state">操作类别：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.type" allow-clear placeholder="请输入操作类别" style="width: 100%">
              <h-select-option :value="1">保证金缴纳</h-select-option>
              <h-select-option :value="2">保证金退款</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="createTime">金额：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="merchantName"
              v-model:value="searchParam.amount"
              allow-clear
              placeholder="请输入金额"
              type="number"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="verificationTime">核对人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="merchantName" v-model:value="searchParam.receiveName" allow-clear placeholder="请输入核对人" />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="12" style="text-align: left"></h-col>
        </h-row>
      </h-col>

      <h-col :span="24">
        <h-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          :row-key="(record) => record.id"
          :scroll="{ x: 1500 }"
          :size="'small'"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'recordNo'">
              <h-tooltip :title="record.recordNo" placement="top">
                <span>{{ record.recordNo || '-' }}</span>
              </h-tooltip>
            </template>
            <template v-else-if="column.dataIndex === 'merchantName'">
              <h-tooltip :title="record.merchantName" placement="top">
                <span>{{ record.merchantName || '-' }}</span>
              </h-tooltip>
            </template>
            <template v-else-if="column.dataIndex === 'amount'">
              <h-tooltip :title="record.amount != null ? formatNumberThousands(record.amount) + ' 元' : '-'" placement="top">
                <span>{{ record.amount != null ? formatNumberThousands(record.amount) + ' 元' : '-' }}</span>
              </h-tooltip>
            </template>
            <template v-else-if="column.dataIndex === '_operator'">
              <template v-if="record.state === MarginStatusEnum.REJECTED || record.state === MarginStatusEnum.COMPLETED">
                <h-button type="link" @click="handleDetail(record)">详情</h-button>
              </template>
              <template v-else-if="record.state === MarginStatusEnum.UNDER_VERIFICATION">
                <h-button type="link" @click="handleConfirm(record)">确定</h-button>
              </template>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <DetailDialog :id="currentId" :visible="detailVisible" @cancel="detailVisible = false" />

    <!-- 缴纳确认弹框 -->
    <a-modal
      :footer="null"
      :visible="depositConfirmVisible"
      destroyOnClose
      style="min-width: 400px"
      title="保证金缴纳确认"
      width="400px"
      @cancel="clearDepositForm(); depositConfirmVisible = false">
      <div v-if="confirmRecord" class="detail-content">
        <div class="detail-item">单号：{{ confirmRecord.recordNo }}</div>
        <div class="detail-item">操作类别：保证金缴纳</div>
        <div class="detail-item">金额：{{ formatNumberThousands(confirmRecord.amount) }} 元</div>
        <div class="detail-item">创建时间：{{ confirmRecord.gmtCreate }}</div>
        <div class="detail-item">备注：{{ confirmRecord.remark }}</div>
        <div class="detail-item">
          支付凭证：
          <template v-if="confirmRecord.attachList && confirmRecord.attachList.length">
            <div v-for="(item, index) in confirmRecord.attachList.filter((item) => item.type === 20)" :key="index">
              <a :href="item.path" target="_blank">支付凭证 {{ index + 1 }}</a>
            </div>
          </template>
          <template v-else-if="confirmRecord.payPath && confirmRecord.payPath.length">
            <div v-for="(url, index) in confirmRecord.payPath" :key="index">
              <a :href="url" target="_blank">支付凭证 {{ index + 1 }}</a>
            </div>
          </template>
          <template v-else> 无</template>
        </div>
        <div class="detail-item" style="display: flex; align-items: center">
          <span style="min-width: 100px">SAP收款单号：</span>
          <div style="flex: 1">
            <h-input
              v-model:value="depositForm.sapReceiveNo"
              disabled
              placeholder="请选择收款单号"
              style="width: 170px"
            />
            <h-button style="margin-left: 10px" type="primary" @click="openReceiptSelect">选择</h-button>
          </div>
        </div>
        <div class="detail-item" style="display: flex; align-items: center; height: 30px">
          <span style="min-width: 100px; height: 30px; line-height: 30px">收据上传：</span>
          <div style="flex: 1; height: 100%">
            <h-upload
              v-model:fileList="depositFileList"
              :before-upload="beforeUpload"
              :custom-request="uploadRequest"
              :max-count="1"
              :multiple="true"
              :show-upload-list="true"
              accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
              style="height: 100%"
              @remove="handleDepositFileRemove"
            >
              <h-button
                :loading="uploadLoading"
                style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 200px"
              >
                <upload-outlined></upload-outlined>
                上传收据
              </h-button>
            </h-upload>
          </div>
        </div>
        <div class="footer-btns top">
          <h-button :loading="uploadLoading" @click="handleDepositSubmit(MarginStatusEnum.REJECTED)">驳回</h-button>
          <h-button :loading="uploadLoading" style="margin-left: 8px" type="primary" @click="handleDepositSubmit(MarginStatusEnum.COMPLETED)"
            >确定
          </h-button>
        </div>
      </div>
    </a-modal>

    <!-- 退款确认弹框 -->
    <a-modal
      :footer="null"
      :visible="refundConfirmVisible"
      destroyOnClose
      title="保证金退款确认"
      width="500px"
      @cancel="clearRefundForm();refundConfirmVisible = false"
    >
      <div v-if="confirmRecord" class="detail-content">
        <div class="detail-section">
          <div class="detail-item">单号：{{ confirmRecord.recordNo }}</div>
          <div class="detail-item">操作类别：{{ confirmRecord.type === 1 ? '缴纳' : '退款' }}</div>
          <div class="detail-item">金额：{{ formatNumberThousands(confirmRecord.amount) }} 元</div>
          <div class="detail-item">创建时间：{{ confirmRecord.gmtCreate }}</div>
          <div class="detail-item">
            退款申请单：
            <template v-if="confirmRecord.attachList && confirmRecord.attachList.length">
              <div v-for="(item, index) in confirmRecord.attachList.filter((item) => item.type === 23)" :key="index">
                <a :href="item.path" target="_blank">退款申请单 {{ index + 1 }}</a>
              </div>
            </template>
            <template v-else-if="confirmRecord.refundPath && confirmRecord.refundPath.length">
              <div v-for="(url, index) in confirmRecord.refundPath" :key="index">
                <a :href="url" target="_blank">退款申请单 {{ index + 1 }}</a>
              </div>
            </template>
            <template v-else> 无</template>
          </div>
          <div class="detail-item">备注：{{ confirmRecord.remark }}</div>
        </div>
        <div class="detail-section">
          <div class="detail-title">退款确认</div>
          <div class="detail-item">
            付款凭证：
            <h-upload
              v-model:value="fileList"
              :custom-request="uploadRequest"
              :data="{ type: 'refund' }"
              :max-count="1"
              :multiple="true"
              :show-upload-list="true"
              accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
              @remove="handleRemove"
            >
              <h-button :loading="uploadLoading">
                <upload-outlined></upload-outlined>
                上传凭证
              </h-button>
            </h-upload>
          </div>
          <div class="detail-item">
            备注：
            <h-input v-model:value="confirmRemark" :max-length="200" placeholder="请输入" style="width: 280px" />
          </div>
        </div>
        <div class="footer-btns">
          <h-button :loading="uploadLoading" @click="handleRefundConfirm(MarginStatusEnum.REJECTED)">驳回</h-button>
          <h-button :loading="uploadLoading" style="margin-left: 8px" type="primary" @click="handleRefundConfirm(MarginStatusEnum.COMPLETED)"
            >确定
          </h-button>
        </div>
      </div>
    </a-modal>

    <!-- 添加驳回原因弹框 -->
    <a-modal
      :footer="null"
      :visible="rejectReasonVisible"
      :z-index="rejectModalZIndex"
      title="驳回原因"
      width="400px"
      @cancel="rejectReasonVisible = false;rejectReason = ''"
    >
      <div class="reject-reason-content">
        <h-input v-model:value="rejectReason" :max-length="200" placeholder="请输入驳回原因" />
        <div class="footer-btns">
          <h-button
            @click="rejectReasonVisible = false;rejectReason = ''">取消
          </h-button>
          <h-button :loading="confirmLoading" style="margin-left: 8px" type="primary" @click="confirmReject"
            >确定
          </h-button>
        </div>
      </div>
    </a-modal>

    <!-- 使用封装的收款记录选择组件 -->
    <ReceiptSelect
      :visible="receiptSelectVisible"
      :z-index="rejectModalZIndex"
      @cancel="receiptSelectVisible = false"
      @select="handleSelectReceipt"
      @update:visible="receiptSelectVisible = $event"
    />
  </div>
</template>

<style lang="less" scoped>
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.footer-btns {
  margin-top: 24px;
  text-align: right;
}

.detail-content {
  padding: 8px 0;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-title {
  font-weight: bold;
  margin-bottom: 12px;
}

.detail-item {
  margin-bottom: 12px;
  line-height: 22px;
  display: flex;
  align-items: flex-start;

  > a {
    margin-right: 8px;
  }
}

.ant-upload-list-item-container {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

:deep(.ant-motion-collapse) {
  width: 245px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.top {
  margin-top: 65px;
}

.reject-reason-content {
  padding: 16px 0;

  .footer-btns {
    margin-top: 24px;
    text-align: right;
  }
}
</style>
