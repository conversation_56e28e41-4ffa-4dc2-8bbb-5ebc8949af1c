<script lang="ts" setup>
'orderList'
// 平台端-订单列表页面
import {
  ref,
  reactive,
  onMounted,
  onUnmounted,
  defineProps,
  computed,
  watch,
  onActivated,
  inject,
} from 'vue'
import {
  Button,
  Pagination,
  message,
  Modal,
} from 'ant-design-vue'
import SearchDrawer from './components/Dialog.vue'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton'
import {
  SearchParams,
  MiceBidManOrderList,
  OrderListResponse,
  OrderListConstant,
  PlatformCount,
  ProcessNode,
  SelTabObj,
  TabList,
  CountSum,
  MerchantType,
  ApproveListConstant,
  SchemeChangeState,
  MiceBillUploadStateTypeConstant,
} from '@haierbusiness-front/common-libs'
import { miceBidManOrderListApi, schemeApi, portalApi } from '@haierbusiness-front/apis'
import { useRoute, useRouter } from 'vue-router'

import { getDealTime, routerParam, getDataBy, formatNumberThousands, saveDataBy } from '@haierbusiness-front/utils'

const props = defineProps({
  type: {
    type: String,
    default: 'manage',
    // manage - 后台-订单列表
    // user - 用户端-订单列表
  },
})

const isTestForCiCi = ref(false)

// 议价
const bargainingShow = ref<boolean>(false) // 议价
const bargainingLoading = ref<boolean>(false) // 议价
const priceChangeList = ref([]) //

const orderBtnLoading = ref(false)

const scrollPosition = ref(0)
// 状态切换对象，用于控制顶部标签页的选中状态
const selTabObj = reactive<SelTabObj>({
  selTab1: 0,
  selTab2: 0,
})
const selTab = (num: number | string, type?: string) => {
  searchParams.pageNum = 1
  if (selTabObj[type] === num) return
  if (type) selTabObj[type] = num
  if (num !== 0) {
    countSum.processNodes = num
    searchParams.processNodes = num
    getOrderList()
  } else {
    countSum.processNodes = undefined
    searchParams.processNodes = undefined
    getOrderList('searchList')
  }
}
const options = ref<[]>([])

const loading = ref(false)

// 统计总数对象，用于存储各类筛选条件的统计数量
const countSum = reactive<CountSum>({
  processNodes: undefined,
  states: [],
  keyword: undefined,
  demandItem: undefined,
  mainCode: undefined,
  miceName: undefined,
  miceType: undefined,
  operatorCode: undefined,
  operatorName: undefined,
  operatorPhone: undefined,
  consultantUserCode: undefined,
  consultantUserName: undefined,
  contactUserCode: undefined,
  contactUserName: undefined,
  contactUserPhone: undefined,
  districtType: undefined,
  startDateStart: undefined,
  startDateEnd: undefined,
  endDateStart: undefined,
  endDateEnd: undefined,
  winTheBidMerchantName: undefined,
  winTheBidMerchantCode: undefined,
  isSpecialPowers: undefined,
  isUrgent: undefined,
  isAppoint: undefined,
  demandProvince: undefined,
  demandCity: undefined,
  demandDistrict: undefined,
  demandCenterMarker: undefined,
  isConsignment: undefined,
  isPlaceOrder: undefined,
  isChange: undefined,
  isBidFailure: undefined,
  pageNum: undefined,
  pageSize: undefined,
})

// 搜索参数对象，用于存储搜索条件
const searchParams = reactive<SearchParams>({
  processNodes: undefined,
  statesName: [],
  states: [],
  keyword: undefined,
  demandItem: undefined,
  mainCode: undefined,
  miceName: undefined,
  miceType: undefined,
  operatorCode: undefined,
  operatorName: undefined,
  operatorPhone: undefined,
  consultantUserCode: undefined,
  consultantUserName: undefined,
  contactUserCode: undefined,
  contactUserName: undefined,
  contactUserPhone: undefined,
  districtType: undefined,
  pageNum: 1,
  pageSize: 10,
})
const router = useRouter()
const route = useRoute()

// 费用支付
const feesPayShow = ref<boolean>(false) // 弹窗
const feesPayUrl = ref<string>('') // 支付url
const platformFeeRate = ref<number>(0) // 平台服务费费率
const platformFee = ref<number>(0) // 服务费
const payWinTheBidTotalPrices = ref<number>(0) // 中标价格

const showDetailBtn = (record, type: string) => {
  const recordTemp = {
    miceId: record.miceId,
    interactEndDate: record.interactEndDateMinute, // 方案提报截止时间
    schemeType: type, // view/notReported/reported/schemeView/biddingView
    pdMainId: record.pdMainId, //
    pdVerId: record.pdVerId, //
    schemeChangeType: 'schemeOrderView',
  }

  router.push({
    // /src/page/billUploadScheme/components/showDetail.vue
    path: '/mice-merchant/orderList/showDetail',
    query: {
      record: routerParam(recordTemp),
    },
  })
}

// 获取订单状态
const getOrderState = async (miceId: number, type: string) => {
  if (orderBtnLoading.value) return false

  orderBtnLoading.value = true

  // 商户端
  const stateRes = await miceBidManOrderListApi.merchantShowState({
    miceIds: [miceId],
  })

  if (stateRes[0]?.isSchemeBidSwitch && type == 'schemeBidSwitch') {
    // 中标方案切换中
    orderBtnLoading.value = false
    return true
  }
  if (stateRes[0]?.isSchemePriceChange && type == 'schemePriceChange') {
    // 方案议价中
    orderBtnLoading.value = false
    return true
  }
  if (stateRes[0]?.isSchemeChange && type == 'schemeChange') {
    // 方案变更中
    orderBtnLoading.value = false
    return true
  }
  if (stateRes[0]?.isBillSubmit && type == 'billSubmit') {
    // 账单上传中
    orderBtnLoading.value = false
    return true
  }

  Modal.warning({
    title:
      stateRes[0]?.notSchemeBidSwitchReason ||
      stateRes[0]?.notSchemePriceChangeReason ||
      stateRes[0]?.notSchemeChangeReason ||
      stateRes[0]?.notBillSubmitReason,
    okText: '确定',
  })

  orderBtnLoading.value = false

  return false
}

const btnJump = async (order) => {
  // 账单上传
  if(!checkBillStatus(order.winTheBidMerchants)) {
    Modal.warning({
      title: '账单上传',
      content: '账单已提交，请等待其他服务商上传账单！',
      okText: '确定',
    })
    return
  }
  
  const isPass = await getOrderState(order.miceId, 'billSubmit')
  if (!isPass) return

  if (order.processNode === 'MICE_COMPLETED') {
    // 会议完成
    const params = {
      miceId: order.miceId,
      schemeType: 'billUpload',
    }

    // 账单上传
    router.push({
      path: '/mice-merchant/billUploadScheme/billUploadschemeInteract',
      query: {
        record: routerParam(params),
      },
    })
  }
}

// 订单列表数据
const orderList = ref<MiceBidManOrderList[]>([])
// 总数据条数
const total = ref(0)

// 处理时间映射，用于显示订单处理时长
const dealTimeMap = ref<Record<string, string>>({})
let timer: number | null = null

const getRatio = (order) => {
  if (['M0', 'M1'].includes(authority.value)) {
    if (['SCHEME_SUBMIT'].includes(order.processNode) && order.schemeWaitSubmitNum && order.schemePushNum) {
      return (
        `<span style='color:#1677ff'>${order.schemePushSubmitNum}</span>` +
        '/' +
        (Number(order.schemePushSubmitNum) + Number(order.schemeWaitSubmitNum))
      )
    } else if (['BIDDING'].includes(order.processNode) && order.bidWaitSubmitNum && order.bidSchemePushNum) {
      return (
        `<span style='color:#1677ff'>${order.bidSchemePushSubmitNum}</span>` +
        '/' +
        (Number(order.bidSchemePushSubmitNum) + Number(order.bidWaitSubmitNum))
      )
    }
  }
}
const moreBtn = (order, type) => {
  const params = {
    miceId: order.miceId,
    schemeType: type, // notBidding - 待竞价
    miceSchemeId: order.miceSchemeId, // 方案ID
    pdMainId: order.pdMainId, //
    pdVerId: order.pdVerId, //
  }
  let url
  if (['change'].includes(type)) {
    url = '/mice-merchant/scheme/schemePresentChange'
  }
  router.push({
    path: url,
    query: {
      record: routerParam(params),
    },
  })
}
const presentOrder = async (order) => {
  Modal.confirm({
    title: '礼品下单',
    content: '是否确认礼品下单？',
    async onOk() {
      const res = await schemeApi.presentOrder({
        mainCode: order.mainCode,
      })
      if (res.success) {
        message.success('下单成功！')
        getOrderList()
      }
    },
    onCancel() {
      console.log('Cancel')
    },
    class: 'test',
  })
}
// 更新处理时间显示
const updateDealTimes = () => {
  orderList.value.forEach((order: MiceBidManOrderList) => {
    if (order.stateGmtModified) {
      dealTimeMap.value[order.mainCode] = getDealTime(order.stateGmtModified)
    }
  })
}
// 获取公司名称，根据屏幕宽度自动截断
const getCompany = (companyArr: []) => {
  const arr = companyArr?.filter((e) => e.merchantType === '2') || []
  return arr.length > 0 ? arr[0]?.merchantName : companyArr[0]?.merchantName || '-'
}
const getContact = (companyArr: []) => {
  const arr = companyArr?.filter((e) => e.merchantType === '2') || []
  return arr.length > 0 ? arr[0]?.contactMerchantName : companyArr[0]?.contactMerchantName || '-'
}

// //查找winTheBillState字段
const checkBillStatus = (list) => {
  if (!Array.isArray(list)) return true

  const res = list.find((item) => item?.winTheBillState)
  if (!res) return true

  return [MiceBillUploadStateTypeConstant.REJECT.code, MiceBillUploadStateTypeConstant.APPROVE_REJECT.code].includes(res.winTheBillState)
}

//别删有用

const showBtn = (order) => {
  if (['DEMAND_CONFIRM'].includes(order.processNode)) return ''
  else if (['VENDOR_INVOICE_ENTRY'].includes(order.processNode)) return '发票上传'
  else if (['MICE_COMPLETED'].includes(order.processNode)) return '账单上传'

  else return ProcessNode.ofType(order.processNode)?.desc
}
const judgeAuthority = (order: MiceBidManOrderList, type: string) => {
  // 会议完成、服务商发票录入
  return ['MICE_COMPLETED', 'VENDOR_INVOICE_ENTRY'].includes(order.processNode)
}
const authority = ref('U')
const tabList = ref<TabList[]>([])
const store = applicationStore()
const widthSearch = ref('80%')
const merchantType = ref<number>(null) // 服务商类型
const getOrderList = async (type?: string, first?: boolean) => {
  // 获取登录服务商的类型
  const res = await schemeApi.getMerchantByUser({})

  // 服务商的类型
  // 1-酒店,2-旅行社,3-保险,4-礼品,5-用车
  merchantType.value = res.merchantType

  loading.value = true
  if (route.query.username) {
    searchParams.consultantUserCode = route.query.username
  }

  if (type === 'searchList') {
    let authorArray = ['会议完成', '服务商发票录入', '流程结束']

    if (props.type === 'manage') {
      let tempStates = JSON.parse(JSON.stringify(searchParams))
      if (tempStates.states?.length > 0) {
        tempStates.states = JSON.stringify(tempStates.states).replaceAll('[', '').replaceAll(']', '')
      }
      tabList.value = await miceBidManOrderListApi.merchantCount({ ...countSum, ...tempStates })
    } else {
      tabList.value = await miceBidManOrderListApi.merchantCount(countSum)
    }
    // tabList.value = tabList.value.sort((a, b) => {
    //   return b.counts - a.counts;
    // });
    tabList.value = tabList.value.filter((item) => {
      if (authorArray.includes(ProcessNode.ofType(item.processNode)?.desc)) return item
    })
    tabList.value.sort(
      (a, b) =>
        authorArray.indexOf(ProcessNode.ofType(a.processNode)?.desc) -
        authorArray.indexOf(ProcessNode.ofType(b.processNode)?.desc),
    )
  }

  let tempStates = JSON.parse(JSON.stringify(searchParams))
  if (tempStates.states?.length > 0) {
    tempStates.states = JSON.stringify(tempStates.states).replaceAll('[', '').replaceAll(']', '')
  }
  if (props.type === 'manage') {
    // let temp = '';
    // ProcessNode.toArray().forEach((item) => {
    //   if (item?.code !== 'DEMAND_SUBMIT') temp += item.code + ',';
    // });
    // if (!tempStates.processNodes) tempStates.processNodes = temp;
    const res = (await miceBidManOrderListApi.merchantOrderList(tempStates)) as OrderListResponse
    orderList.value = JSON.parse(JSON.stringify(res.records))
    total.value = res.total
  } else {
    const res = (await miceBidManOrderListApi.merchantOrderList(tempStates)) as OrderListResponse
    orderList.value = JSON.parse(JSON.stringify(res.records))
    total.value = res.total
  }

  orderList.value.forEach(async (item, index) => {
    if (['DEMAND_SUBMIT', 'DEMAND_PRE_INTERACT'].includes(item.processNode)) {
      const res = await getCache(item.miceId)
      if (res) {
        const resData = JSON.parse(res)

        orderList.value[index].miceName = resData.miceName
        orderList.value[index].miceType = resData.miceType
        orderList.value[index].personTotal = resData.personTotal
        orderList.value[index].endDate = resData.endDate
        orderList.value[index].startDate = resData.startDate

        orderList.value[index].processNode = item.processNode
      }
    }
  })

  loading.value = false

  let element = document.getElementById('myElement')?.offsetWidth - 40 + 'px'
  widthSearch.value = element || '80%'

  window.addEventListener('resize', function () {
    let element = document.getElementById('myElement')?.offsetWidth - 40 + 'px'
    widthSearch.value = element || '80%'
  })
}
const getCache = async (miceId: string) => {
  if (!miceId) {
    return
  }

  const resCacheStr = await getDataBy({
    applicationCode: 'haierbusiness-mice-bid',
    cacheKey: 'haierbusiness-mice-bid_' + store.loginUser.value?.username + '_demandSubKey' + miceId, // 需求提报
  })
  const resStr = resCacheStr

  return resStr
}
// 复制文本到剪贴板
const getCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success('复制成功！')
  } catch (err) {
    message.error('复制失败')
  }
}
const showTip = (order) => {
  if (order.endType == 4 && order.delayEndingDate) {
    let schemeDeadline = new Date(order.delayEndingDate)
    let curDate = new Date()
    if (curDate < schemeDeadline) {
      let remaining = getDealTime(order.delayEndingDate)

      if (remaining.length <= 8 && remaining.slice(0, 2) == '00') {
        return remaining
      }
    }
  }
  return false
}
const getSerchItem = computed(() => {
  if (searchDrawer.value?.formState) {
    let tempObj = searchDrawer.value.formState
    Object.assign(tempObj, {
      consultantUserCode: searchParams.consultantUserCode,
    })
    for (let key in searchDrawer.value.formState) {
      if (searchDrawer.value.formState[key]?.length > 0) return true
    }
  }
  return false
})
const collapsed = inject('collapsed')
watch(collapsed, (newVal) => {
  if (newVal) {
    let element = document.getElementById('myElement')?.offsetWidth + 100 + 'px'
    widthSearch.value = element || '80%'
  } else {
    let element = document.getElementById('myElement')?.offsetWidth - 170 + 'px'
    widthSearch.value = element || '80%'
  }
})
// 页面加载时初始化数据
onMounted(async () => {
  isTestForCiCi.value = localStorage.getItem('testProcessSignForCiCi') === '1'

  getOrderList('searchList', true)
  // 启动定时器，每秒更新一次处理时间
  timer = window.setInterval(updateDealTimes, 1000)
})

onActivated(() => {
  getOrderList()
})
// 页面卸载时清理定时器
onUnmounted(() => {
  // 组件卸载时清除定时器
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

// 搜索方法
const handleSearch = () => {
  if (searchParams.statesName.length === 0) searchParams.states = []
  searchParams.pageNum = 1
  getOrderList(selTabObj.selTab1 === 0 ? 'searchList' : '')
}

// 新增：让输入框的@search事件也能触发搜索
const onSearch = async () => {
  let tempStates = JSON.parse(JSON.stringify(searchParams))
  let res
  if (props.type === 'manage') {
    res = (await miceBidManOrderListApi.orderList(tempStates)) as OrderListResponse
  } else {
    res = (await miceBidManOrderListApi.orderListU(tempStates)) as OrderListResponse
  }
  options.value = JSON.parse(JSON.stringify(res.records)).map((item) => {
    return { value: item.mainCode }
  })
}

// 重置搜索条件
const resetSearch1 = () => {
  ;(Object.keys(searchParams) as Array<keyof SearchParams>).forEach((key) => {
    if (key === 'pageNum' || key === 'pageSize') {
      searchParams[key] = key === 'pageNum' ? 1 : 10
    } else if (key === 'statesName' || key === 'states') {
      searchParams[key] = []
    } else {
      searchParams[key] = undefined
    }
  })
  getOrderList('searchList')
}

// 显示高级搜索抽屉
const searchDrawer = ref()
const showDrawer = () => {
  searchDrawer.value.show({
    consultantUserName: searchParams.consultantUserName,
    consultantUserCode: searchParams.consultantUserCode,
    states: searchParams.states,
    keyword: searchParams.keyword,
  })
}

// 处理高级搜索
const handleAdvancedSearch = (formData: SearchParams) => {
  Object.assign(searchParams, formData)
  searchParams.pageNum = 1
  getOrderList(selTabObj.selTab1 === 0 ? 'searchList' : '')
}

// 处理重置搜索
const handleResetSearch = (formData: SearchParams) => {
  Object.assign(searchParams, formData)
  getOrderList(selTabObj.selTab1 === 0 ? 'searchList' : '')
}

// 处理分页变化
const handlePageChange = (page: number, pageSize: number) => {
  searchParams.pageNum = page
  searchParams.pageSize = pageSize
  getOrderList()
}

// 审批列表弹窗相关
const approvalModalVisible = ref(false)
const currentApprovalOrder = ref(null)
const approvalList = ref([])

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'reason',
    key: 'reason',
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    key: 'gmtCreate',
  },
  {
    title: '完成时间',
    dataIndex: 'gmtModified',
    key: 'gmtModified',
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
  },
]
const mainId = ref('')
const orderCopy = (order) => {
  Modal.confirm({
    title: '复制订单',
    content: '是否确认复制订单？',
    async onOk() {
      let resDemand = await miceBidManOrderListApi.platformDetails({
        miceId: order.miceId,
        miceDemandId: order.miceDemandId,
      })
      let params = {
        ...resDemand,
        demandType: props.type === 'manage' ? 4 : 1, // 1, "用户提报版本"   4, "顾问代提版本"
      }
      const res = await portalApi.createOrder(params)

      if (res?.success && res?.data.mainId) {
        const record = {
          miceId: res?.data.mainId,
          mainCode: res?.data.mainCode || '',
          pdMainId: resDemand.pdMainId,
        }

        // 后端缓存
        await saveDataBy({
          applicationCode: 'haierbusiness-mice-bid',
          // 规则: haierbusiness-mice-bid_工号_你业务的缓存key
          cacheKey: 'haierbusiness-mice-bid_' + store.loginUser.username + '_demandSubKey' + record.miceId, // 需求提报
          cacheValue: JSON.stringify({
            ...params,
            ...record,
          }),
        })

        // 需求提报
        const localUrl = window.location.href
        const localHref = 'http://localhost:5182/#'
        const businessMiceBid = import.meta.env.VITE_MICE_BID_URL + '#'
        const openUrl =
          (localUrl.includes('/localhost') ? localHref : businessMiceBid) +
          '/demand/index' +
          '?record=' +
          routerParam(record)

        window.open(openUrl)
      }
      // if (res2.success) {
      //   message.success('复制成功！');
      //   getOrderList();
      // }
    },
    onCancel() {
      console.log('Cancel')
    },
    class: 'test',
  })
}
// 显示审批列表弹窗
const showApprovalModal = (order) => {
  currentApprovalOrder.value = order
  approvalModalVisible.value = true
  mainId.value = order.miceId
  getApprovalList()
}
const finishRemark = ref('')
const finishModal = ref(false)
const currentOrder = reactive({})
const onOk = async () => {
  if (finishRemark.value === '') {
    message.error('原因不能为空！')
    return
  }
  let res = await miceBidManOrderListApi.submitFinish({
    miceId: currentOrder.miceId,
    finishRemark: finishRemark.value,
  })
  if (res.success) {
    finishRemark.value = ''
    message.success('结束成功！')
    finishModal.value = false
    getOrderList()
  }
}

// 议价记录
let priceChangeColumns = [
  {
    title: '序号',
    dataIndex: '1',
    width: 50,
    ellipsis: true,
    align: 'center',
    customRender: ({ index }) => {
      return index + 1
    },
  },
  {
    title: '商户名称',
    dataIndex: 'dataIndex2',
    width: 280,
    ellipsis: true,
    align: 'center',
    customRender: ({ record }) => {
      return record.merchantName || '-'
    },
  },
  {
    title: '议价状态',
    dataIndex: 'dataIndex3',
    width: 130,
    ellipsis: true,
    align: 'center',

    customRender: ({ record }) => {
      const stateName = {
        10: '顾问待确认', // 发起议价确认中
        20: '顾问驳回', // 发起议价驳回
        30: '待服务商议价', // 议价提报中
        40: '用户待确认', // 议价确认中
        50: '议价完成',
        60: '议价驳回',
      }

      return stateName[record.state] || '-'
    },
  },
  {
    title: '变更前价格',
    dataIndex: 'dataIndex3',
    width: 110,
    ellipsis: true,
    align: 'center',

    customRender: ({ record }) => {
      return record.changeBeforePrice || '-'
    },
  },
  {
    title: '变更后价格',
    dataIndex: 'dataIndex4',
    width: 110,
    ellipsis: true,
    align: 'center',

    customRender: ({ record }) => {
      return record.changeAfterPrice || '-'
    },
  },
  {
    title: '议价原因',
    dataIndex: '5',
    width: 180,
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      return record.reason || '-'
    },
  },
  {
    title: '驳回原因',
    dataIndex: '5',
    // width: 160,
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      return record.rejectReason || '-'
    },
  },
]

// 方案议价
const schemeBargainingBtn = async (order) => {
  // 方案议价
  const isPass = await getOrderState(order.miceId, 'schemePriceChange')
  if (!isPass) return

  // 方案议价 - schemeBargainingEdit
  let params = {
    miceId: order.miceId,
    schemeChangeType: 'schemeBargainingEdit', // 方案议价
    pdMainId: order.pdMainId, //
    pdVerId: order.pdVerId, //
  }

  router.push({
    // /src/page/scheme/schemeChange.vue
    path: '/mice-merchant/scheme/bargaining',
    query: {
      record: routerParam(params),
    },
  })
}
// 议价记录查看
const schemeBargainingViewBtn = async (order) => {
  priceChangeList.value = []
  bargainingLoading.value = true
  bargainingShow.value = true

  const res = await schemeApi.merchantPriceChangeRecord({
    miceId: order.miceId,
  })

  priceChangeList.value = res || []

  bargainingLoading.value = false
}
const closeBargainingModal = () => {
  bargainingShow.value = false
}

const changeSchemeRecord = ref<boolean>(false) // 弹窗
const loadingModal = ref(false)
const priceChangeListScheme = ref<[]>([]) //

let priceChangeColumnsScheme = [
  {
    title: '订单号',
    dataIndex: 'mainCode',
    // width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '变更申请订单号',
    dataIndex: 'changeApplyCode',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '服务商',
    dataIndex: 'merchantName',
    width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '变更状态',
    dataIndex: 'stateName',
    // width: 160,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '变更说明',
    dataIndex: 'reason',
    // width: 160,
    align: 'center',
    ellipsis: true,
  },
]
const isCanPriceChange = ref(false)

// 方案变更
const schemeChangeViewBtn = async (order) => {
  currentApprovalOrder.value = order

  const res = await miceBidManOrderListApi.changeMerchantRecord({
    miceId: order.miceId,
  })

  priceChangeListScheme.value = res.map((item) => {
    return {
      ...item,
      stateName: SchemeChangeState.ofType(item.state)?.desc || '-',
    }
  })

  if (priceChangeListScheme.value.length > 0) {
    changeSchemeRecord.value = true
    isCanPriceChange.value = true
  } else {
    schemeChangeBtn()
  }
}

const schemeChangeBtn = async () => {
  // 方案变更
  const isPass = await getOrderState(currentApprovalOrder.value.miceId, 'schemeChange')
  if (!isPass) return

  const order = currentApprovalOrder.value

  const params = {
    miceId: order.miceId,
    schemeChangeType: 'schemeChangeEdit', // 方案变更
    pdMainId: order.pdMainId, //
    pdVerId: order.pdVerId, //
  }

  router.push({
    path: '/mice-merchant/scheme/change',
    query: {
      record: routerParam(params),
    },
  })
}

const overProcess = async (order) => {
  Object.assign(currentOrder, order)
  finishModal.value = true
}
const cancelSchemeModal = ref(false)
const cancelSchemeRemark = ref('')

const cancelScheme = async () => {
  if (cancelSchemeRemark.value == '') {
    message.error('请输入撤回原因！')
    return
  }
  const res = await schemeApi.againPush({ miceId: currentOrder.miceId, backReason: cancelSchemeRemark.value })
  if (res.success) {
    message.success('撤回成功！')
    cancelSchemeModal.value = false
    cancelSchemeRemark.value = ''
    getOrderList()
  }
}
const cancelProcess = async (order) => {
  Modal.confirm({
    title: '取消自动结束提报',
    content: '是否确认取消自动结束提报？',
    async onOk() {
      const res = await schemeApi.delayFinish({ miceId: order.miceId })
      if (res.success) {
        message.success('取消成功！')
        getOrderList()
      }
    },
    onCancel() {
      console.log('Cancel')
    },
    class: 'test',
  })
}
const showPush = async (order) => {
  let resDemand = await miceBidManOrderListApi.platformDetails({
    miceId: order.miceId,
  })
  let hotelIdList = []
  resDemand.hotels.forEach((item) => {
    hotelIdList.push(item.id)
  })

  router.push({
    path: '/bidman/scheme/publish/view',
    query: {
      record: JSON.stringify({
        miceId: order.miceId,
        miceDemandId: order.demandId,
        status: 'show',
        hotels: hotelIdList,
        type: 'show',
      }),
    },
  })
}
const showBidPushed = ref(false)

const bidedColumn = ref([
  {
    title: '服务商类型',
    dataIndex: 'type',
    key: 'type',
    customCell: (record, rowIndex, column) => {
      return { rowSpan: record.rowSpan }
    },
  },
  {
    title: '标的数量',
    dataIndex: 'num',
    key: 'num',
    customCell: (record, rowIndex, column) => {
      return { rowSpan: record.rowSpan }
    },
  },
  { title: '方案名称', dataIndex: 'schemeName', key: 'schemeName' },
  { title: '参与服务商', dataIndex: 'scheme', key: 'scheme' },
  {
    title: '竞价截止时间',
    dataIndex: 'biddingDeadline',
    key: 'biddingDeadline',
    customCell: (record, rowIndex, column) => {
      return { rowSpan: record.rowSpan }
    },
  },
])
const bidedData = ref([])

const showBid = async (order) => {
  let pushedData = await schemeApi.pushedDetails({
    miceId: order.miceId,
  })
  bidedData.value = []
  let num = 0
  pushedData.merchantTypes.forEach((item, index) => {
    item.schemes.forEach((item1, index1) => {
      num++
      let arr = []
      item1.merchants.forEach((item2) => {
        let tempItem = item2
        tempItem.bidMerchantName = item2.bidMerchantName
        tempItem.schemeName = `方案${num}`
        arr.push(item2)
      })
      bidedData.value.push({
        merchantType: item.merchantType,
        rowSpan: item.schemes.length == 1 ? 1 : index1 === 0 ? item.schemes.length : 0,
        type: MerchantType.ofType(item.merchantType)?.desc,
        num: item.bidNum,
        schemeName: `方案${num}`,
        biddingDeadline: pushedData.biddingDeadline,
        scheme: arr,
      })
    })
  })
  showBidPushed.value = true
}
const getApprovalList = async () => {
  const res = await miceBidManOrderListApi.recordPage({
    mainId: mainId.value,
    pageNum: 1,
    pageSize: 999,
  })

  approvalList.value = res.records
}
// 关闭审批列表弹窗
const closeApprovalModal = () => {
  approvalModalVisible.value = false
  feesPayShow.value = false
}

const PayHandleOk = () => {
  window.open(feesPayUrl.value)
}

// 查看审批详情
const viewApprovalDetail = (item) => {
  window.location.href = `${import.meta.env.VITE_BUSINESS_URL}hbweb/process/?code=${item.approveCode}#/details`
}
const handleStateChange = (values: string[]) => {
  searchParams.states = []
  searchParams.statesName = values
  OrderListConstant.toArray().forEach((item) => {
    if (values.includes(item.desc)) searchParams.states.push(item.code)
  })
}
// 撤销审批
const cancelApproval = async (item) => {
  const res = await miceBidManOrderListApi.recordRevoke({
    processCode: item.approveCode,
  })

  message.info('撤销审批成功')
}

const giftDeliveryModalVisible = ref(false)
const giftDeliveryTrackingNumbers = ref<string[]>([''])

const openGiftDeliveryModal = (order) => {
  currentApprovalOrder.value = order
  giftDeliveryModalVisible.value = true
  giftDeliveryTrackingNumbers.value = ['']
}

const addGiftDeliveryInput = () => {
  giftDeliveryTrackingNumbers.value.push('')
}

const updateGiftDeliveryTrackingNumber = (index: number, value: string) => {
  giftDeliveryTrackingNumbers.value[index] = value
}

const removeGiftDeliveryInput = (index: number) => {
  if (giftDeliveryTrackingNumbers.value.length > 1) {
    giftDeliveryTrackingNumbers.value.splice(index, 1)
  }
}

const submitGiftDelivery = async () => {
  // 这里可以做校验和提交逻辑
  let arr = JSON.parse(JSON.stringify(giftDeliveryTrackingNumbers.value))
  if (arr.filter((item) => item != '').length == 0) return message.error('请填写发货信息！')
  const res = await schemeApi.presentDeliver({
    miceId: currentApprovalOrder.value.miceId,
    mainCode: currentApprovalOrder.value.mainCode,
    logisticsNumber: arr.filter((item) => item != '').join(','),
    shipmentInfo: '1',
  })
  if (res.success) {
    message.success('发货成功！')
    giftDeliveryModalVisible.value = false
    getOrderList()
  }
}

const giftDeliveryInfoModalVisible = ref(false)
const giftDeliveryInfoList = ref()

const giftDeliveryInfoColumns = [
  { title: '快递单号', dataIndex: 'trackingNumber', key: 'trackingNumber' },
  { title: '发货时间', dataIndex: 'deliveryTime', key: 'deliveryTime' },
  { title: '发货人', dataIndex: 'deliveryUser', key: 'deliveryUser' },
]

const openGiftDeliveryInfoModal = async (order) => {
  const res = await schemeApi.presentDetail({ mainCode: order.mainCode })
  giftDeliveryInfoList.value = res.logisticsNumber.split(',').map((item) => {
    return {
      trackingNumber: item,
      deliveryTime: res.shipmentTime,
      deliveryUser: res.shipmentName,
    }
  })
  // 如需异步获取数据，可在此处请求
  giftDeliveryInfoModalVisible.value = true
}
</script>

<template>
  <a-spin :spinning="loading">
    <div id="myElement" class="container">
      <!-- 顶部区域：包含状态栏和搜索框 -->
      <a-affix v-if="props.type === 'user'" :offset-top="10">
        <div v-show="!route.query.username" :style="`width:${widthSearch}`" class="top-section">
          <div class="header">
            <!-- 状态切换栏：全部订单、待处理、进行中、待结算 -->
            <div class="status-bar">
              <div
                :class="'status-item ' + (selTabObj.selTab1 === 0 ? 'active' : '')"
                style="padding-right: 16px"
                @click="selTab(0, 'selTab1')"
              >
                <span>全部订单</span>
              </div>
              <div
                v-for="(item, index) in tabList"
                :key="item.processNode"
                :class="'status-item ' + (selTabObj.selTab1 === item.processNode ? 'active' : '')"
                @click="selTab(item.processNode, 'selTab1')"
              >
                <span>{{ ProcessNode.ofType(item.processNode).desc }}</span>
                <span class="order-count">{{ item.counts }}</span>
              </div>
            </div>

            <!-- 搜索区域：会议状态选择和关键词搜索 -->
            <div class="search-input">
              <a-auto-complete
                v-model:value="searchParams.keyword"
                :allow-clear="true"
                :options="options"
                :placeholder="'单号/名称/经办人/地点搜索'"
                size="small"
                @search="onSearch"
                @select="
                  () => {
                    options.value = []
                  }
                "
              />
              <Button class="primary1" type="primary" @click="handleSearch">搜索</Button>
              <Button class="plain1" @click="showDrawer">高级搜索</Button>
              <search-drawer
                v-if="showDrawer"
                ref="searchDrawer"
                :user1="{
                  consultantUserName: searchParams.consultantUserName,
                  consultantUserCode: searchParams.consultantUserCode,
                  states: searchParams.states,
                  keyword: searchParams.keyword,
                }"
                @resetSearch="handleResetSearch"
                @search="handleAdvancedSearch"
              />
            </div>
          </div>

          <!-- 过滤标签：接单、需求互动等业务流程标签 -->
          <div class="filter-tags"></div>
        </div>
      </a-affix>
      <div v-else v-show="!route.query.username" :style="`width:${widthSearch}`" class="top-section">
        <div class="header">
          <!-- 状态切换栏：全部订单、待处理、进行中、待结算 -->
          <div class="status-bar">
            <div
              :class="'status-item ' + (selTabObj.selTab1 === 0 ? 'active' : '')"
              style="padding-right: 16px"
              @click="selTab(0, 'selTab1')"
            >
              <span>全部订单</span>
            </div>
            <div
              v-for="(item, index) in tabList"
              :key="item.processNode"
              :class="'status-item ' + (selTabObj.selTab1 === item.processNode ? 'active' : '')"
              @click="selTab(item.processNode, 'selTab1')"
            >
              <span>{{ ProcessNode.ofType(item.processNode).desc }}</span>
              <span class="order-count">{{ item.counts }}</span>
            </div>
          </div>

          <!-- 搜索区域：会议状态选择和关键词搜索 -->
          <div class="search-input">
            <!-- <div class="search-label">会议状态:</div> -->
            <!-- <a-select
            :dropdownStyle="{ minWidth: '280px' }"
            v-model:value="searchParams.statesName"
            mode="multiple"
            :options="OrderListConstant.toArray()"
            :field-names="{ label: 'desc', value: 'desc' }"
            @change="handleStateChange"
            size="small"
            allowClear
          >
          </a-select> -->
            <a-input
              v-model:value="searchParams.keyword"
              :allow-clear="true"
              placeholder="单号/名称/经办人/地点搜索"
              size="small"
              type="text"
            />
            <!-- <a-auto-complete
              size="small"
              v-model:value="searchParams.keyword"
              :options="options"
              placeholder="单号/名称/经办人/地点搜索"
              :allow-clear="true"
              @select="
                () => {
                  options.value = [];
                }
              "
              @search="onSearch"
            /> -->
            <!-- <a-input
            size="small"
            type="text"
            v-model:value="searchParams.keyword"
            placeholder="单号/名称/经办人/地点搜索"
            allowClear
          /> -->
            <Button class="primary1" type="primary" @click="handleSearch">搜索</Button>
            <a-badge v-if="getSerchItem" dot>
              <Button class="plain1" @click="showDrawer">高级搜索</Button>
            </a-badge>
            <Button v-else class="plain1" @click="showDrawer">高级搜索</Button>
            <search-drawer
              v-if="showDrawer"
              ref="searchDrawer"
              :user1="{
                consultantUserName: searchParams.consultantUserName,
                consultantUserCode: searchParams.consultantUserCode,
                states: searchParams.states,
                keyword: searchParams.keyword,
              }"
              @resetSearch="handleResetSearch"
              @search="handleAdvancedSearch"
            />
          </div>
        </div>

        <!-- 过滤标签：接单、需求互动等业务流程标签 -->
        <div class="filter-tags"></div>
      </div>

      <!-- 订单列表区域 -->
      <div v-if="total > 0" class="order-list">
        <!-- 订单项循环渲染 -->
        <div v-for="(order, index) in orderList" :key="order.contactUserCode" class="order-item">
          <!-- 订单左侧：订单基本信息 -->
          <div class="order-item-left">
            <div class="order-header">
              <a-tooltip>
                <template #title>
                  <div>节点：{{ ProcessNode.ofType(order.processNode)?.desc }}</div>
                  <div>状态：{{ OrderListConstant.ofType(order.state)?.desc }}</div>
                </template>
                <span
                  :style="'margin-left: 14px;background:' + ProcessNode.ofType(order.processNode)?.color"
                  class="tag"
                  >{{ OrderListConstant.ofType(order.state)?.desc }}</span
                >
              </a-tooltip>
              <span v-if="order.state == 1410" style="margin-left: 14px; color: red">{{
                order.state == 1410 ? '竞价流标' : ''
              }}</span>
              <span v-if="getRatio(order)" style="margin-left: 14px" v-html="getRatio(order)"></span>
              <span v-if="judgeAuthority(order, 'time')" class="process-time">
                <img src="@/assets/image/orderList/dealTime.png" width="15" />
                <span>处理时长：{{ dealTimeMap[order.mainCode] }}</span>
                <img
                  v-if="order.isUrgent"
                  src="@/assets/image/orderList/urgent.png"
                  style="margin-left: 10px"
                  width="20"
                />
              </span>
              <span v-show="false" class="capacity">
                <img class="icon02" src="@/assets/image/orderList/personDeal.png" width="15" />
                <img class="icon12" src="@/assets/image/orderList/icon12.png" width="15" />{{
                  order.operatorName
                }}审批中</span
              >
              <span v-show="showTip(order)" class="capacity1">
                <img src="@/assets/image/orderList/warn.png" width="15" />所有方案已经互动完成，将于{{
                  showTip(order)
                }}后自动终止互动。</span
              >
              <span
                v-show="
                  [110, 120, 310, 320, 620, 910].includes(order.state) &&
                  (order.processNode === order.reverseProcessNode ||
                    order.processNode === order.reverseAfterProcessNode)
                "
                class="capacity2"
              >
                <!-- <img src="@/assets/image/orderList/warn.png" width="15" /> -->
                <a-tooltip>
                  <template #title>{{ order.finalReverseReason }}</template>
                  原因：{{ order.finalReverseReason }}
                </a-tooltip>
              </span>

              <span v-if="isTestForCiCi">
                <a-tooltip>
                  <template #title>
                    <div>10: '顾问待确认', // 发起议价确认中</div>
                    <div>20: '顾问驳回', // 发起议价驳回</div>
                    <div>30: '待服务商议价', // 议价提报中</div>
                    <div>40: '用户待确认', // 议价确认中</div>
                    <div>50: '议价完成'</div>
                    <div>60: '议价驳回'</div>
                  </template>
                </a-tooltip>
              </span>
            </div>
            <div class="order-header">
              <a-tooltip>
                <template #title> {{ order.miceName || '-' }}</template>
                <span class="title" style="margin-left: 14px">
                  {{ order.miceName || '-' }}
                </span>
              </a-tooltip>
              <a-tooltip>
                <template #title>{{ order.pdVerDescription }}</template>
                <span class="detail">{{ order.pdVerName }}</span>
              </a-tooltip>

              <a-divider type="vertical" />
              <span class="order-no" @click="getCopy(order.mainCode)"
                >{{ order.mainCode }}
                <img class="icon11" src="@/assets/image/orderList/icon11.png" width="15" />
                <img class="icon01" src="@/assets/image/orderList/file.png" width="15" />
              </span>
              <a-divider type="vertical" />
              <span v-show="order.personTotal" class="enterPerson">
                <img src="@/assets/image/orderList/enterPerson.png" width="15" />{{ order.personTotal }}人参会</span
              >
              <a-divider type="vertical" />
              <a-tooltip>
                <template #title>{{ order.cityNames }}</template>
                <span v-show="order.cityNames" class="location">{{ order.cityNames }}</span>
              </a-tooltip>
            </div>
            <div class="order-content">
              <div class="info-row">
                <span
                  ><i class="label">会议日期：</i
                  >{{
                    order.startDate
                      ? order.startDate.split(' ')[0].replaceAll('-', '/') +
                        '-' +
                        order.endDate.split(' ')[0].replaceAll('-', '/')
                      : '-'
                  }}</span
                >
                <span v-show="order.operatorName"
                  ><i class="label">经办人：</i>{{ order.operatorName }}{{ order.operatorName ? '/' : ''
                  }}{{ order.operatorCode }}

                  <a-tooltip placement="top">
                    <template #title>
                      <span v-show="order.operatorPhone">{{ order.operatorPhone }}</span>
                    </template>
                    <img
                      v-show="order.operatorPhone"
                      src="@/assets/image/orderList/personDeal.png"
                      style="margin-left: 5px; cursor: pointer"
                      width="15"
                      @click="getCopy(order.operatorPhone)"
                    />
                  </a-tooltip>
                </span>
                <span
                  ><i class="label">会议顾问：</i
                  >{{ order.consultantUserName ? order.consultantUserName + '/' + order.consultantUserCode : '-' }}
                  <a-tooltip placement="top">
                    <template #title>
                      <span>{{ order.consultantUserPhone }}</span>
                    </template>
                    <img
                      v-show="order.consultantUserPhone"
                      src="@/assets/image/orderList/personDeal.png"
                      style="margin-left: 5px; cursor: pointer"
                      width="15"
                      @click="getCopy(order.consultantUserPhone)"
                    /> </a-tooltip
                ></span>
              </div>
              <div class="info-row">
                <span><i class="label">方案截止：</i>{{ order.schemeDeadline || '-' }}</span>
                <span><i class="label">竞价截止：</i>{{ order.biddingDeadline || '-' }}</span>
              </div>

              <div class="info-row">
                <div class="row">
                  <i class="label">中标服务商：</i>
                  <div v-show="order?.winTheBidMerchants?.length === 0" class="rowDetail">-</div>
                  <div v-show="order?.winTheBidMerchants?.length > 0" class="rowDetail">
                    <a-popover placement="top">
                      <template #content>
                        <p v-for="(item, idx) in order.winTheBidMerchants" :key="idx" class="p1">
                          {{ idx + 1 + '、' + item.merchantName }}
                        </p>
                      </template>
                      <template #title>
                        <span class="title1">{{ '共有' + order.winTheBidMerchants?.length + '家中标服务商' }}</span>
                      </template>
                      <div :style="props.type == 'user' ? 'max-width:12vw' : ''" class="detail5">
                        <span>{{ getCompany(order.winTheBidMerchants) }}</span>
                      </div>
                      <div class="detail2">
                        {{ order.winTheBidMerchants?.length }}
                      </div>
                    </a-popover>
                  </div>
                </div>
                <div class="row">
                  <i class="label">服务商对接人：</i>
                  <div v-show="order.winTheBidMerchants?.length === 0" class="rowDetail">-</div>
                  <div v-show="order.winTheBidMerchants?.length > 0" class="rowDetail">
                    <a-popover placement="top">
                      <template #content>
                        <p v-for="(item, idx) in order.winTheBidMerchants" :key="idx" class="p1">
                          {{ idx + 1 + '、' + item.merchantName + ' - ' + item.contactMerchantName }}
                        </p>
                      </template>
                      <template #title>
                        <span class="title1">
                          {{ '共有' + order.winTheBidMerchants?.length + '家中标服务商对接人' }}
                        </span>
                      </template>
                      <div class="detail3">{{ order.winTheBidMerchants?.length }}</div>
                      {{ getContact(order.winTheBidMerchants) }}
                    </a-popover>
                  </div>
                </div>
                <div class="row">
                  <i class="label">中标总金额：</i
                  ><span>{{
                    order?.winTheBidMerchants?.length > 0 ? order.winTheBidMerchants[0]?.winTheBidPrice + '元' : '-'
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 订单右侧：操作按钮区域 -->
          <div v-show="!route.query.username" class="order-item-right">
            <Button
              v-show="judgeAuthority(order) && showBtn(order) !== ''"
              class="primary"
              type="primary"
              @click="btnJump(order)"
            >
              <img src="@/assets/image/orderList/btn1.png" width="15" />
              {{ showBtn(order) }}
            </Button>

            <Button class="plain" @click="showDetailBtn(order, 'view')">
              <img src="@/assets/image/orderList/btn2.png" width="15" />
              查看详情
            </Button>

            <a-dropdown :trigger="['click']">
              <Button class="plain">
                <img src="@/assets/image/orderList/btn3.png" width="15" />
                更多操作
              </Button>
              <template #overlay>
                <a-menu>
                  <a-menu-item
                    v-if="
                      ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(order.processNode) &&
                      [1, 2, 5].includes(merchantType) &&
                      [30].includes(order.finalPriceChangeState) &&
                      !order.isChangeApply
                    "
                    style="text-align: center"
                    @click="schemeBargainingBtn(order)"
                  >
                    <a href="javascript:">议价提报</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="
                      ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(order.processNode) &&
                      [1, 2, 5].includes(merchantType)
                    "
                    style="text-align: center"
                    @click="schemeBargainingViewBtn(order)"
                  >
                    <a href="javascript:">议价记录查看</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="
                      ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(order.processNode) &&
                      [1, 2, 5].includes(merchantType)
                    "
                    style="text-align: center"
                    @click="schemeChangeViewBtn(order)"
                  >
                    <a href="javascript:">方案变更</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="merchantType == 4 && !order.shipmentStatus"
                    style="text-align: center"
                    @click="moreBtn(order, 'change')"
                  >
                    <a href="javascript:">修改礼品明细</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="
                      merchantType == 4 &&
                      !order.orderStatus &&
                      ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(order.processNode)
                    "
                    style="text-align: center"
                    @click="presentOrder(order)"
                  >
                    <a href="javascript:">礼品下单</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="merchantType == 4 && !order.shipmentStatus && order.confirmStatus == 1"
                    style="text-align: center"
                    @click="openGiftDeliveryModal(order)"
                  >
                    <a href="javascript:">礼品发货</a>
                  </a-menu-item>
                  <a-menu-item
                    v-if="order.shipmentStatus"
                    style="text-align: center"
                    @click="openGiftDeliveryInfoModal(order)"
                  >
                    <a href="javascript:">发货信息查看</a>
                  </a-menu-item>

                  <a-modal
                    v-model:open="cancelSchemeModal"
                    title="撤回重新发布"
                    @cancel="cancelSchemeRemark = ''"
                    @ok="cancelScheme"
                  >
                    <p>请输入撤回原因！</p>
                    <a-textarea
                      v-model:value="cancelSchemeRemark"
                      :maxLength="200"
                      :rows="4"
                      placeholder="请输入撤回原因"
                    />
                  </a-modal>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
        <!-- 分页 -->
        <div class="pagination">
          <Pagination
            v-model:current="searchParams.pageNum"
            v-model:pageSize="searchParams.pageSize"
            :show-quick-jumper="true"
            :show-size-changer="true"
            :show-total="(total: number) => `共 ${total} 条`"
            :total="total"
            @change="handlePageChange"
          />
        </div>
      </div>
      <a-empty v-if="total === 0" />

      <!-- 审批列表弹窗 -->
      <a-modal
        :footer="null"
        :open="approvalModalVisible"
        destroyOnClose
        title="审批列表"
        width="700px"
        @cancel="closeApprovalModal"
      >
        <a-table :columns="columns" :data-source="approvalList" :pagination="false" size="middle">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a @click="viewApprovalDetail(record)">查看</a>
              <a v-show="record.state === '10'" style="margin-left: 8px" @click="cancelApproval(record)">撤销</a>
            </template>
            <template v-if="column.key === 'state'">
              {{ ApproveListConstant.ofType(record.state).desc }}
            </template>
          </template>
        </a-table>
      </a-modal>

      <!-- 费用支付 - 弹窗 -->
      <a-modal :open="feesPayShow" destroyOnClose title="费用支付" width="700px" @cancel="closeApprovalModal">
        <div class="demand_pay_fees mt20">
          <div class="demand_pay_fees_label">您的中标方案金额：</div>
          <div class="demand_pay_fees_value">{{ formatNumberThousands(payWinTheBidTotalPrices) + ' 元' }}</div>
        </div>
        <div v-if="platformFeeRate" class="demand_pay_fees mt12">
          <div class="demand_pay_fees_label">用户平台服务费：</div>
          <div class="demand_pay_fees_value">
            {{ formatNumberThousands(platformFee) + ' 元' }}
          </div>
        </div>
        <div v-if="platformFeeRate" class="demand_pay_fees mt12">
          <div class="demand_pay_fees_label">合计：</div>
          <div class="demand_pay_fees_value">
            {{ formatNumberThousands(payWinTheBidTotalPrices + platformFee) + ' 元' }}
          </div>
        </div>
        <template #footer>
          <a-button key="submit" type="primary" @click="PayHandleOk">确定</a-button>
        </template>
      </a-modal>
      <a-modal v-model:open="finishModal" title="结束提报" @cancel="finishRemark = ''" @ok="onOk">
        <p>请输入提前结束原因，结束后供应商将无法提报方案！</p>
        <a-textarea v-model:value="finishRemark" :maxLength="200" :rows="4" placeholder="请输入驳回原因" />
      </a-modal>

      <a-modal v-model:open="showBidPushed" :footer="null" title="竞价列表" width="60%">
        <a-table :columns="bidedColumn" :data-source="bidedData" :pagination="false" bordered style="margin-top: 10px">
          <template #bodyCell="{ text, record, index, column }">
            <div v-if="column.dataIndex === 'scheme'">
              <div v-for="item in record.scheme">{{ item.bidMerchantName }}</div>
            </div>
            <div v-else>{{ record[column.dataIndex] }}</div>
          </template>
        </a-table>
        <div class="footer">
          <a-button
            type="primary"
            @click="
              () => {
                router.push({ path: '/bidman/orderList/index', query: { status: '0' } })
                showBidPushed = false
                isCloseLastTab = true
              }
            "
            >确定
          </a-button>
        </div>
      </a-modal>
      <a-modal
        :open="changeSchemeRecord"
        destroyOnClose
        title="变更记录"
        width="1000px"
        @cancel="changeSchemeRecord = false"
      >
        <a-spin :spinning="bargainingLoading">
          <a-table
            :columns="priceChangeColumnsScheme"
            :data-source="priceChangeListScheme"
            :pagination="false"
            :row-key="(record: { id: string }) => record.id"
            bordered
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <span v-html="record[column.dataIndex]"></span>
            </template>
          </a-table>
        </a-spin>
        <template #footer>
          <a-button v-if="isCanPriceChange" :loading="loadingModal" type="primary" @click="schemeChangeBtn"
            >方案变更
          </a-button>
        </template>
      </a-modal>
      <!-- 礼品发货弹窗 -->
      <a-modal
        v-model:open="giftDeliveryModalVisible"
        title="填写物流单号"
        @cancel="giftDeliveryModalVisible = false"
        @ok="submitGiftDelivery"
      >
        <div
          v-for="(num, idx) in giftDeliveryTrackingNumbers"
          :key="idx"
          style="display: flex; align-items: center; margin-bottom: 8px"
        >
          <a-input
            v-model:value="giftDeliveryTrackingNumbers[idx]"
            placeholder="请输入物流单号"
            style="flex: 1"
            @input="(e) => updateGiftDeliveryTrackingNumber(idx, e.target.value)"
          />
          <!-- 只在最后一个输入框后显示添加按钮 -->
          <a-button v-if="idx === giftDeliveryTrackingNumbers.length - 1" type="link" @click="addGiftDeliveryInput">
            <img alt="" src="@/assets/image/orderList/add.png" width="20" />
          </a-button>
          <!-- 每个输入框后都显示删除按钮，只有一个时禁用 -->
          <a-button
            :disabled="giftDeliveryTrackingNumbers.length === 1"
            type="link"
            @click="removeGiftDeliveryInput(idx)"
          >
            <img alt="" src="@/assets/image/orderList/delete.png" width="20" />
          </a-button>
        </div>
      </a-modal>

      <!-- 发货信息弹窗 -->
      <a-modal
        v-model:open="giftDeliveryInfoModalVisible"
        :footer="null"
        title="发货信息"
        @cancel="giftDeliveryInfoModalVisible = false"
      >
        <a-table
          :columns="giftDeliveryInfoColumns"
          :data-source="giftDeliveryInfoList"
          :pagination="false"
          row-key="trackingNumber"
        />
      </a-modal>

      <!-- 方案议价 - 弹窗 -->
      <a-modal :open="bargainingShow" destroyOnClose title="方案议价" width="1000px" @cancel="closeBargainingModal">
        <a-spin :spinning="bargainingLoading">
          <a-table
            :columns="priceChangeColumns"
            :data-source="priceChangeList"
            :pagination="false"
            :row-key="(record: { id: string }) => record.id"
            bordered
            size="small"
          >
          </a-table>
        </a-spin>

        <template #footer>
          <a-button @click="closeBargainingModal">关闭</a-button>
        </template>
      </a-modal>
    </div>
  </a-spin>
</template>

<style lang="less" scoped>
.container {
  // padding-top: 50px;
  position: relative;
  // 容器样式
  background: #f5f5f5;
  min-height: 100vh;
  overflow: auto !important;
  min-width: 1100px;
}

.top-section {
  margin-left: 20px;
  position: fixed;
  // top: 0;
  z-index: 999;
  // 顶部区域样式，包含状态栏和搜索框
  background: white;
  padding: 14px 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.ellipsis {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本 */
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-bar {
  margin-left: 10px;
  // 状态切换栏样式，显示全部订单、待处理等状态
  display: flex;
  // gap: 8px;
  :first-child {
    border-radius: 4px 0 0 4px;
  }

  :last-child {
    border-radius: 0 4px 4px 0;
  }

  .status-item {
    white-space: nowrap;
    padding: 6px 12px 6px 16px;
    cursor: pointer;
    // border-right: 1px solid #e7e7e7;
    margin-right: 4px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    color: #666;
    font-size: 14px;
    background: #f2f3f5;

    &.active {
      .order-count {
        border: none;
        width: 20px;
      }

      border-radius: 4px;
      // margin: 0 4px;
      background: #1868db;
      color: white;
    }

    .count {
      margin-left: 4px;
      background: #f0f2f5;
      color: #666;
      padding: 0 6px;
      border-radius: 10px;
      font-size: 14px;
      line-height: 16px;

      .active & {
        background: rgba(255, 255, 255, 0.3);
        color: white;
      }
    }
  }
}

.filter-tags {
  // 过滤标签样式，包含接单、需求互动等标签
  display: flex;
  gap: 8px;
  flex-wrap: wrap;

  .tag {
    padding: 3px 8px;
    // background: #f7f8fa;
    border-radius: 2px;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;

    &.active {
      // background: #e6f4ff;
      color: #1677ff;

      .count {
        color: #ffffff;
      }
    }

    .count {
      background: #1677ff;
      padding: 0 4px;
      border-radius: 10px;
      color: #ffffff;
      font-size: 14px;
    }
  }
}

.search-input {
  margin-right: 5px;
  // 搜索区域样式，包含下拉框和搜索框
  display: flex;
  gap: 8px;
  align-items: center;

  .search-label {
    white-space: nowrap;
    color: #333;
    font-size: 14px;
  }

  :deep(.ant-input) {
    width: 210px;
  }

  :deep(.ant-input-affix-wrapper) {
    width: 210px;
    height: 24px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    padding: 0 8px;
    font-size: 14px;

    &::placeholder {
      color: #999;
    }
  }

  :deep(.ant-btn) {
    height: 24px;
    padding: 0 8px;
    font-size: 14px;
    border-radius: 2px;
  }
}

.capacity {
  border-radius: 14px;
  border: 1px solid #e5e6e8;
  padding: 2px 8px;
  color: #101010;
  font-size: 14px;

  .icon12 {
    display: none;
  }
}

.capacity:hover {
  cursor: pointer;
  border: 1px solid #1868db;
  color: #1868db;

  .icon12 {
    display: inline-block;
  }

  .icon02 {
    display: none;
  }
}

.rowDetail {
  position: relative;
  display: inline-block;
}

.enterPerson {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  margin-right: 0px !important;
}

img {
  vertical-align: -10%;
  margin-right: 3px;
}

.capacity2 {
  vertical-align: -40%;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 500px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid rgba(255, 85, 51, 1);
  padding: 2px 8px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 85, 51, 1);
  text-align: left;
  font-style: normal;
}

.capacity1 {
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid rgba(24, 104, 219, 0.2);
  padding: 2px 8px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1868db;
  text-align: left;
  font-style: normal;
}

.title1 {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  text-align: left;
  font-style: normal;
}

.p1 {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  text-align: left;
  font-style: normal;
}

.row {
  background: #f7f8fc;
  white-space: nowrap;
  width: 30%;
  position: relative;
  display: inlnie-block;
}

.order-list {
  margin-top: 60px;
  // 订单列表区域
  padding: 14px;

  .order-item {
    // 单个订单项样式
    display: flex;
    justify-content: space-between;
    background: #ffffff;
    border-radius: 8px;
    padding: 20px 14px;
    height: 100%;
    margin-bottom: 24px;

    .order-item-left {
      // 订单左侧信息区域
      display: inline-block;
      width: 84%;
      align-items: center;
      gap: 14px;

      .order-header {
        // 订单头部，包含标题、编号等信息
        width: 100%;

        span {
          margin-right: 24px;
          white-space: nowrap;
        }

        white-space: nowrap;
        // display: flex;
        // justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .tag {
          vertical-align: -40%;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 108px;
          display: inline-block;
          padding: 4px 12px;
          // background: #1868db;
          border-radius: 4px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
          text-align: center;
          font-style: normal;
        }

        .title {
          vertical-align: -20%;
          overflow: hidden;
          display: inline-block;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 300px;
          font-weight: 500;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 700;
          font-size: 18px;
          color: #1d2129;
          line-height: 25px;
          text-align: left;
          font-style: normal;
        }

        .order-no {
          margin-right: 0px;
          font-family:
            PingFangSC,
            PingFang SC;
          position: relative;
          font-weight: 400;
          font-size: 14px;
          color: #86909c;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          cursor: pointer;

          .icon01 {
            display: inline-block;
          }

          .icon11 {
            display: none;
          }
        }

        .order-no:hover .icon11 {
          display: inline-block;
        }

        .order-no:hover .icon01 {
          display: none;
        }

        .location {
          vertical-align: -25%;
          display: inline-block;
          max-width: 120px;
          padding: 0 5px;
          background: #f2f3f5;
          border-radius: 2px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #1d2129;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .detail {
          background: #e3edfb;
          border-radius: 2px;
          padding: 2px 4px;
          cursor: pointer;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: #1868db;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          margin-right: 0px;
        }
      }

      .label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #86909c;
        line-height: 20px;
        text-align: right;
        font-style: normal;
      }

      .detail5 {
        vertical-align: -27%;
        display: inline-block;
        max-width: calc(14vw);
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .detail2 {
        display: inline-block;
        font-size: 14px;
        color: #1d2129;
        text-align: center;
        line-height: 16px;
        width: 16px;
        margin-left: 5px;
        height: 16px;
        // position: absolute;
        // bottom: 0px;
        // right: -15px;
        background: #c9cdd4;
        border-radius: 3px;
      }

      .detail3 {
        font-size: 14px;
        color: #1d2129;
        text-align: center;
        line-height: 16px;
        width: 16px;
        height: 16px;
        position: absolute;
        bottom: 0px;
        right: -20px;
        background: #c9cdd4;
        border-radius: 3px;
      }

      .order-content {
        background: #f7f8fc;
        border-radius: 2px;
        padding: 14px;
        // 订单内容，包含会议日期、经办人等详细信息
        .info-row {
          display: flex;
          gap: 32px;
          // justify-content: space-between;
          margin-bottom: 8px;
          color: #666;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }

          span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #1d2129;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            width: 30%;

            &:nth-child(2),
            &:nth-child(3) {
              .number {
                color: #1677ff;
              }
            }
          }
        }
      }
    }

    .process-time {
      line-height: 15px;
      width: 105px;
      white-space: nowrap;
      color: #101010;
      font-size: 14px;
    }

    .order-item-right {
      // 订单右侧操作区域，包含按钮和处理时长
      position: relative;
      display: flex;
      width: 15%;
      align-items: center;
      justify-content: space-around;
      gap: 8px;
      flex-direction: column;
      padding: 0 8px;
      padding-top: 15px;

      img {
        margin-right: 5px;
      }

      :deep(.ant-btn) {
        width: 168px;
        height: 39.82px;
        font-size: 14px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .primary {
        background: #1868db;
        border-radius: 4px;
      }

      .plain {
        border-radius: 4px;
        border: 1px solid #1868db;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        // font-size: 14px;
        color: #1868db;
        line-height: 20px;
        text-align: center;
        font-style: normal;
      }
    }
  }
}

.primary1 {
  background: #1868db;
  border-radius: 4px;
  border: none;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}

.plain1 {
  background: #e8f3ff;
  border: none;
  border-radius: 4px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1868db;
  line-height: 22px;
  text-align: right;
  font-style: normal;
}

// .detail4 {
//   display: inline-block;
//   width: 200px; /* 需要设置一个宽度 */
//   overflow: hidden;
//   white-space: nowrap;
//   text-overflow: ellipsis;
// }
.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
}

.order-count {
  margin-left: 4px;
  margin-right: -10px;
  font-weight: bold;
  display: inline-block;
  width: 25px;
  border-radius: 0 !important;
}

:deep(.ant-divider) {
  margin: 0 16px;
  color: #e5e6eb;
  height: 20px;
}

:deep(.ant-empty) {
  margin-top: 200px;
}

.demand_pay_fees {
  font-family:
    PingFangSC,
    PingFang SC;
  font-size: 16px;
  line-height: 22px;
  display: flex;

  .demand_pay_fees_label {
    width: 160px;
    text-align: right;
    color: #1d2129;
  }

  .demand_pay_fees_value {
    text-indent: 8px;
    font-weight: 500;
    color: #1868db;
  }
}

.footer {
  margin-top: 20px;
  text-align: center;
}

:deep(.ant-dropdown-menu-item) {
  padding: 9px 12px !important;
}
</style>
